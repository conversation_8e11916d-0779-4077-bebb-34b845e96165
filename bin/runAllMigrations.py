import os
import subprocess
import sys

migrations_dir = os.path.join(os.path.dirname(__file__), '..', 'migrations')
migrations_dir = os.path.abspath(migrations_dir)

if not os.path.exists(migrations_dir):
    print(f"目录 {migrations_dir} 不存在")
    sys.exit(1)

sql_files = sorted([f for f in os.listdir(migrations_dir) if f.endswith('.sql')])

mysql_bin = r'C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe'
user = 'root'
password = 'root'
database = 'xy-os'

for sql_file in sql_files:
    sql_path = os.path.join(migrations_dir, sql_file)
    print(f"正在执行 {sql_path} ...")
    try:
        with open(sql_path, 'rb') as f:
            proc = subprocess.run(
                [mysql_bin,  '--default-character-set=utf8mb4', 
                 '-u', user, f'-p{password}', database],
                stdin=f,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=False
            )
        if proc.returncode == 0:
            print(f"{sql_file} 执行成功")
        else:
            print(f"{sql_file} 执行失败")
            print(proc.stderr.decode('utf-8', errors='ignore'))
            sys.exit(1)
    except Exception as e:
        print(f"{sql_file} 执行异常: {e}")
        sys.exit(1)

print("all done")