# 统一建模语言

| 中文   | 英语                  | 说明 |
|------|---------------------|----|
| 物料分类 | MaterialCategory    |    |
| 物料档案 | MaterialMasterData  |    |
| 库存   | Inventory           |    |
| 安全库存 | SafetyInventory     |    |        
| 最大库存 | MaximumInventory    |    |         
| 最小库存 | MinimumInventory    |    |
| 库存管理 | InventoryManagement |    |       
| 包装物  | PackagingMaterial   |    |
| 包装产品 | PackagedProduct     |    |
| 计量单位 | UOM,uintOfMeasure   |    |

# 接口文档

## 物料分类

[接口文档](http://192.168.0.61:3000/project/19/interface/api/cat_381 )

## 物料档案

[物料档案](http://192.168.0.61:3000/project/19/interface/api/cat_383)

## 权限的code码

| 模块   | 接口名称       | 权限code                          |
|------|------------|---------------------------------|
| 物料分类 | 创建物料分类     | erp:material-category:create    |
| 物料分类 | 更新物料分类     | erp:material-category:update    |
| 物料分类 | 删除物料分类     | erp:material-category:delete    |
| 物料分类 | 查看物料分类     | erp:material-category:query     |
| 物料分类 | 物料分类列表     | erp:material-category:query     |
| 物料分类 | 停用/启用 物料分类 | erp:material-category:status    |
| 物料档案 | 创建物料档案     | erp:material-master-data:create |
| 物料档案 | 更新物料档案     | erp:material-master-data:update |
| 物料档案 | 删除物料档案     | erp:material-master-data:delete |
| 物料档案 | 查看物料档案     | erp:material-master-data:query  |
| 物料档案 | 物料档案分页列表   | erp:material-master-data:query  |
| 物料档案 | 停用/启用 物料档案 | erp:material-master-data:status |

## sql

```sql
CREATE TABLE `erp_material_category`
(
    `id`          bigint                                                        NOT NULL AUTO_INCREMENT COMMENT 'id',
    `parent_id`   bigint                                                        NOT NULL COMMENT '父级编号',
    `name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
    `code`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '编码',
    `sort`        int                                                           NOT NULL DEFAULT 0 COMMENT '排序',
    `level`       int                                                           NOT NULL DEFAULT 0 COMMENT '层级',
    `status`      int                                                           NOT NULL DEFAULT 0 COMMENT '状态（停用/启用）',
    `creator`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT '' COMMENT '创建者',
    `create_time` datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT '' COMMENT '更新者',
    `update_time` datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`     bit(1)                                                        NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`   bigint                                                        NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物料分类';

CREATE TABLE `erp_material_master_data`
(
    `id`                         bigint                                                        NOT NULL AUTO_INCREMENT COMMENT 'id',
    `category_id`                bigint                                                        NOT NULL COMMENT '物料分类',
    `code`                       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '编码',
    `name`                       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
    `unit_of_measure`            varchar(64)                                                   NOT NULL COMMENT '单位',
    `specification`              varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '规格型号',
    `packaging`                  bit(1)                                                        NOT NULL DEFAULT b'0' COMMENT '是否包装物',
    `unique_code_managed`        bit(1) NULL COMMENT '是否唯一码管理',
    `packaging_uom`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '包装单位',
    `packaging_quantity`         decimal(10, 2) NULL COMMENT '包装数量',
    `packaged_product`           bit(1)                                                        NOT NULL DEFAULT b'0' COMMENT '是否包装产品',
    `inventory_control`          bit(1)                                                        NOT NULL DEFAULT b'0' COMMENT '是否开启库存控制',
    -- 是否开启安全库存控制
    `safety_inventory_control`   bit(1) NULL COMMENT '是否开启安全库存控制',
    `safety_inventory_quantity`  decimal(10, 2) NULL COMMENT '安全库存数量',
    -- 是否开启最小库存控制
    `minimum_inventory_control`  bit(1) NULL COMMENT '是否开启最小库存控制',
    `minimum_inventory_quantity` decimal(10, 2) NULL COMMENT '最小库存数量',
    -- 是否开启最大库存控制
    `maximum_inventory_control`  bit(1) NULL COMMENT '是否开启最大库存控制',
    `maximum_inventory_quantity` decimal(10, 2) NULL COMMENT '最大库存数量',
    `status`                     int                                                           NOT NULL DEFAULT 0 COMMENT '状态（停用/启用）',
    `creator`                    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT '' COMMENT '创建者',
    `create_time`                datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`                    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT '' COMMENT '更新者',
    `update_time`                datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`                    bit(1)                                                        NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`                  bigint                                                        NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物料档案';




```











