package cn.iocoder.yudao.module.infra;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.controller.admin.codegen.vo.CodegenCreateListReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.codegen.vo.CodegenUpdateReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.codegen.vo.column.CodegenColumnSaveReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.codegen.vo.table.CodegenTableSaveReqVO;
import cn.iocoder.yudao.module.infra.dal.dataobject.codegen.CodegenColumnDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.codegen.CodegenTableDO;
import cn.iocoder.yudao.module.infra.enums.codegen.CodegenTemplateTypeEnum;
import cn.iocoder.yudao.module.infra.service.codegen.CodegenService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@SpringBootApplication
public class CodegenMain {


    private static final Long DATASOURCE_CONFIG_ID = 0L;
    private static final Long USER_ID = 1L;

    /**
     * 修改为自己的nacos命令空间
     */
    private static final String SPRING_CLOUD_NACOS_NAMESPACE = "dev-yangshuai";


    /**
     * 修改为自己的的路基
     */
    private static final String PROJECT_ROOT_PATH = "D:\\IdeaProjects\\xy-os";

//    /**
//     * 修改包名
//     */
//    private static final String packageName = "abc";
//
//    /**
//     * 修改为需要生成的所有表的集合。这里只管写下当前业务的所有表，包括主表和子表
//     */
//    private static final String[] TABLE_NAMES = {"erp_stock"};
//    private static final String masterTable = "erp_stock_detail";

    /**
     * 模板信息
     *
     * @see CodegenTemplateTypeEnum
     */
    private static final Integer templateType = CodegenTemplateTypeEnum.ONE.getType();
    private static final String MASTER_SUB_TABLE_CONFIG = "{'masterTable':'erp_inventory_bill', 'packageName':'abc','templateType':11" +
            ",'remark':'{\"isDocument\":true,\"documentType\":\"purchaseReturned\"}'" +
            ",'subTableList':[" +
            "  {'tableName':'erp_inventory_bill_item','joinColumn':'bill_id','subJoinMany':true}" +
            "]}";

//    private static final Integer templateType = CodegenTemplateTypeEnum.ONE.getType();


    public static void main(String[] args) {
        System.setProperty("spring.cloud.nacos.namespace", SPRING_CLOUD_NACOS_NAMESPACE);
        System.setProperty("spring.cloud.nacos.server.instance.tag", "dev-local");
        System.setProperty("spring.cloud.nacos.discovery.ip", "localhost");
        System.setProperty("server.port", "1888");
        ConfigurableApplicationContext context = SpringApplication.run(CodegenMain.class, args);
        try {
            TableConfig tableConfig = bulidTableConfig();
            //先删除记录
            CodegenService codegenService = context.getBean(CodegenService.class);
            for (String tableName : tableConfig.getTables()) {
                CodegenTableDO codegenTableByName = codegenService.getCodegenTableByName(tableName);
                if (Objects.nonNull(codegenTableByName)) {
                    codegenService.deleteCodegen(codegenTableByName.getId());
                }
            }

            //参考页面上的操作，通过main函数生成代码
//            1,导入表
            CodegenCreateListReqVO reqVO = new CodegenCreateListReqVO();
            reqVO.setDataSourceConfigId(DATASOURCE_CONFIG_ID);
            reqVO.setTableNames(tableConfig.getTables());
            List<Long> codegenList = codegenService.createCodegenList(USER_ID, reqVO);
            System.out.printf("codegen list ids{}", codegenList);

            //1.1查询主表的信息
            CodegenTableDO masterTable = codegenService.getCodegenTableByName(tableConfig.getMasterTable());

            //2，如果有子表，先设置子表的生成信息
            if (CollUtil.isNotEmpty(tableConfig.getSubTableList())) {
                for (SubTable subTable : tableConfig.getSubTableList()) {
                    //查找子表的信息
                    CodegenTableDO codegenTableDO = codegenService.getCodegenTableByName(subTable.getTableName());
                    //查找关联字段的Id
                    CodegenColumnDO joinColumn = codegenService.selectOneByTableIdAndColumnName(codegenTableDO.getId(), subTable.getJoinColumn());

                    //设置生成信息
                    CodegenUpdateReqVO updateReqVO = new CodegenUpdateReqVO();
                    CodegenTableSaveReqVO table = new CodegenTableSaveReqVO();
                    table.setId(codegenTableDO.getId());
                    table.setBusinessName(tableConfig.packageName);
                    table.setTemplateType(subTable.templateType);

                    table.setMasterTableId(masterTable.getId());
                    table.setSubJoinColumnId(joinColumn.getId());
                    table.setSubJoinMany(subTable.subJoinMany);

                    updateReqVO.setColumns(BeanUtils.toBean(codegenService.getCodegenColumnListByTableId(table.getId()), CodegenColumnSaveReqVO.class));
                    updateReqVO.setTable(table);
                    codegenService.updateCodegen(updateReqVO);
                }
            }
            //3,更新主表
            //设置生成信息
            CodegenUpdateReqVO updateReqVO = new CodegenUpdateReqVO();
            CodegenTableSaveReqVO table = new CodegenTableSaveReqVO();
            table.setId(masterTable.getId());
            table.setBusinessName(tableConfig.packageName);
            table.setTemplateType(tableConfig.getTemplateType());
            // 设置备注信息
            if (tableConfig.getRemark() != null) {
                table.setRemark(tableConfig.getRemark());
            }
            updateReqVO.setTable(table);
            codegenService.updateCodegen(updateReqVO);


            Map<String, String> codes = codegenService.generationCodes(masterTable.getId());
            codes.forEach((k, v) -> {
                String fullPath = PROJECT_ROOT_PATH + File.separator + k;
                File file = FileUtil.writeUtf8String(v, fullPath);
                System.out.println("生成文件：" + file.getAbsoluteFile() + "，文件名：" + file.getName());
            });

            // 构建 zip 包
//            String[] paths = codes.keySet().toArray(new String[0]);
//            ByteArrayInputStream[] ins = codes.values().stream().map(IoUtil::toUtf8Stream).toArray(ByteArrayInputStream[]::new);
//            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//            ZipUtil.zip(outputStream, paths, ins);
            // 输出


        } finally {
            // 关闭前等待异步任务完成（可选：添加 sleep）
            context.close(); // 手动关闭上下文
        }
    }

    private static TableConfig bulidTableConfig() {
        return JSONUtil.toBean(MASTER_SUB_TABLE_CONFIG, TableConfig.class);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class TableConfig {
        private String masterTable;
        private String packageName;

        /**
         * 修改生成的模板类型
         *
         * @see CodegenTemplateTypeEnum
         */
        private Integer templateType;

        /**
         * 备注信息，用于存储额外的配置，如文档类型等
         * 格式：JSON字符串，例如：{"isDocument":true,"documentType":"shipping"}
         */
        private String remark;

        private List<SubTable> subTableList;


        public List<String> getTables() {
            List<String> tables = new ArrayList<>();
            tables.add(masterTable);
            if (CollUtil.isNotEmpty(subTableList)) {
                tables.addAll(subTableList.stream().map(SubTable::getTableName).toList());
            }
            return tables;
        }
    }

    @Data
    static class SubTable {
        private Integer templateType = CodegenTemplateTypeEnum.SUB.getType();
        private String tableName;
        private String joinColumn;
        //是否一堆多，一对多为true，一对一为false
        private Boolean subJoinMany;
    }

}
