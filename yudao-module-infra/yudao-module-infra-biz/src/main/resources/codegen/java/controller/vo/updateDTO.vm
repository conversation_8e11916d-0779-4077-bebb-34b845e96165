package ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import ${jakartaPackage}.validation.constraints.*;
## 处理 BigDecimal 字段的引入
#foreach ($column in $columns)
    #if (${column.javaType} == "BigDecimal")
    import java.math.BigDecimal;
        #break
    #end
#end
## 处理 LocalDateTime 字段的引入
#foreach ($column in $columns)
    #if ((${column.createOperation} || ${column.updateOperation}) && ${column.javaType} == "LocalDateTime")
    import org.springframework.format.annotation.DateTimeFormat;
    import java.time.LocalDateTime;
        #break
    #end
#end
## 特殊：主子表专属逻辑
###foreach ($subTable in $subTables)
##import ${basePackage}.module.${subTable.moduleName}.dal.dataobject.${subTable.businessName}.${subTable.className}DO;
###end

@Schema(description = "${sceneEnum.name} - ${table.classComment}修改 DTO")
@Data
public class Update${sceneEnum.prefixClass}${table.className}DTO extends Create${sceneEnum.prefixClass}${table.className}DTO {

}