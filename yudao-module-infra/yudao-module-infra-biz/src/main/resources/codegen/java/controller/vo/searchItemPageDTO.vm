package ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo;

import ${PageParamClassName};
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
## 处理 LocalDateTime 字段的引入
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDate;

import static ${DateUtilsClassName}.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "${sceneEnum.name} - ${table.classComment}明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchItemPageDTO extends PageParam {

    @Schema(description = "${table.classComment}ID", example = "1024")
    private ${primaryColumn.javaType} ${classNameVar}Id;

    @Schema(description = "单据编号", example = "DH202401001")
    private String code;

    @Schema(description = "仓库ID", example = "1")
    private Long warehouseId;

    @Schema(description = "单据状态", example = "1")
    private Integer documentStatus;

    @Schema(description = "物料编码", example = "M001")
    private String materialCode;

    @Schema(description = "物料名称", example = "钢材")
    private String materialName;

    @Schema(description = "批次号", example = "B001")
    private String batchNo;

    @Schema(description = "唯一码", example = "U001")
    private String uniqueCode;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] createTime;

}
