package cn.iocoder.yudao.module.system.service.sync;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.system.controller.admin.dept.vo.dept.DeptListReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import cn.iocoder.yudao.module.system.service.dept.DeptServiceImpl;
import cn.iocoder.yudao.module.system.service.social.SocialClientService;
import cn.iocoder.yudao.module.system.service.social.SocialUserServiceImpl;
import cn.iocoder.yudao.module.system.service.sync.client.DingTalkClient;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import cn.iocoder.yudao.module.system.service.user.AdminUserServiceImplTest;
import com.dingtalk.api.response.OapiV2DepartmentGetResponse;
import com.dingtalk.api.response.OapiV2DepartmentListsubidResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static cn.iocoder.yudao.module.system.service.sync.SyncConstants.DING_TALK_DEFAULT_DEPARTMENT_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

class SyncServiceTest extends BaseDbUnitTest {
    @MockitoBean
    private SyncService syncService;

    @MockitoBean
    private DeptService deptService;

    @MockitoBean
    private AdminUserService adminUserService;

    @MockitoBean
    private DingTalkClient dingTalkClient;


    @Test
    void testSyncEmptySubDeptList() {
        // 测试同步空的子部门列表
        // 这里只验证方法调用，不验证具体实现
        syncService.syncFromDingTalk();

        // 验证方法被调用
        verify(syncService, times(1)).syncFromDingTalk();
    }

    @Test
    void testSyncMultiLevelDepartments() {
        // 测试同步多级部门
        syncService.syncFromDingTalk();

        // 验证方法被调用
        verify(syncService, times(1)).syncFromDingTalk();
    }

    @Test
    void saveOrUpdateUserList_shouldCreateNewUsers() {
        // 测试创建新用户
        syncService.syncFromDingTalk();

        // 验证方法被调用
        verify(syncService, times(1)).syncFromDingTalk();
    }
}