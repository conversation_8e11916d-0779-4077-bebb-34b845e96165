package cn.iocoder.yudao.module.system.service.sync.client;


import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.dingtalk.api.response.*;
import org.junit.jupiter.api.Test;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class DingTalkClientTest extends BaseDbUnitTest {

    @MockitoBean
    private RedisTemplate<String, String> redisTemplate;

    @MockitoBean
    private DingTalkClient dingTalkClient;


    @Test
    void testGetDepartmentList() {
        // 准备模拟数据
        OapiV2DepartmentListsubResponse.DeptBaseResponse mockDept = new OapiV2DepartmentListsubResponse.DeptBaseResponse();
        mockDept.setDeptId(130855438L);
        mockDept.setName("测试部门");

        // 配置模拟行为
        when(dingTalkClient.getDepartmentList(any())).thenReturn(List.of(mockDept));

        // 执行测试
        List<OapiV2DepartmentListsubResponse.DeptBaseResponse> deptBaseResponseList = this.dingTalkClient.getDepartmentList(130855438L);

        // 验证结果
        assert deptBaseResponseList.size() == 1;
        assert deptBaseResponseList.get(0).getDeptId().equals(130855438L);
    }

    @Test
    void testGetDepartment() {
        // 准备模拟数据
        OapiV2DepartmentGetResponse mockResponse = new OapiV2DepartmentGetResponse();

        // 配置模拟行为
        when(dingTalkClient.getDepartment(any())).thenReturn(mockResponse);

        // 执行测试
        OapiV2DepartmentGetResponse deptGetResponse = this.dingTalkClient.getDepartment(1L);

        // 验证结果
        assert deptGetResponse != null;
    }

    @Test
    void testGetUserByMobile() {
        // 准备模拟数据
        OapiV2UserGetbymobileResponse.UserGetByMobileResponse mockResponse = new OapiV2UserGetbymobileResponse.UserGetByMobileResponse();
        mockResponse.setUserid("mock_user_id");

        // 配置模拟行为
        when(dingTalkClient.getUserByMobile(any())).thenReturn(mockResponse);

        // 执行测试
        OapiV2UserGetbymobileResponse.UserGetByMobileResponse response = this.dingTalkClient.getUserByMobile("18989864117");

        // 验证结果
        assert response.getUserid().equals("mock_user_id");
    }

    @Test
    void testGetUser() {
        // 准备模拟数据
        OapiV2UserGetResponse.UserGetResponse mockUser = new OapiV2UserGetResponse.UserGetResponse();
        OapiV2UserGetResponse.DeptOrder mockDeptOrder = new OapiV2UserGetResponse.DeptOrder();
        mockDeptOrder.setDeptId(123L);
        mockUser.setDeptOrderList(List.of(mockDeptOrder));
        mockUser.setDeptIdList(List.of(123L));

        // 配置模拟行为
        when(dingTalkClient.getUser(any())).thenReturn(mockUser);

        // 执行测试
        OapiV2UserGetResponse.UserGetResponse user = this.dingTalkClient.getUser("17391510335865598");

        // 验证结果
        assert user.getDeptOrderList().size() == 1;
        assert user.getDeptIdList().size() == 1;
        assert user.getDeptIdList().get(0).equals(123L);
    }

    @Test
    void testGetUsers() {
        // 准备模拟数据
        OapiV2UserListResponse.ListUserResponse mockUser = new OapiV2UserListResponse.ListUserResponse();
        mockUser.setName("测试用户");

        // 配置模拟行为
        when(dingTalkClient.getUserList(any())).thenReturn(List.of(mockUser));

        // 执行测试
        List<OapiV2UserListResponse.ListUserResponse> userResponseList = this.dingTalkClient.getUserList(694114342L);

        // 验证结果
        assert userResponseList.size() == 1;
        assert userResponseList.get(0).getName().equals("测试用户");
    }
}