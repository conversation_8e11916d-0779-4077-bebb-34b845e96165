package cn.iocoder.yudao.module.system.job.sync;

import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.system.service.sync.SyncService;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;

@Component
public class DingTalkSyncJob {

    private static final Logger logger = LoggerFactory.getLogger(DingTalkSyncJob.class);
    private final AtomicInteger counts = new AtomicInteger();

    @Resource
    private SyncService syncService;

    @XxlJob("dingTalkSyncJob")
    @TenantJob
    public void execute() {
        logger.info("[execute][定时第 ({}) 次执行]", counts.incrementAndGet());
        this.syncService.syncFromDingTalk();
    }
}
