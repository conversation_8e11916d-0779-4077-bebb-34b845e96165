package cn.iocoder.yudao.module.erp.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * ERP 错误码枚举类
 * <p>
 * erp 系统，使用 1-030-000-000 段
 */
public interface ErrorCodeConstants {

    //============通用的错误提示吗 1-030-602-000===============================
    ErrorCode DOCUMENT_LOCK_FAILED = new ErrorCode(1_030_602_000, "单据获取锁失败，请稍后再试");
    ErrorCode ITEM_LOCK_FAILED = new ErrorCode(1_030_602_001, "单据获取锁失败，请稍后再试");
    ErrorCode HAS_OUTBOUND_NOT_SUPPORT_CANCEL_APPROVE = new ErrorCode(1_030_602_002, "单据存在出库记录，不支持反审核");
    // ========== ERP 物料分类 1-030-603-000 ==========
    ErrorCode MATERIAL_CATEGORY_NOT_EXISTS = new ErrorCode(1_030_603_000, "物料分类不存在");
    ErrorCode MATERIAL_CATEGORY_EXITS_CHILDREN = new ErrorCode(1_030_603_001, "存在子物料分类，无法删除");
    ErrorCode MATERIAL_CATEGORY_PARENT_NOT_EXITS = new ErrorCode(1_030_603_002, "父级物料分类不存在");
    ErrorCode MATERIAL_CATEGORY_PARENT_ERROR = new ErrorCode(1_030_603_003, "不能设置自己为父物料分类");
    ErrorCode MATERIAL_CATEGORY_PARENT_IS_CHILD = new ErrorCode(1_030_603_004, "不能设置自己的子分类为父分类");
    ErrorCode MATERIAL_CATEGORY_CODE_DUPLICATE = new ErrorCode(1_030_603_005, "已经存在该分类编码的物料分类");
    ErrorCode MATERIAL_CATEGORY_PARENT_STATUS_DISABLE = new ErrorCode(1_030_603_006, "父级物料分类状态必须是启用状态");
    ErrorCode MATERIAL_CATEGORY_STATUS_DISABLE = new ErrorCode(1_030_603_007, "物料分类的状态未启用，不能操作");
    ErrorCode MATERIAL_CATEGORY_EXITS_MATERIAL = new ErrorCode(1_030_603_008, "存在物料使用该分类，无法删除");
    ErrorCode MATERIAL_CATEGORY_IDS_NOT_EXISTS = new ErrorCode(1_030_603_009, "物料分类ids（{}）不存在");
    ErrorCode MATERIAL_CATEGORY_NOT_LEAF_NODE = new ErrorCode(1_030_603_010, "物料分类不是叶子节点，不能修改父节点");
    ErrorCode MATERIAL_CATEGORY_LEVEL_TOO_LONG = new ErrorCode(1_030_603_011, "物料分类层级不能超过5级");


    // ========== ERP 物料档案 1-030-604-000 ==========
    ErrorCode MATERIAL_INVENTORY_CONTROL_MASTER_SELECT = new ErrorCode(1_030_604_000, "库存控制已经开启，请至少选择一种库存控制");

    ErrorCode MATERIAL_INVENTORY_CONTROL_MASTER_VALUE = new ErrorCode(1_030_604_001, "库存控制已开启，请设置对应的数量");
    //最小库存必须小于安全库存
    ErrorCode MATERIAL_INVENTORY_MINIMUM_MASTER_SMALLER_THAN_SAFETY = new ErrorCode(1_030_604_002, "最小库存不能大于安全库存");
    //最小库存必须小于最大库存
    ErrorCode MATERIAL_INVENTORY_MINIMUM_MASTER_SMALLER_THAN_MAXIMUM = new ErrorCode(1_030_604_003, "最小库存不能大于最大库存");
    //安全库存必须大于最小库存
    ErrorCode MATERIAL_INVENTORY_SAFETY_MASTER_BIGGER_THAN_MINIMUM = new ErrorCode(1_030_604_004, "安全库存不能小于最小库存");
    //安全库存必须小于最大库存
    ErrorCode MATERIAL_INVENTORY_SAFETY_MASTER_SMALLER_THAN_MAXIMUM = new ErrorCode(1_030_604_005, "安全库存不能大于最大库存");
    //最大库存必须大于安全库存
    ErrorCode MATERIAL_INVENTORY_MAXIMUM_MASTER_BIGGER_THAN_SAFETY = new ErrorCode(1_030_604_006, "最大库存不能小于安全库存");
    //最大库存必须大于最小库存
    ErrorCode MATERIAL_INVENTORY_MAXIMUM_MASTER_BIGGER_THAN_MINIMUM = new ErrorCode(1_030_604_007, "最大库存不能小于最小库存");

    ErrorCode MATERIAL_MASTER_DATA_NOT_EXISTS = new ErrorCode(1_030_604_008, "物料档案不存在");
    ErrorCode MATERIAL_MASTER_DATA_CODE_DUPLICATE = new ErrorCode(1_030_604_009, "已经存在该编码的物料档案");
    ErrorCode MATERIAL_MASTER_DATA_PACKAGING_SETTING_ERROR = new ErrorCode(1_030_604_010, "物料是包装物时，请完整设置相关信息包括‘是否唯一码管理，包装单位，最大包装量’");

    ErrorCode MATERIAL_MASTER_DATA_LIST_NOT_EXISTS = new ErrorCode(1_030_604_011, "物料档案({})不存在");
    ErrorCode MATERIAL_MASTER_DATA_LIST_NOT_ENABLE = new ErrorCode(1_030_604_012, "物料档案({})未启用");
    ErrorCode MATERIAL_MASTER_DATA_LIST_NOT_ENABLE_OR_NOT_PACKAGED_PRODUCT = new ErrorCode(1_030_604_013, "物料档案({})未启用或不是包装物");

    // ========== ERP 客户档案 1-030-605-000 ==========

    ErrorCode CUSTOMER_NOT_EXISTS = new ErrorCode(1_030_605_000, "客户档案({})不存在");
    ErrorCode CUSTOMER_NOT_ENABLE = new ErrorCode(1_030_605_001, "客户档案({})未启用");
    ErrorCode CUSTOMER_CODE_DUPLICATE = new ErrorCode(1_030_605_002, "客户编号({})已存在");
    ErrorCode CUSTOMER_NAME_DUPLICATE = new ErrorCode(1_030_605_003, "客户名称({})已存在");
    ErrorCode CUSTOMER_RECEIVE_ADDRESS_NOT_EXISTS = new ErrorCode(1_030_605_004, "客户收货地址({})不存在");
    ErrorCode CUSTOMER_INVOICE_INFO_NOT_EXISTS = new ErrorCode(1_030_605_005, "客户发票信息({})不存在");
    ErrorCode CUSTOMER_NOT_UPDATABLE = new ErrorCode(1_030_605_006, "客户档案({})无法修改");
    ErrorCode CUSTOMER_SHORT_NAME_DUPLICATE = new ErrorCode(1_030_605_007, "客户简称({})已存在");
    ErrorCode CUSTOMER_INVOICE_INFO_EMPTY = new ErrorCode(1_030_605_008, "客户开票信息不能都为空");


    //============ ERP 仓库管理 1-030-606-000 ==========
    ErrorCode WAREHOUSE_CODE_DUPLICATE = new ErrorCode(1_030_606_000, "已经存在该分类编码的仓库");
    ErrorCode WAREHOUSE_NAME_DUPLICATE = new ErrorCode(1_030_606_001, "已经存在该仓库名称的仓库");
    ErrorCode WAREHOUSE_NOT_EXISTS = new ErrorCode(1_030_606_002, "仓库id({})不存在");
    ErrorCode WAREHOUSE_STORAGE_POSITION_PARENT_ERROR = new ErrorCode(1_030_606_003, "不能设置自己为上级库位");
    ErrorCode WAREHOUSE_STORAGE_POSITION_PARENT_NOT_EXITS = new ErrorCode(1_030_606_004, "上级库位不存在");
    ErrorCode WAREHOUSE_STORAGE_POSITION_PARENT_IS_CHILD = new ErrorCode(1_030_606_005, "不能设置自己的子库位为上级库位");
    ErrorCode WAREHOUSE_STORAGE_POSITION_NAME_DUPLICATE = new ErrorCode(1_030_606_006, "已经存在该名称（{}）的库位");

    ErrorCode WAREHOUSE_STORAGE_POSITION_CONTROL_NOT_OPEN = new ErrorCode(1_030_606_007, "仓库（{}）的库位管理未开启，请开启后再试");
    ErrorCode WAREHOUSE_NOT_HAS_THE_STORAGE_POSITION = new ErrorCode(1_030_606_008, "仓库（{}）不存在该库位（{}）");
    ErrorCode WAREHOUSE_STORAGE_POSITION_EXITS_CHILDREN = new ErrorCode(1_030_606_009, "该库位存在子库位，无法删除");
    ErrorCode WAREHOUSE_DISABLE = new ErrorCode(1_030_606_010, "库位id({})未启用，不能操作");
    ErrorCode WAREHOUSE_STORAGE_POSITION_LEVEL_TOO_LONG = new ErrorCode(1_030_606_011, "库位层级不能超过5级");

    ErrorCode STORAGE_POSITION_NOT_LEAF_NODE = new ErrorCode(1_030_606_012, "库位不是叶子节点，不能修改父节点");
    ErrorCode WAREHOUSE_REFER_MATERIAL_CATEGORY_EXITS = new ErrorCode(1_030_606_013, "有仓库已关联该物料分类，不能删除");
    ErrorCode WAREHOUSE_STATUS_DISABLE = new ErrorCode(1_030_606_014, "仓库({})，未启用");
    ErrorCode WAREHOUSE_NOT_SUPPORT_IN_TYPE = new ErrorCode(1_030_606_015, "仓库({})不支持入库类型({})");
    ErrorCode WAREHOUSE_STORAGE_POSITION_NOT_BELONG_WAREHOUSE = new ErrorCode(1_030_606_016, "仓库位置({})，不属于仓库（{}）未启用");
    ErrorCode WAREHOUSE_STORAGE_POSITION_NOT_ENABLE = new ErrorCode(1_030_606_017, "仓库位置({})，未启用");
    ErrorCode WAREHOUSE_STORAGE_POSITION_NOT_EXISTS = new ErrorCode(1_030_606_018, "仓库位置({})不存在");
    ErrorCode WAREHOUSE_STORAGE_POSITION_PARENT_DISABLE = new ErrorCode(1_030_606_019, "当前仓位的父节点未启用，不支持操作");
    ErrorCode WAREHOUSE_NOT_SUPPORT_OUT_TYPE = new ErrorCode(1_030_606_020, "仓库({})不支持出库类型({})");

    // ============ ERP 销售订单 1-030-607-000 ==========
    ErrorCode SALES_ORDER_NOT_EXISTS = new ErrorCode(1_030_607_000, "销售订单({})不存在");
    //仅状态为编制中、回退的单据可编辑
    ErrorCode SALES_ORDER_NOT_EDITABLE = new ErrorCode(1_030_607_001, "仅状态为编制中、回退的单据可编辑");
    //仅状态为审核通过才能进行反审核
    ErrorCode SALES_ORDER_NOT_CANCEL_APPROVE = new ErrorCode(1_030_607_002, "仅状态为审核通过才能进行反审核");
    //当前业务状态不允许进行操作
    ErrorCode SALES_ORDER_NOT_BUSINESS_STATUS_CHANGE = new ErrorCode(1_030_607_003, "当前业务状态不允许进行操作");

    ErrorCode SALES_ORDER_DELIVERY_DATE_ERROR = new ErrorCode(1_030_607_004, "交货日期不能早于订单日期");
    ErrorCode SALES_ORDER_CUSTOMER_ORDER_CODE_ERROR = new ErrorCode(1_030_607_005, "当前销售订单，客户订单号只能是0");
    ErrorCode SALES_ORDER_NOT_APPROVE = new ErrorCode(1_030_607_006, "销售订单({})不是审核通过状态");
    ErrorCode SALES_ORDER_LINE_SHIPPING_QUANTITY_ERROR = new ErrorCode(1_030_607_007, "发货数量（{}）不可大于未发货数量");
    ErrorCode SALES_ORDER_LOCK_FAILED = new ErrorCode(1_030_607_008, "销售订单({})加锁失败");
    ErrorCode SALES_ORDER_IN_SHIPPING = new ErrorCode(1_030_607_009, "销售订单已经被发货通知单引用，不支持当前操作");
    ErrorCode SALES_ORDER_NOT_NORMAL = new ErrorCode(1_030_607_010, "销售订单({})不是正常状态，不能进行当前操作");
    ErrorCode SALES_ORDER_LINE_REFER_MATERIAL_EXITS = new ErrorCode(1_030_607_011, "物料档案已被销售订单行引用，不能删除");
    ErrorCode SALES_ORDER_REFER_CUSTOMER_EXITS = new ErrorCode(1_030_607_012, "客户档案已被销售订单引用，不能删除");
    ErrorCode SALES_ORDER_CUSTOMER_ORDER_CODE_SET = new ErrorCode(1_030_607_013, "销售订单({})的客户订单号已经设置过，不能重复设置");
    //========== ERP 使用redis 生成单据编号 1-030-608-000 ==========
    //流水号溢出
    ErrorCode SERIAL_NUMBER_OVERFLOW = new ErrorCode(1_030_608_000, "流水号溢出");
    //使用redis 生成单据编号发生异常
    ErrorCode SERIAL_NUMBER_EXCEPTION = new ErrorCode(1_030_608_001, "使用redis 生成单据编号发生异常");


    //========== ERP 备货通知单 1-030-609-000 ==========
    ErrorCode PICKING_LIST_NOT_EXISTS = new ErrorCode(1_030_609_000, "备货通知单({})不存在");
    ErrorCode PICKING_LIST_NOT_EDITABLE = new ErrorCode(1_030_609_001, "仅状态为编制中、回退的单据可编辑");
    ErrorCode PICKING_LIST_NOT_BUSINESS_STATUS_CHANGE = new ErrorCode(1_030_609_002, "当前业务状态不允许进行此项操作");
    ErrorCode PICKING_LIST_NOT_DOCUMENT_STATUS_CHANGE = new ErrorCode(1_030_609_003, "当前单据的状态不允许进行此操作");
    ErrorCode PICKING_LIST_LINE_REFER_MATERIAL_EXITS = new ErrorCode(1_030_609_004, "物料档案已被备货通知单行引用，不能删除");
    ErrorCode PICKING_LIST_REFER_CUSTOMER_EXITS = new ErrorCode(1_030_609_005, "客户档案已被备货通知单引用，不能删除");
    //========== ERP  包装类型 1-030-610-000 ==========

    ErrorCode PACKAGING_TYPE_NOT_EXISTS = new ErrorCode(1_030_610_000, "包装类型({})不存在");
    ErrorCode PACKAGING_TYPE_NOT_UPDATABLE = new ErrorCode(1_030_610_001, "包装类型({})无法修改");
    ErrorCode PACKAGING_TYPE_CODE_DUPLICATE = new ErrorCode(1_030_610_002, "包装类型编号({})已存在");
    ErrorCode PACKAGING_TYPE_NAME_DUPLICATE = new ErrorCode(1_030_610_003, "包装类型名称({})已存在");
    ErrorCode PACKAGING_CATEGORY_NOT_EXISTS = new ErrorCode(1_030_610_004, "包装类别({})不存在");
    ErrorCode PACKAGING_SPEC_NOT_EXISTS = new ErrorCode(1_030_610_005, "包装规格({})不存在");
    ErrorCode PACKAGING_SPEC_NOT_BELONG_TO_PACKAGING_TYPE = new ErrorCode(1_030_610_006, "包装规格({})不属于包装类型({})");
    ErrorCode PACKAGING_SPEC_NOT_UPDATABLE = new ErrorCode(1_030_610_007, "包装规格({})无法修改");
    ErrorCode PACKAGING_SPEC_CODE_DUPLICATE = new ErrorCode(1_030_610_008, "包装规格编号({})已存在");
    ErrorCode PACKAGING_SPEC_NAME_DUPLICATE = new ErrorCode(1_030_610_009, "包装规格名称({})已存在");
    ErrorCode PACKAGING_SPEC_NOT_ENABLE = new ErrorCode(1_030_610_011, "包装规格({})未启用");
    ErrorCode PACKAGING_SPEC_NOT_FOUND = new ErrorCode(1_030_610_010, "包装规格未找到");
    ErrorCode PACKAGING_SPEC_CAPACITY_NOT_ENOUGH = new ErrorCode(1_030_610_012, "单瓶充装量不能大于包装规格({})的最大容量({})");


    //==============ERP 承运商档案  1-030-611-000 =============
    ErrorCode CARRIER_NOT_EXISTS = new ErrorCode(1_030_611_000, "承运商({})不存在");
    ErrorCode CARRIER_CODE_DUPLICATE = new ErrorCode(1_030_611_001, "承运商code({})已存在");
    ErrorCode CARRIER_NAME_DUPLICATE = new ErrorCode(1_030_611_002, "承运商名称({})已经存在");
    ErrorCode CARRIER_SHORT_NAME_DUPLICATE = new ErrorCode(1_030_611_003, "承运商简称({})已经存在");
    ErrorCode CARRIER_STATUS_DISABLE = new ErrorCode(1_030_611_004, "承运商({})未启用，不支持当前操作");
    ErrorCode CARRIER_DRIVER_NOT_EXISTS = new ErrorCode(1_030_611_005, "承运商司机({})不存在");
    ErrorCode CARRIER_DRIVER_NOT_BELONG_TO_CARRIER = new ErrorCode(1_030_611_006, "司机({})不属于承运商({})");
    ErrorCode CARRIER_DRIVER_STATUS_DISABLE = new ErrorCode(1_030_611_007, "司机({})未启用，不支持当前操作");

    //==============ERP 发货通知单  1-030-612-000 =============
    ErrorCode SHIPPING_NOT_EXISTS = new ErrorCode(1_030_612_000, "发货通知单({})不存在");
    ErrorCode SHIPPING_DOCUMENT_STATUS_IS_APPROVE = new ErrorCode(1_030_612_001, "发货通知单({})已审核通过，不支持当前操作");

    ErrorCode SHIPPING_NOT_BUSINESS_STATUS_CHANGE = new ErrorCode(1_030_612_002, "发货通知单({})当前状态不支持此操作");
    ErrorCode SHIPPING_DOCUMENT_STATUS_NOT_SUPPORT_OPERATION = new ErrorCode(1_030_612_003, "发货通知单({})当前状态不支持此操作");
    ErrorCode SHIPPING_LOCK_FAILED = new ErrorCode(1_030_612_004, "发货通知单({})获取锁失败，请稍后再试");
    ErrorCode SHIPPING_LINE_NOT_EXISTS = new ErrorCode(1_030_612_005, "发货通知单({})不存在行信息");
    ErrorCode SHIPPING_BUSINESS_STATUS_NOT_SUPPORT_OPERATION = new ErrorCode(1_030_612_006, "发货通知单({})当前业务状态不支持此操作");
    ErrorCode SHIPPING_LINE_OUT_QUANTITY_EXCEEDS_UNSHIPPED = new ErrorCode(1_030_612_007, "发货通知单行的出库数量({})大于未出库数量({})，请检查");
    ErrorCode SHIPPING_LINE_OUT_QUANTITY_EXCEEDS_SHIPPED = new ErrorCode(1_030_612_008, "发货通知单行的出库数量({})大于已出库数量({})，请检查");
    ErrorCode SHIPPING_IN_OUTBOUND = new ErrorCode(1_030_612_009, "发货通知单已经存在出库记录，请检查");
    ErrorCode SHIPPING_LINE_REFER_MATERIAL_EXITS = new ErrorCode(1_030_612_010, "物料档案已被发货通知单行引用，不能删除");
    ErrorCode SHIPPING_REFER_CUSTOMER_EXITS = new ErrorCode(1_030_612_011, "客户档案已被发货通知引用，不能删除");

    //=============ERP 生产入库  1-030-613-000 =============
    ErrorCode PRODUCE_INBOUND_NOT_EXISTS = new ErrorCode(1_030_613_000, "生产入库({})不存在");
    ErrorCode PRODUCE_INBOUND_LINE_GROSS_WEIGHT_TARE_WEIGHT_NOT_EQUAL_IN_QUANTITY = new ErrorCode(1_030_613_001, "生产入库中，商品毛重({}) - 空重({}) != 入库数量({})");
    ErrorCode PRODUCE_INBOUND_DOCUMENT_STATUS_IS_APPROVE = new ErrorCode(1_030_613_002, "生产入库单({})已审核，不能进行操作");
    ErrorCode PRODUCE_INBOUND_BUSINESS_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_613_003, "生产入库单({})当前业务状态不支持该操作");

    ErrorCode PRODUCE_INBOUND_LOCK_FAIL = new ErrorCode(1_030_613_004, "生产入库单({})加锁失败");
    ErrorCode PRODUCE_INBOUND_DOCUMENT_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_613_005, "生产入库单({})当前单据状态不支持此操作");
    ErrorCode PRODUCE_INBOUND_LINE_PACKAGING_COMPANY_ID_NOT_NULL = new ErrorCode(1_030_613_006, "归属公司不能为空");
    ErrorCode PRODUCE_INBOUND_LINE_REFER_MATERIAL_EXITS = new ErrorCode(1_030_613_007, "物料档案已被生产入库单行引用，不能删除");
    ErrorCode PRODUCE_INBOUND_REFER_WAREHOUSE_EXITS = new ErrorCode(1_030_613_008, "仓库档案已被生产入库单引用，不能删除");

    // ============ERP 销售出库  1-*********** ============
    ErrorCode SALES_OUTBOUND_NOT_EXISTS = new ErrorCode(1_030_614_000, "销售出库单({})不存在");
    ErrorCode SALES_OUTBOUND_NOT_APPROVED = new ErrorCode(1_030_614_001, "销售出库单({})没有审核通过，不支持当前操作");
    ErrorCode SALES_OUTBOUND_ITEM_RETURNED_QUANTITY_NOT_ENOUGH = new ErrorCode(1_030_614_002, "退货数量({})大于销售出库明细中的可退数量({})");
    ErrorCode SALES_OUTBOUND_SHIPMENT_OUT_QUANTITY_NOT_ENOUGH = new ErrorCode(1_030_614_003, "本次发货数量({})大于未出库数量({})，请检查");
    ErrorCode SALES_OUTBOUND_SHIPMENT_OUT_QUANTITY_NOT_POSITIVE = new ErrorCode(1_030_614_004, "本次发货数量({})必须大于0");
    ErrorCode SALES_OUTBOUND_SHIPMENT_UNSHIPPED_QUANTITY_NOT_POSITIVE = new ErrorCode(1_030_614_005, "未发货数量({})必须大于0");
    ErrorCode SALES_OUTBOUND_ITEM_NOT_EXISTS = new ErrorCode(1_030_614_006, "销售出库单明细({})不存在");
    ErrorCode SALES_OUTBOUND_ITEM_OUT_QUANTITY_NOT_ENOUGH = new ErrorCode(1_030_614_007, "销售出库单明细({})的出库数量({})大于未出库数量({})");
    ErrorCode SALES_OUTBOUND_ITEM_OUT_QUANTITY_NOT_POSITIVE = new ErrorCode(1_030_614_008, "销售出库单明细的出库数量({})必须大于0");
    ErrorCode SALES_OUTBOUND_CAN_NOT_EDIT = new ErrorCode(1_030_614_009, "销售出库单({})，当前状态为：({})，不支持当前操作");
    ErrorCode SALES_OUTBOUND_NOT_BUSINESS_STATUS_CHANGE = new ErrorCode(1_030_614_010, "销售出库单当前业务状态不支持此操作");
    ErrorCode SALES_OUTBOUND_LOCK_FAILED = new ErrorCode(1_030_614_011, "销售出库单({})加锁失败，请稍后再试");
    ErrorCode SALES_OUTBOUND_SHIPPING_LOCK_FAILED = new ErrorCode(1_030_614_012, "销售出库单({})发货通知单加锁失败，请稍后再试");
    ErrorCode SALES_OUTBOUND_SHIPMENT_SHIPPING_ITEM_ID_DUPLICATE = new ErrorCode(1_030_614_014, "销售出库单发货通知单明细({})已存在，不能重复添加");
    ErrorCode SALES_OUTBOUND_IN_RETURNED_ORDER = new ErrorCode(1_030_614_015, "销售出库单存在退货记录，请检查");
    ErrorCode SALES_OUTBOUND_SHIPMENT_REFER_MATERIAL_EXITS = new ErrorCode(1_030_614_016, "物料档案已被销售出库单引用，不能删除");
    ErrorCode SALES_OUTBOUND_ITEM_REFER_MATERIAL_EXITS = new ErrorCode(1_030_614_017, "物料档案已被销售出库单明细引用，不能删除");
    ErrorCode SALES_OUTBOUND_REFER_CUSTOMER_EXITS = new ErrorCode(1_030_614_018, "客户档案已被销售出库单引用，不能删除");
    ErrorCode SALES_OUTBOUND_SHIPMENT_SHIPPING_ID_NOT_UNIQUE = new ErrorCode(1_030_614_019, "销售出库单发货通知单({})不唯一，请检查");
    ErrorCode SALES_OUTBOUND_DOCUMENT_STATUS_NOT_SUPPORT_OPERATION = new ErrorCode(1_030_614_020, "销售出库单({})当前单据状态不支持此操作");
    ErrorCode SALES_OUTBOUND_ITEM_GRATER_THAN_INVENTORY_QUANTITY = new ErrorCode(1_030_614_021, "销售出库单明细的出库数量({})大于库存数量({})，请检查");
    ErrorCode SALES_OUTBOUND_SHIPMENT_SHIPPING_ID_DUPLICATE = new ErrorCode(1_030_614_022, "销售出库单发货通知单({})已存在，不能重复添加");
    ErrorCode SALES_OUTBOUND_OUT_QUANTITY_BIG_THAN_INVENTORY_QUANTITY = new ErrorCode(1_030_614_023, "销售出库单发货数量大于仓库中的可用数量");
    ErrorCode SALES_OUTBOUND_SHIPMENT_OUT_QUANTITY_ILLEGAL = new ErrorCode(1_030_614_024, "销售出库单发货数量({})不等于出库明细中的数量({})");
    ErrorCode SALES_OUTBOUND_REFER_WAREHOUSE_EXITS = new ErrorCode(1_030_614_025, "仓库档案已被销售出库单引用，不能删除");
    //=============ERP 库存详情  1-030-615-000 ============

    ErrorCode INVENTORY_DETAIL_NOT_EXISTS = new ErrorCode(1_030_615_000, "库存详情({})不存在");
    ErrorCode INVENTORY_DETAIL_MATERIAL_NOT_EXISTS = new ErrorCode(1_030_615_001, "库存详情中，物料({})不存在");
    ErrorCode INVENTORY_DETAIL_MATERIAL_NOT_ENABLE = new ErrorCode(1_030_615_002, "库存详情中，物料({})未启用");

    ErrorCode INVENTORY_DETAIL_MATERIAL_NAME_NOT_MATCH = new ErrorCode(1_030_615_003, "库存详情中，物料的名称({})和物料档案中的名称({})不匹配");
    ErrorCode INVENTORY_DETAIL_MATERIAL_CODE_NOT_MATCH = new ErrorCode(1_030_615_004, "库存详情中，物料({})的编号({})和物料档案中的编号({})不匹配");
    ErrorCode INVENTORY_DETAIL_MATERIAL_SPEC_NOT_MATCH = new ErrorCode(1_030_615_005, "库存详情中，物料({})的规格型号({})和物料档案中的规格型号({})不匹配");
    ErrorCode INVENTORY_DETAIL_MATERIAL_UOM_NOT_MATCH = new ErrorCode(1_030_615_006, "库存详情中，物料({})的主计量单位({})和物料档案中的主计量单位({})不匹配");
    ErrorCode INVENTORY_DETAIL_GROSS_WEIGHT_TARE_WEIGHT_NOT_EQUAL_IN_QUANTITY = new ErrorCode(1_030_615_007, "库存详情中，商品毛重({}) - 空重({}) != 入库数量({})");
    ErrorCode INVENTORY_DETAIL_LOCK_FAIL = new ErrorCode(1_030_615_008, "库存详情({})加锁失败，请稍后再试");
    ErrorCode INVENTORY_DETAIL_REFER_MATERIAL_EXITS = new ErrorCode(1_030_615_009, "物料档案已被库存详情引用，不能删除");
    ErrorCode INVENTORY_DETAIL_OUT_QUANTITY_GREATER_THAN_AVAILABLE_QUANTITY = new ErrorCode(1_030_615_010, "库存详情的出库数量({})大于可用库存数量({})，请检查");
    ErrorCode INVENTORY_DETAIL_GROSS_WEIGHT_TARE_WEIGHT_NOT_EQUAL_OUT_QUANTITY = new ErrorCode(1_030_615_011, "库存详情中，商品毛重({}) - 空重({}) != 出库数量({})");
    ErrorCode INVENTORY_DETAIL_REFER_WAREHOUSE_EXITS = new ErrorCode(1_030_615_012, "仓库已存在库存，不能删除");


    //=============ERP 退货通知单  1-030-616-000 ============
    ErrorCode RETURNED_ORDER_NOT_EXISTS = new ErrorCode(1_030_616_000, "退货通知单({})不存在");

    ErrorCode RETURNED_ORDER_ITEMS_SALES_OUTBOUND_NOT_SAME = new ErrorCode(1_030_616_001, "退货通知单明细中的销售出库单不一致");
    ErrorCode RETURNED_ORDER_ITEMS_HAS_SAME_SALES_OUTBOUND_ITEM = new ErrorCode(1_030_616_002, "退货通知单({})明细中出现相同的销售出库单明细");
    ErrorCode RETURNED_ORDER_ITEMS_PACKAGING_CODE_NOT_SAME = new ErrorCode(1_030_616_003, "退货通知单,提交的包装码({})和销售出库单中的包装码({})不一致");
    ErrorCode RETURNED_ORDER_DOCUMENT_STATUS_IS_APPROVE = new ErrorCode(1_030_616_004, "退货通知单({})已审核通过，不支持当前操作");
    ErrorCode RETURNED_ORDER_DOCUMENT_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_616_005, "退货通知单({})当前单据状态不支持此操作");
    ErrorCode RETURNED_ORDER_BUSINESS_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_616_006, "退货通知单({})当前业务状态不支持此操作");
    ErrorCode RETURNED_ORDER_LOCK_FAILED = new ErrorCode(1_030_616_007, "退货通知单({})加锁失败，请稍后再试");

    ErrorCode RETURNED_ORDER_LOCK_SALES_OUTBOUND_ITEM_FAILED = new ErrorCode(1_030_616_008, "退货通知单({})锁销售出库明细({})失败，请稍后再试");
    ErrorCode RETURNED_ORDER_ITEM_IN_QUANTITY_GE_QUANTITY = new ErrorCode(1_030_616_009, "退货通知单的入库数量({})不能大于退货数量({})");
    ErrorCode RETURNED_ORDER_ITEM_IN_QUANTITY_NOT_ENOUGH = new ErrorCode(1_030_616_010, "退货通知单的入库数量({})不能大于未确认入库数量({})");
    ErrorCode RETURNED_ORDER_IN_RETURNED_INBOUND = new ErrorCode(1_030_616_011, "退货通知单存在退货入库记录，请检查");
    ErrorCode RETURNED_ORDER_ITEMS_REFER_MATERIAL_EXITS = new ErrorCode(1_030_616_012, "物料档案已被退货通知单行引用，不能删除");
    ErrorCode RETURNED_ORDER_REFER_CUSTOMER_EXITS = new ErrorCode(1_030_616_013, "客户档案已被退货通知单引用，不能删除");
    ErrorCode RETURNED_ORDER_REFER_WAREHOUSE_EXITS = new ErrorCode(1_030_616_014, "仓库档案已被退货通知单引用，不能删除");
    //==============ERP 退货入库  1-030-617-000 ============
    ErrorCode RETURNED_INBOUND_NOT_EXISTS = new ErrorCode(1_030_617_000, "退货入库({})不存在");
    ErrorCode RETURNED_INBOUND_DOCUMENT_STATUS_IS_APPROVE = new ErrorCode(1_030_617_001, "退货入库单({})当前单据状态不支持此操作");
    ErrorCode RETURNED_INBOUND_BUSINESS_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_617_002, "退货入库单({})当前单据业务状态不支持此操作");
    ErrorCode RETURNED_INBOUND_LOCK_FAILED = new ErrorCode(1_030_617_003, "退货入库单({})加锁失败，请稍后再试");
    ErrorCode RETURNED_INBOUND_DOCUMENT_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_617_004, "退货入库单({})，文档状态不支持当前操作");
    ErrorCode RETURNED_INBOUND_LOCK_RETURNED_ORDER_ITEM_FAILED = new ErrorCode(1_030_617_005, "退货入库单({})锁退货通知单明细({})失败，请稍后再试");
    ErrorCode RETURNED_INBOUND_ITEMS_NOT_SAME_RETURNED_ORDER = new ErrorCode(1_030_617_006, "退货入库只支持同一退货通知单号的退货明细");
    ErrorCode RETURNED_INBOUND_ITEMS_HAS_SAME_RETURNED_ORDER_ITEM = new ErrorCode(1_030_617_007, "退货入库中有重复的退货通知单明细");
    ErrorCode RETURNED_INBOUND_IN_QUANTITY_NOT_ENOUGH = new ErrorCode(1_030_617_008, "退货入库单的入库数量({})大于退货通知单的可入库数量({})");
    ErrorCode RETURNED_INBOUND_ITEMS_REFER_MATERIAL_EXITS = new ErrorCode(1_030_617_009, "物料档案已被退货入库单行引用，不能删除");
    ErrorCode RETURNED_INBOUND_REFER_CUSTOMER_EXITS = new ErrorCode(1_030_617_010, "客户档案已被退货入库单引用，不能删除");
    ErrorCode RETURNED_INBOUND_REFER_WAREHOUSE_EXITS = new ErrorCode(1_030_617_011, "仓库档案已被退货入库单引用，不能删除");

    //========== ERP 采购申请单 1-030-618-000 ==========
    ErrorCode PURCHASE_REQUEST_NOT_EXISTS = new ErrorCode(1_030_618_000, "采购申请单({})不存在");
    ErrorCode PURCHASE_REQUEST_NOT_EDITABLE = new ErrorCode(1_030_618_001, "仅状态为编制中、回退的采购申请单可编辑");
    ErrorCode PURCHASE_REQUEST_NOT_DELETABLE = new ErrorCode(1_030_618_002, "仅状态为编制中、回退的采购申请单可删除");
    ErrorCode PURCHASE_REQUEST_NOT_APPROVABLE = new ErrorCode(1_030_618_003, "仅状态为编制中、回退的采购申请单可审核");
    ErrorCode PURCHASE_REQUEST_NOT_CANCEL_APPROVABLE = new ErrorCode(1_030_618_004, "仅状态为已审核的采购申请单可取消审核");
    ErrorCode PURCHASE_REQUEST_BUSINESS_STATUS_NOT_MATCH = new ErrorCode(1_030_618_005, "采购申请单业务状态不匹配");
    ErrorCode PURCHASE_REQUEST_ITEM_NOT_EXISTS = new ErrorCode(1_030_619_006, "采购申请单明细({})不存在");
    ErrorCode PURCHASE_REQUEST_REQUIRED_DATE_INVALID = new ErrorCode(1_030_618_007, "需求日期不能早于申请日期");
    ErrorCode PURCHASE_REQUEST_ITEM_NO_QUANTITY = new ErrorCode(1_030_618_008, "采购申请单明细({})申请数量为0");
    ErrorCode PURCHASE_REQUEST_ITEMS_EMPTY = new ErrorCode(1_030_618_009, "采购申请单明细不能为空");
    ErrorCode PURCHASE_EXPECTED_ARRIVAL_DATE_INVALID = new ErrorCode(1_030_618_010, "预计到货日期不准确");
    ErrorCode PURCHASE_REQUEST_NOT_APPROVE = new ErrorCode(1_030_618_011, "采购申请单({})不是审核通过状态");


    //========== ERP 采购订单 1-030-619-000 ==========
    ErrorCode PURCHASE_ORDER_NOT_EXISTS = new ErrorCode(1_030_619_000, "采购订单({})不存在");
    ErrorCode PURCHASE_ORDER_BUSINESS_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_619_001, "采购订单({})业务状态不支持变更");
    ErrorCode PURCHASE_ORDER_DOCUMENT_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_619_002, "采购订单({})单据状态，不支持当前操作");
    ErrorCode PURCHASE_ORDER_ITEM_REFER_MATERIAL_EXITS = new ErrorCode(1_030_619_004, "采购订单明细引用物料({})，不允许删除");
    ErrorCode PURCHASE_ORDER_ITEM_QUANTITY_EXCEED_REQUEST = new ErrorCode(1_030_619_005, "采购数量({})不能超过申请单的未采购数量({})");
    ErrorCode PURCHASE_ORDER_REFER_SUPPLIER_EXITS = new ErrorCode(1_030_619_006, "采购订单引用供应商({})，不允许删除");
    /**
     * 税价合计不匹配
     */
    ErrorCode PURCHASE_ORDER_ITEM_TAX_PRICE_MISMATCH = new ErrorCode(1_030_619_006, "采购数量({}) * 含税单价({}) != 税价合计({})");
    ErrorCode PURCHASE_ORDER_IN_PURCHASE_ARRIVAL = new ErrorCode(1_030_619_007, "采购订单存在到货记录，不支持当前操作，请检查");
    ErrorCode PURCHASE_ORDER_NOT_APPROVE = new ErrorCode(1_030_619_008, "采购订单({})不是审核通过状态");
    ErrorCode PURCHASE_ORDER_ITEM_NOT_EXISTS = new ErrorCode(1_030_619_009, "采购订单Item({})不存在");


    //========== ERP 采购到货单 1-*********** ==========
    ErrorCode PURCHASE_ARRIVAL_NOT_EXISTS = new ErrorCode(1_030_620_000, "采购到货单({})不存在");
    ErrorCode PURCHASE_ARRIVAL_NOT_EDITABLE = new ErrorCode(1_030_620_001, "仅状态为编制中、回退的采购到货单可编辑");
    ErrorCode PURCHASE_ARRIVAL_NOT_APPROVABLE = new ErrorCode(1_030_620_003, "仅状态为编制中、回退的采购到货单可审核");
    ErrorCode PURCHASE_ARRIVAL_NOT_CANCEL_APPROVABLE = new ErrorCode(1_030_620_004, "仅状态为已审核的采购到货单可取消审核");
    ErrorCode PURCHASE_ARRIVAL_BUSINESS_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_620_005, "采购到货单({})业务状态不支持变更");
    ErrorCode PURCHASE_ARRIVAL_DOCUMENT_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_620_006, "采购到货单({})单据状态，不支持当前操作");
    ErrorCode PURCHASE_ARRIVAL_ITEM_REFER_MATERIAL_EXITS = new ErrorCode(1_030_620_008, "采购到货单明细引用物料({})，不允许删除");
    ErrorCode PURCHASE_ARRIVAL_ITEMS_NOT_SAME_PURCHASE_ORDER = new ErrorCode(1_030_620_010, "仅支持同一采购订单号");
    ErrorCode PURCHASE_ARRIVAL_ITEM_ARRIVAL_QUANTITY_NOT_POSITIVE = new ErrorCode(1_030_620_011, "到货数量({})必须大于0");
    ErrorCode PURCHASE_ARRIVAL_ARRIVAL_QUANTITY_BIG_THAN_UNARRIVED_QUANTITY = new ErrorCode(1_030_620_012, "采购到货单的到货数量({})大于采购订单的未到货数量({})");
    ErrorCode PURCHASE_ARRIVAL_ITEM_UNIQUE_CODE_DUPLICATE = new ErrorCode(1_030_620_013, "采购到货单明细的唯一码({})重复");
    ErrorCode PURCHASE_ARRIVAL_ITEM_UNIQUE_CODE_EXISTS_IN_INVENTORY = new ErrorCode(1_030_620_014, "物料({})的唯一码（{}）在库存中已存在");
    ErrorCode PURCHASE_ARRIVAL_ITEM_UNIQUE_CODE_SHOULD_BE_NULL = new ErrorCode(1_030_620_015, "物料({})未开启唯一码管理，唯一码必须为空");
    ErrorCode PURCHASE_ARRIVAL_ITEM_UNIQUE_CODE_REQUIRED = new ErrorCode(1_030_620_016, "物料({})已开启唯一码管理，唯一码不能为空");
    ErrorCode PURCHASE_ARRIVAL_IN_PURCHASE_INBOUND = new ErrorCode(1_030_620_017, "采购到货单存在入库记录，不支持当前操作，请检查");
    ErrorCode PURCHASE_ARRIVAL_ITEM_INBOUND_QUANTITY_NOT_ENOUGH = new ErrorCode(1_030_620_018, "入库数量({})不能大于到货单的未入库数量({})");
    ErrorCode PURCHASE_ARRIVAL_ITEM_NOT_EXISTS_OR_NOT_APPROVE = new ErrorCode(1_030_620_019, "采购到货单明细({})不存在或未审核通过");
    ErrorCode PURCHASE_ARRIVAL_REFER_SUPPLIER_EXITS = new ErrorCode(1_030_620_020, "采购到货单引用供应商({})，不允许删除");


    //========== ERP 采购物料配置 1-030-621-000 ==========
    ErrorCode PURCHASE_MATERIAL_CONFIG_NOT_EXISTS = new ErrorCode(1_030_621_000, "采购物料配置不存在");
    ErrorCode PURCHASE_MATERIAL_CONFIG_MATERIAL_DUPLICATE = new ErrorCode(1_030_621_001, "该物料已存在采购周期配置");

    //========== ERP 采购物料配置导入 1-030-621-100 ==========


    ErrorCode IMPORT_MATERIAL_CODE_NOT_EXISTS = new ErrorCode(1_030_621_101, "物料编码({})不存在");
    ErrorCode IMPORT_MATERIAL_NAME_NOT_MATCH = new ErrorCode(1_030_621_102, "物料名称({})与编码({})不匹配");
    ErrorCode IMPORT_MATERIAL_NOT_ENABLED = new ErrorCode(1_030_621_103, "物料({})未启用");
    ErrorCode IMPORT_DUPLICATE_MATERIAL_CODE = new ErrorCode(1_030_621_104, "Excel中存在重复的物料编码({})");

    //========== ERP 采购入库单 1-030-622-000 ==========
    ErrorCode PURCHASE_INBOUND_NOT_EXISTS = new ErrorCode(1_030_622_000, "采购入库单({})不存在");
    ErrorCode PURCHASE_INBOUND_CANNOT_EDIT = new ErrorCode(1_030_622_001, "仅状态为编制中、回退的采购入库单可编辑");
    ErrorCode PURCHASE_INBOUND_CANNOT_DELETE = new ErrorCode(1_030_622_002, "仅状态为编制中、回退的采购入库单可删除");
    ErrorCode PURCHASE_INBOUND_CANNOT_APPROVE = new ErrorCode(1_030_622_003, "仅状态为编制中、回退的采购入库单可审核");
    ErrorCode PURCHASE_INBOUND_CANNOT_CANCEL_APPROVE = new ErrorCode(1_030_622_004, "仅状态为已审核的采购入库单可取消审核");
    ErrorCode PURCHASE_INBOUND_DOCUMENT_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_621_006, "采购入库单({})单据状态，不支持当前操作");
    ErrorCode PURCHASE_INBOUND_ITEMS_EMPTY = new ErrorCode(1_030_622_007, "采购入库单明细不能为空");
    ErrorCode PURCHASE_INBOUND_ITEMS_REFER_MATERIAL_EXITS = new ErrorCode(1_030_622_008, "采购入库单明细引用物料({})，不允许删除");
    ErrorCode PURCHASE_INBOUND_REFER_SUPPLIER_EXITS = new ErrorCode(1_030_622_009, "采购入库单引用供应商({})，不允许删除");
    ErrorCode PURCHASE_INBOUND_REFER_WAREHOUSE_EXITS = new ErrorCode(1_030_622_010, "采购入库单引用仓库({})，不允许删除");
    ErrorCode PURCHASE_INBOUND_BUSINESS_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_622_011, "采购入库单({})当前业务状态不支持此操作");
    ErrorCode PURCHASE_INBOUND_LOCK_FAILED = new ErrorCode(1_030_622_012, "采购入库单({})加锁失败，请稍后再试");
    ErrorCode PURCHASE_INBOUND_ITEM_PURCHASE_ARRIVAL_ITEM_ID_DUPLICATE = new ErrorCode(1_030_622_013, "采购入库明细存在相同的采购到货单明细ID({})，请检查");
    ErrorCode PURCHASE_INBOUND_ITEM_UNIQUE_CODE_DUPLICATE = new ErrorCode(1_030_622_014, "采购入库唯一码({})重复，请检查");
    ErrorCode PURCHASE_INBOUND_ITEM_UNIQUE_CODE_SHOULD_BE_NULL = new ErrorCode(1_030_622_015, "物料({})未开启唯一码管理，唯一码必须为空");
    ErrorCode PURCHASE_INBOUND_ITEM_UNIQUE_CODE_REQUIRED = new ErrorCode(1_030_622_016, "物料({})已开启唯一码管理，唯一码不能为空");
    ErrorCode PURCHASE_INBOUND_ITEM_UNIQUE_CODE_EXISTS_IN_INVENTORY = new ErrorCode(1_030_622_017, "物料({})的唯一码（{}）在库存中已存在");
    ErrorCode PURCHASE_INBOUND_ITEM_RETURNABLE_QUANTITY_NOT_ENOUGH = new ErrorCode(1_030_622_019, "采购入库的可退货数量({})小于退货数量({})");


    // === Excel通用问题 1-030-623-000 =====

    ErrorCode IMPORT_FILE_EMPTY = new ErrorCode(1_030_623_000, "导入文件不能为空");
    ErrorCode IMPORT_FILE_FORMAT_ERROR = new ErrorCode(1_030_623_001, "导入文件格式错误，仅支持.xlsx和.xls格式");
    ErrorCode IMPORT_EXCEL_PARSE_ERROR = new ErrorCode(1_030_623_002, "Excel文件解析失败，请检查文件格式");
    ErrorCode IMPORT_ERROR_FILE_GENERATE_FAILED = new ErrorCode(1_030_623_003, "生成错误文件失败");
    ErrorCode IMPORT_FILE_LINE_COUNT_EXCEED = new ErrorCode(1_030_623_004, "导入文件行数超过限制({})，请检查");

    // ====================== 合作伙伴  1-030-624-000============================================
    ErrorCode ORGANIZATION_NOT_EXISTS = new ErrorCode(1_030_624_000, "合作伙伴({})不存在");
    ErrorCode ORGANIZATION_NAME_DUPLICATE = new ErrorCode(1_030_624_001, "该公司({})已在商业伙伴中存在，请从商业伙伴中导入");
    ErrorCode ORGANIZATION_SHORT_NAME_DUPLICATE = new ErrorCode(1_030_624_002, "该公司({})已在商业伙伴中存在，请从商业伙伴中导入");
    ErrorCode ORGANIZATION_NAME_LIKE = new ErrorCode(1_030_624_003, "商业伙伴中存在与该公司名称（{}）相似的公司，是否从商业伙伴中导入");


    // ==================供应商 1-030-625-000 ====================
    ErrorCode SUPPLIER_NOT_EXISTS = new ErrorCode(1_030_625_000, "供应商（{}）不存在");
    ErrorCode SUPPLIER_CODE_DUPLICATE = new ErrorCode(1_030_625_001, "供应商编号（{}）重复");
    ErrorCode SUPPLIER_NAME_DUPLICATE = new ErrorCode(1_030_625_002, "供应商名称（{}）重复");
    ErrorCode SUPPLIER_SHORT_NAME_DUPLICATE = new ErrorCode(1_030_625_003, "供应商简称（{}）重复");
    ErrorCode SUPPLIER_ALREADY_DISABLE = new ErrorCode(1_030_625_004, "供应商（{}已经停用");
    ErrorCode SUPPLIER_ALREADY_ENABLE = new ErrorCode(1_030_625_005, "供应商（{}已经启用");


    // ================== ERP 采购退货单 1-030-626-000 ====================
    ErrorCode PURCHASE_RETURNED_NOT_EXISTS = new ErrorCode(1_030_626_000, "采购退货单({})不存在");
    ErrorCode PURCHASE_RETURNED_NOT_EDITABLE = new ErrorCode(1_030_626_001, "仅状态为编制中、回退的采购退货单可编辑");
    ErrorCode PURCHASE_RETURNED_NOT_APPROVABLE = new ErrorCode(1_030_626_003, "仅状态为编制中、回退的采购退货单可审核");
    ErrorCode PURCHASE_RETURNED_NOT_CANCEL_APPROVABLE = new ErrorCode(1_030_626_004, "仅状态为已审核的采购退货单可取消审核");
    ErrorCode PURCHASE_RETURNED_ITEMS_NOT_SAME_PURCHASE_ARRIVAL = new ErrorCode(1_030_626_005, "采购退货单只支持同一采购到货单号的退货");
    ErrorCode PURCHASE_RETURNED_CANCEL_APPROVE_FAIL_STATUS_NOT_APPROVED = new ErrorCode(1_030_626_006, "采购退货单({})当前状态不是已审核，不能进行反审核");
    ErrorCode PURCHASE_RETURNED_BUSINESS_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_626_007, "采购退货单({})当前业务状态不支持此操作");
    ErrorCode PURCHASE_RETURNED_LOCK_FAILED = new ErrorCode(1_030_626_008, "采购退货单({})加锁失败，请稍后再试");
    ErrorCode PURCHASE_RETURNED_ITEM_RETURNED_QUANTITY_NOT_POSITIVE = new ErrorCode(1_030_626_009, "退货数量({})必须大于0");
    ErrorCode PURCHASE_RETURNED_ITEMS_NOT_SAME_WAREHOUSE = new ErrorCode(1_030_626_010, "采购退货单明细中的仓库不一致");

    // ================== ERP 采购退货出库单 1-030-628-000 ====================
    ErrorCode PURCHASE_RETURNED_OUTBOUND_NOT_EXISTS = new ErrorCode(1_030_628_000, "采购退货出库单({})不存在");
    ErrorCode PURCHASE_RETURNED_OUTBOUND_NOT_EDITABLE = new ErrorCode(1_030_628_001, "仅状态为编制中、回退的采购退货出库单可编辑");
    ErrorCode PURCHASE_RETURNED_OUTBOUND_NOT_APPROVABLE = new ErrorCode(1_030_628_003, "仅状态为编制中、回退的采购退货出库单可审核");
    ErrorCode PURCHASE_RETURNED_OUTBOUND_NOT_CANCEL_APPROVABLE = new ErrorCode(1_030_628_004, "仅状态为已审核的采购退货出库单可取消审核");
    ErrorCode PURCHASE_RETURNED_OUTBOUND_BUSINESS_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_628_005, "采购退货出库单({})当前业务状态不支持此操作");
    ErrorCode PURCHASE_RETURNED_OUTBOUND_DOCUMENT_STATUS_NOT_SUPPORT_CHANGE = new ErrorCode(1_030_628_006, "采购退货出库单({})当前单据状态不支持此操作");
    ErrorCode PURCHASE_RETURNED_OUTBOUND_LOCK_FAILED = new ErrorCode(1_030_628_007, "采购退货出库单({})加锁失败，请稍后再试");
    ErrorCode PURCHASE_RETURNED_OUTBOUND_ITEM_OUT_QUANTITY_NOT_POSITIVE = new ErrorCode(1_030_628_008, "出库数量({})必须大于0");

    //========== ERP 领料单 1-030-627-000 ==========
    ErrorCode REQUISITION_ORDER_NOT_EXISTS = new ErrorCode(1_030_627_000, "领料单({})不存在");
    ErrorCode REQUISITION_ORDER_UPDATE_FAIL_APPROVE = new ErrorCode(1_030_627_001, "领料单({})已审核，不能修改");
    ErrorCode REQUISITION_ORDER_DELETE_FAIL_APPROVE = new ErrorCode(1_030_627_002, "领料单({})已审核，不能删除");
    ErrorCode REQUISITION_ORDER_APPROVE_FAIL_STATUS_NOT_DRAFT = new ErrorCode(1_030_627_003, "领料单({})不是草稿状态，不能审核");
    ErrorCode REQUISITION_ORDER_CANCEL_APPROVE_FAIL_STATUS_NOT_APPROVE = new ErrorCode(1_030_627_004, "领料单({})不是审核状态，不能取消审核");
    ErrorCode REQUISITION_ORDER_CLOSE_FAIL_STATUS_CLOSED = new ErrorCode(1_030_627_005, "领料单({})已关闭，不能重复关闭");
    ErrorCode REQUISITION_ORDER_CANCEL_CLOSE_FAIL_STATUS_NOT_CLOSED = new ErrorCode(1_030_627_006, "领料单({})未关闭，不能取消关闭");
    ErrorCode REQUISITION_ORDER_SUSPEND_FAIL_STATUS_SUSPENDED = new ErrorCode(1_030_627_007, "领料单({})已挂起，不能重复挂起");
    ErrorCode REQUISITION_ORDER_CANCEL_SUSPEND_FAIL_STATUS_NOT_SUSPENDED = new ErrorCode(1_030_627_008, "领料单({})未挂起，不能取消挂起");
    ErrorCode REQUISITION_ORDER_ITEM_REFER_MATERIAL_EXITS = new ErrorCode(1_030_627_009, "领料单明细引用物料({})，不允许删除");
    ErrorCode REQUISITION_ORDER_REFER_WAREHOUSE_EXITS = new ErrorCode(1_030_627_010, "领料单引用仓库({})，不允许删除");
    ErrorCode REQUISITION_ORDER_ITEM_REQUEST_QUANTITY_EXCEED_AVAILABLE = new ErrorCode(1_030_627_011, "领料数量({})不能大于可用库存数量({})");
    ErrorCode REQUISITION_ORDER_ITEM_REQUEST_QUANTITY_NOT_POSITIVE = new ErrorCode(1_030_627_012, "领料数量({})必须大于0");

    // ========== ERP 库存单据 1-030-629-000 ==========
    ErrorCode INVENTORY_BILL_NOT_EXISTS = new ErrorCode(1_030_629_000, "库存单据({})不存在");
    ErrorCode INVENTORY_BILL_NOT_EDITABLE = new ErrorCode(1_030_629_001, "仅状态为编制中、回退的库存单据可编辑");

}
