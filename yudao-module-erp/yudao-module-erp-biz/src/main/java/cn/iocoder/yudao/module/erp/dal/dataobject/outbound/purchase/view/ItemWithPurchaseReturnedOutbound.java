package cn.iocoder.yudao.module.erp.dal.dataobject.outbound.purchase.view;

import cn.iocoder.yudao.module.erp.dal.dataobject.outbound.purchase.PurchaseReturnedOutboundDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.outbound.purchase.PurchaseReturnedOutboundItemDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 采购退货出库单明细与主表组合 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ItemWithPurchaseReturnedOutbound extends PurchaseReturnedOutboundItemDO {

    /**
     * 采购退货出库单信息
     */
    private PurchaseReturnedOutboundDO purchaseReturnedOutbound;

}
