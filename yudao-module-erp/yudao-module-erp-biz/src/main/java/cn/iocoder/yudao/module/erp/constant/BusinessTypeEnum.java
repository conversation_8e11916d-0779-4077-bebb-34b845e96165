package cn.iocoder.yudao.module.erp.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 业务类型枚举
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {
    SALES_ORDER(DocumentTypeEnum.SALES_ORDER, "salesOrder", "销售订单", "XS"),
    //    配货通知单
    PICKING_LIST(DocumentTypeEnum.PICKING_LIST, "pickingList", "备货通知单", "BH"),
    SHIPPING(DocumentTypeEnum.SHIPPING, "shipping", "发货通知单", "FH"),
    RETURNED_ORDER(DocumentTypeEnum.RETURN_ORDER, "returnedOrder", "退货通知单", "TH"),
    PRODUCE_INBOUND(DocumentTypeEnum.INBOUND, "produceInbound", "生产入库单", "CPRK"),
    SALES_OUTBOUND(DocumentTypeEnum.OUTBOUND, "salesOutbound", "销售出库单", "XSCK"),
    RETURNED_INBOUND(DocumentTypeEnum.INBOUND, "returnedInbound", "销售退货入库", "XSTK"),
    //采购业务
    PURCHASE_REQUEST(DocumentTypeEnum.PURCHASE_REQUEST, "purchaseRequest", "采购申请单", "CGSH"),
    PURCHASE_ORDER(DocumentTypeEnum.PURCHASE_ORDER, "purchaseOrder", "采购订单", "CG"),
    PURCHASE_ARRIVAL(DocumentTypeEnum.PURCHASE_ARRIVAL, "purchaseArrival", "采购到货单", "DH"),
    PURCHASE_INBOUND(DocumentTypeEnum.INBOUND, "purchaseInbound", "采购入库单", "CGRK"),
    PURCHASE_RETURNED(DocumentTypeEnum.PURCHASE_RETURNED, "purchaseReturned", "采购退货单", "CGTH"),
    PURCHASE_RETURNED_OUTBOUND(DocumentTypeEnum.OUTBOUND, "purchaseReturnedOutbound", "采购退货出库单", "CGCK"),

    REQUISITION_ORDER(DocumentTypeEnum.REQUISITION_ORDER, "requisitionOrder", "领料单", "LL");;

    private final DocumentTypeEnum documentTypeEnum;
    private final String type;
    private final String name;
    private final String prefix;


    public static BusinessTypeEnum getEnumByType(String businessType) {
        return Arrays.stream(values())
                .filter(e -> e.type.equals(businessType))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效业务类型: " + businessType));
    }

    @JsonValue
    public String toString() {
        return name;
    }
}
