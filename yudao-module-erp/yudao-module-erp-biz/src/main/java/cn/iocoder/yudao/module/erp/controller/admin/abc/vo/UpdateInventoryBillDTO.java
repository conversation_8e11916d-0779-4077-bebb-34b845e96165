package cn.iocoder.yudao.module.erp.controller.admin.abc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
    import org.springframework.format.annotation.DateTimeFormat;
    import java.time.LocalDateTime;

@Schema(description = "管理后台 - 出入库清单修改 DTO")
@Data
public class UpdateInventoryBillDTO extends CreateInventoryBillDTO {

}