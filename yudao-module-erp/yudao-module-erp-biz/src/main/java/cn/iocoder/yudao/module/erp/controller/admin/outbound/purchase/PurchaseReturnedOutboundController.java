package cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.*;
import cn.iocoder.yudao.module.erp.service.outbound.purchase.PurchaseReturnedOutboundService;
import db.migration.PermissionName;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 采购退货出库单")
@RestController
@RequestMapping("/erp/purchase-returned-outbound")
@Validated
public class PurchaseReturnedOutboundController {

    @Resource
    private PurchaseReturnedOutboundService purchaseReturnedOutboundService;

    @PostMapping
    @Operation(summary = "创建采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:create')")
    @PermissionName(name = "创建采购退货出库单", sort = 1)
    public CommonResult<Long> create(@Valid @RequestBody CreatePurchaseReturnedOutboundDTO createDTO) {
        return success(purchaseReturnedOutboundService.create(createDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:update')")
    @PermissionName(name = "更新采购退货出库单", sort = 2)
    public CommonResult<Boolean> update(@PathVariable("id") Long id, @Valid @RequestBody UpdatePurchaseReturnedOutboundDTO updateDTO) {
        purchaseReturnedOutboundService.update(id, updateDTO);
        return success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:delete')")
    @PermissionName(name = "删除采购退货出库单", sort = 3)
    public CommonResult<Boolean> delete(@PathVariable("id") Long id) {
        purchaseReturnedOutboundService.delete(id);
        return success(true);
    }

    @GetMapping("/{id}")
    @Operation(summary = "查看采购退货出库单详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:view')")
    @PermissionName(name = "查看采购退货出库单详情", sort = 4)
    public CommonResult<PurchaseReturnedOutboundVO> get(@PathVariable("id") Long id) {
        return success(purchaseReturnedOutboundService.get(id));
    }

    @GetMapping("/page")
    @Operation(summary = "查询采购退货出库单分页")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:query')")
    @PermissionName(name = "查询采购退货出库单-单据模式", sort = 8)
    public CommonResult<PageResult<SimplePurchaseReturnedOutboundVO>> page(@Valid SearchPurchaseReturnedOutboundDTO searchDTO) {
        PageResult<SimplePurchaseReturnedOutboundVO> pageResult = purchaseReturnedOutboundService.page(searchDTO);
        return success(pageResult);
    }
    
    @PutMapping("/approve/{id}")
    @Operation(summary = "审核采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:approve')")
    @PermissionName(name = "审核/取消审核采购退货出库单", sort = 5)
    public CommonResult<Boolean> approve(@PathVariable("id") Long id) {
        purchaseReturnedOutboundService.approve(id);
        return success(true);
    }

    @PutMapping("/cancel-approve/{id}")
    @Operation(summary = "取消审核采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:approve')")
    public CommonResult<Boolean> cancelApprove(@PathVariable("id") Long id) {
        purchaseReturnedOutboundService.cancelApprove(id);
        return success(true);
    }

    @GetMapping("/item/page")
    @Operation(summary = "查询采购退货出库单明细分页")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:item:query')")
    @PermissionName(name = "查询采购退货出库单-明细模式", sort = 8)
    public CommonResult<PageResult<ItemWithPurchaseReturnedOutboundVO>> pageItem(@Valid SearchItemPageDTO searchDTO) {
        PageResult<ItemWithPurchaseReturnedOutboundVO> pageResult = purchaseReturnedOutboundService.pageItem(searchDTO);
        return success(pageResult);
    }


}