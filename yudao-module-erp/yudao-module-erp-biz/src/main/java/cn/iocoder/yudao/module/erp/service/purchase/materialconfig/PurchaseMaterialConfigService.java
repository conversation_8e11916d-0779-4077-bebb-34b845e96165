package cn.iocoder.yudao.module.erp.service.purchase.materialconfig;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.materialconfig.vo.*;
import jakarta.validation.Valid;

import java.time.LocalDate;
import java.util.List;

/**
 * 采购物料配置 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseMaterialConfigService {

    /**
     * 保存采购物料配置（创建或更新）
     *
     * @param materialId 物料ID
     * @param saveDTO    保存信息
     * @return 配置编号
     */
    Long save(Long materialId, @Valid SavePurchaseMaterialConfigDTO saveDTO);

    /**
     * 删除采购物料配置
     *
     * @param materialId 物料ID
     */
    void deleteByMaterialId(Long materialId);

    /**
     * 根据物料ID获得采购物料配置
     *
     * @param materialId 物料ID
     * @return 采购物料配置
     */
    PurchaseMaterialConfigVO getByMaterialId(Long materialId);

    /**
     * 获得采购物料配置分页
     *
     * @param searchDTO 分页查询
     * @return 采购物料配置分页
     */
    PageResult<SimplePurchaseMaterialConfigVO> page(SearchPurchaseMaterialConfigDTO searchDTO);

    void validateMaterialRequiredDate(Long materialId, LocalDate requestDate, LocalDate requiredDate);

    List<SimplePurchaseMaterialConfigVO> list(SearchSimplePurchaseMaterialConfigDTO searchDTO);
}
