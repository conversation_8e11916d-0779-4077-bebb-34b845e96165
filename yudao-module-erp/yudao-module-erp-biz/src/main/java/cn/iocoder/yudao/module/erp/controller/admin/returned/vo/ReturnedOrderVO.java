package cn.iocoder.yudao.module.erp.controller.admin.returned.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 退货通知单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ReturnedOrderVO extends SimpleReturnedOrderVO {
    @Schema(description = "退货通知单明细")
    private List<ReturnedOrderItemVO> items;
}