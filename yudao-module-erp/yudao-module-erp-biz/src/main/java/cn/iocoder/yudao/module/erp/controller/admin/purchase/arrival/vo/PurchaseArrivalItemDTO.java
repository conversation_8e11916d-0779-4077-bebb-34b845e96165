package cn.iocoder.yudao.module.erp.controller.admin.purchase.arrival.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 采购到货单明细 DTO")
@Data
public class PurchaseArrivalItemDTO {

    @Schema(description = "ID", example = "1024")
    private Long id;

    @Schema(description = "采购订单ID", example = "1001")
    private Long purchaseOrderId;

    @Schema(description = "采购订单号", example = "CG202507001")
    private String purchaseOrderCode;

    @Schema(description = "采购订单明细ID", example = "1001")
    private Long purchaseOrderItemId;

    @Schema(description = "采购申请单ID", example = "1001")
    private Long purchaseRequestId;

    @Schema(description = "采购申请单号", example = "CGSH202507001")
    private String purchaseRequestCode;


    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    @NotNull(message = "物料ID不能为空")
    private Long materialId;

    @Schema(description = "物料编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "M001")
    @NotNull(message = "物料编码不能为空")
    private String materialCode;

    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "钢材")
    @NotNull(message = "物料名称不能为空")
    private String materialName;

    @Schema(description = "物料规格型号", example = "Q235")
    private String materialSpec;

    @Schema(description = "物料主计量单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "吨")
    @NotNull(message = "物料主计量单位不能为空")
    private String materialUom;

    @Schema(description = "到货数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    @NotNull(message = "到货数量不能为空")
    @Positive(message = "到货数量必须大于0")
    private BigDecimal arrivalQuantity;

    @Schema(description = "唯一码", example = "UC001")
    private String uniqueCode;

    @Schema(description = "备注", example = "备注信息")
    private String remark;

    @Schema(description = "批次号", example = "B001")
    private String batchNumber;

}
