package cn.iocoder.yudao.module.erp.controller.admin.requisition;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.requisition.vo.*;
import cn.iocoder.yudao.module.erp.service.requisition.RequisitionOrderService;
import db.migration.PermissionName;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 领料单")
@RestController
@RequestMapping("/erp/requisition-order")
@Validated
public class RequisitionOrderController {

    @Resource
    private RequisitionOrderService requisitionOrderService;

    @PostMapping
    @Operation(summary = "创建领料单")
    @PreAuthorize("@ss.hasPermission('erp:requisition-order:create')")
    @PermissionName(name = "创建领料单", sort = 1)
    public CommonResult<Long> create(@Valid @RequestBody CreateRequisitionOrderDTO createDTO) {
        return success(requisitionOrderService.create(createDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新领料单")
    @PreAuthorize("@ss.hasPermission('erp:requisition-order:update')")
    @PermissionName(name = "更新领料单", sort = 2)
    public CommonResult<Boolean> update(@PathVariable("id") Long id, @Valid @RequestBody UpdateRequisitionOrderDTO updateDTO) {
        requisitionOrderService.update(id, updateDTO);
        return success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除领料单")
    @PreAuthorize("@ss.hasPermission('erp:requisition-order:delete')")
    @PermissionName(name = "删除领料单", sort = 3)
    public CommonResult<Boolean> delete(@PathVariable("id") Long id) {
        requisitionOrderService.delete(id);
        return success(true);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取领料单详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('erp:requisition-order:view')")
    @PermissionName(name = "查看领料单详情", sort = 4)
    public CommonResult<RequisitionOrderVO> get(@PathVariable("id") Long id) {
        return success(requisitionOrderService.get(id));
    }

    @GetMapping("/page")
    @Operation(summary = "查询领料单分页")
    @PreAuthorize("@ss.hasPermission('erp:requisition-order:query')")
    @PermissionName(name = "查询领料单-单据模式", sort = 8)
    public CommonResult<PageResult<SimpleRequisitionOrderVO>> page(@Valid SearchRequisitionOrderDTO searchDTO) {
        PageResult<SimpleRequisitionOrderVO> pageResult = requisitionOrderService.page(searchDTO);
        return success(pageResult);
    }

    @GetMapping("/item/page")
    @Operation(summary = "查询领料单明细分页")
    @PreAuthorize("@ss.hasPermission('erp:requisition-order:item:query')")
    @PermissionName(name = "查询领料单-明细模式", sort = 9)
    public CommonResult<PageResult<ItemWithRequisitionOrderVO>> pageItem(@Valid SearchItemPageDTO searchDTO) {
        PageResult<ItemWithRequisitionOrderVO> pageResult = requisitionOrderService.pageItem(searchDTO);
        return success(pageResult);
    }

    // ==================== 业务状态操作 ====================

    @PutMapping("/close/{id}")
    @Operation(summary = "关闭领料单")
    @PreAuthorize("@ss.hasPermission('erp:requisition-order:close')")
    @PermissionName(name = "关闭/取消关闭领料单", sort = 6)
    public CommonResult<Boolean> close(@PathVariable("id") Long id) {
        requisitionOrderService.close(id);
        return success(true);
    }

    @PutMapping("/cancel-close/{id}")
    @Operation(summary = "取消关闭领料单")
    @PreAuthorize("@ss.hasPermission('erp:requisition-order:close')")
    public CommonResult<Boolean> cancelClose(@PathVariable("id") Long id) {
        requisitionOrderService.cancelClose(id);
        return success(true);
    }

    @PutMapping("/suspend/{id}")
    @Operation(summary = "挂起领料单")
    @PreAuthorize("@ss.hasPermission('erp:requisition-order:suspend')")
    @PermissionName(name = "挂起/取消挂起领料单", sort = 7)
    public CommonResult<Boolean> suspend(@PathVariable("id") Long id) {
        requisitionOrderService.suspend(id);
        return success(true);
    }

    @PutMapping("/cancel-suspend/{id}")
    @Operation(summary = "取消挂起领料单")
    @PreAuthorize("@ss.hasPermission('erp:requisition-order:suspend')")
    public CommonResult<Boolean> cancelSuspend(@PathVariable("id") Long id) {
        requisitionOrderService.cancelSuspend(id);
        return success(true);
    }

    @PutMapping("/approve/{id}")
    @Operation(summary = "审核领料单")
    @PreAuthorize("@ss.hasPermission('erp:requisition-order:approve')")
    @PermissionName(name = "审核/取消审核领料单", sort = 5)
    public CommonResult<Boolean> approve(@PathVariable("id") Long id) {
        requisitionOrderService.approve(id);
        return success(true);
    }

    @PutMapping("/cancel-approve/{id}")
    @Operation(summary = "取消审核领料单")
    @PreAuthorize("@ss.hasPermission('erp:requisition-order:approve')")
    public CommonResult<Boolean> cancelApprove(@PathVariable("id") Long id) {
        requisitionOrderService.cancelApprove(id);
        return success(true);
    }

}
