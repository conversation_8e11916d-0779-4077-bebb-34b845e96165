package cn.iocoder.yudao.module.erp.controller.admin.abc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 采购退货出库单 VO")
@Data
@ExcelIgnoreUnannotated
public class PurchaseReturnedOutboundItemVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15640")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "出入库单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32103")
    @ExcelProperty("出入库单ID")
    private Long returnedOutboundId;

    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24255")
    @ExcelProperty("物料ID")
    private Long materialId;

    @Schema(description = "物料编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物料编码")
    private String materialCode;

    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("物料名称")
    private String materialName;

    @Schema(description = "规格型号")
    @ExcelProperty("规格型号")
    private String materialSpec;

    @Schema(description = "计量单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("计量单位")
    private String materialUom;

    @Schema(description = "出库数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出库数量")
    private BigDecimal outQuantity;

    @Schema(description = "批次号")
    @ExcelProperty("批次号")
    private String batchNumber;

    @Schema(description = "唯一码")
    @ExcelProperty("唯一码")
    private String uniqueCode;

    @Schema(description = "库位ID", example = "1798")
    @ExcelProperty("库位ID")
    private Long warehousePositionId;

    @Schema(description = "库位名称", example = "赵六")
    @ExcelProperty("库位名称")
    private String warehousePositionName;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

}