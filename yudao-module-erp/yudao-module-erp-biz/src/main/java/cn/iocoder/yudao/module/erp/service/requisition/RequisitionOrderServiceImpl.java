package cn.iocoder.yudao.module.erp.service.requisition;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.BusinessStatusEnum;
import cn.iocoder.yudao.module.erp.constant.BusinessTypeEnum;
import cn.iocoder.yudao.module.erp.constant.DocumentStatusEnum;
import cn.iocoder.yudao.module.erp.constant.LockTypeEnum;
import cn.iocoder.yudao.module.erp.controller.admin.requisition.vo.*;
import cn.iocoder.yudao.module.erp.convert.requisition.RequisitionOrderConvert;
import cn.iocoder.yudao.module.erp.convert.requisition.RequisitionOrderItemConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.requisition.RequisitionOrderDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.requisition.RequisitionOrderItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.requisition.view.ItemWithRequisitionOrder;
import cn.iocoder.yudao.module.erp.dal.mysql.requisition.RequisitionOrderItemMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.requisition.RequisitionOrderMapper;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataRefer;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataService;
import cn.iocoder.yudao.module.erp.service.common.AbstractDocumentAuditingService;
import cn.iocoder.yudao.module.erp.service.common.DocumentServiceUtil;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.*;

/**
 * 领料单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class RequisitionOrderServiceImpl extends AbstractDocumentAuditingService<RequisitionOrderItemDO>
        implements RequisitionOrderService, MaterialMasterDataRefer {

    @Resource
    private RequisitionOrderMapper requisitionOrderMapper;

    @Resource
    private RequisitionOrderItemMapper requisitionOrderItemMapper;

    @Resource
    private RequisitionOrderConvert requisitionOrderConvert;

    @Resource
    private RequisitionOrderItemConvert requisitionOrderItemConvert;

    @Resource
    private DocumentServiceUtil documentServiceUtil;

    @Resource
    @Lazy
    private MaterialMasterDataService materialMasterDataService;


    @Resource
    @Lazy
    private AdminUserApi adminUserApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(CreateRequisitionOrderDTO createDTO) {
        RequisitionOrderDO requisitionOrder = requisitionOrderConvert.toDO(createDTO);
        List<RequisitionOrderItemDO> items = requisitionOrderItemConvert.toDO(createDTO.getItems());
        // 验证业务逻辑
        commonValidate(requisitionOrder, items);

        // 插入主表
        documentServiceUtil.fillDocumentInfoWhenCreate(requisitionOrder, BusinessTypeEnum.REQUISITION_ORDER);
        requisitionOrderMapper.insert(requisitionOrder);

        // 插入明细表
        createRequisitionOrderItems(requisitionOrder.getId(), items);

        return requisitionOrder.getId();
    }

    private void createRequisitionOrderItems(Long requisitionOrderId, List<RequisitionOrderItemDO> items) {
        items.forEach(item -> {
            item.setRequisitionOrderId(requisitionOrderId);
        });
        requisitionOrderItemMapper.insertBatch(items);
    }

    private void commonValidate(RequisitionOrderDO requisitionOrder, List<RequisitionOrderItemDO> items) {
        // 验证部门和用户
        adminUserApi.validateUserAndDept(requisitionOrder.getBusinessUserId(), requisitionOrder.getBusinessDepartmentId()).checkError();

        // 校验明细中的物料
        documentServiceUtil.validateMaterialsFromItems(items, RequisitionOrderItemDO::getMaterialId);

        for (RequisitionOrderItemDO requisitionOrderItemDO : items) {
            if (requisitionOrderItemDO.getRequestQuantity().compareTo(requisitionOrderItemDO.getAvailableQuantity()) > 0) {
                throw exception(REQUISITION_ORDER_ITEM_REQUEST_QUANTITY_EXCEED_AVAILABLE, requisitionOrderItemDO.getRequestQuantity(), requisitionOrderItemDO.getAvailableQuantity());
            }
            if (requisitionOrderItemDO.getRequestQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                throw exception(REQUISITION_ORDER_ITEM_REQUEST_QUANTITY_NOT_POSITIVE, requisitionOrderItemDO.getRequestQuantity());
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Long id, UpdateRequisitionOrderDTO updateDTO) {
        // 验证存在
        RequisitionOrderDO requisitionOrder = validateRequisitionOrderExists(id);
        validateRequisitionOrderUpdatable(requisitionOrder);

        // 转换并更新主表
        RequisitionOrderDO updateObj = requisitionOrderConvert.toDO(updateDTO);
        updateObj.setId(id);
        List<RequisitionOrderItemDO> items = requisitionOrderItemConvert.toDO(updateDTO.getItems());

        // 验证业务逻辑
        commonValidate(updateObj, items);

        // 更新主表
        requisitionOrderMapper.updateById(updateObj);

        // 更新明细表
        updateRequisitionOrderItems(id, items);
    }

    private void updateRequisitionOrderItems(Long requisitionOrderId, List<RequisitionOrderItemDO> items) {
        // 删除旧的明细
        requisitionOrderItemMapper.deleteByRequisitionOrderId(requisitionOrderId);
        // 插入新的明细
        createRequisitionOrderItems(requisitionOrderId, items);
    }

    private void validateRequisitionOrderUpdatable(RequisitionOrderDO requisitionOrder) {
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(requisitionOrder.getDocumentStatus())) {
            throw exception(REQUISITION_ORDER_UPDATE_FAIL_APPROVE, requisitionOrder.getCode());
        }
    }

    private RequisitionOrderDO validateRequisitionOrderExists(Long id) {
        RequisitionOrderDO requisitionOrder = requisitionOrderMapper.selectById(id);
        if (requisitionOrder == null) {
            throw exception(REQUISITION_ORDER_NOT_EXISTS);
        }
        return requisitionOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        // 验证存在
        RequisitionOrderDO requisitionOrder = validateRequisitionOrderExists(id);
        validateRequisitionOrderDeletable(requisitionOrder);

        // 删除主表
        requisitionOrderMapper.deleteById(id);
        // 删除明细表
        requisitionOrderItemMapper.deleteByRequisitionOrderId(id);
    }

    private void validateRequisitionOrderDeletable(RequisitionOrderDO requisitionOrder) {
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(requisitionOrder.getDocumentStatus())) {
            throw exception(REQUISITION_ORDER_DELETE_FAIL_APPROVE, requisitionOrder.getCode());
        }
    }

    @Override
    public RequisitionOrderVO get(Long id) {
        RequisitionOrderDO requisitionOrder = validateRequisitionOrderExists(id);
        List<RequisitionOrderItemDO> items = requisitionOrderItemMapper.selectListByRequisitionOrderId(id);

        RequisitionOrderVO result = requisitionOrderConvert.toVO(requisitionOrder);
        result.setItems(requisitionOrderItemConvert.toVOList(items));
        return result;
    }

    @Override
    public PageResult<SimpleRequisitionOrderVO> page(SearchRequisitionOrderDTO searchDTO) {
        PageResult<RequisitionOrderDO> pageResult = requisitionOrderMapper.selectPage(searchDTO);
        return requisitionOrderConvert.toSimpleVOPage(pageResult);
    }

    @Override
    public PageResult<ItemWithRequisitionOrderVO> pageItem(SearchItemPageDTO searchDTO) {
        PageResult<ItemWithRequisitionOrder> pageResult = requisitionOrderItemMapper.selectItemPage(searchDTO);
        return requisitionOrderItemConvert.convertPage(pageResult);
    }

    // ==================== 业务状态操作 ====================

    @Override
    public void close(Long id) {
        RequisitionOrderDO requisitionOrder = validateRequisitionOrderExists(id);
        validateRequisitionOrderCloseable(requisitionOrder);
        requisitionOrderMapper.updateBusinessStatusById(BusinessStatusEnum.CLOSED.getStatus(), id);
    }

    private void validateRequisitionOrderCloseable(RequisitionOrderDO requisitionOrder) {
        if (BusinessStatusEnum.CLOSED.getStatus().equals(requisitionOrder.getBusinessStatus())) {
            throw exception(REQUISITION_ORDER_CLOSE_FAIL_STATUS_CLOSED, requisitionOrder.getCode());
        }
    }

    @Override
    public void cancelClose(Long id) {
        RequisitionOrderDO requisitionOrder = validateRequisitionOrderExists(id);
        validateRequisitionOrderCancelCloseable(requisitionOrder);
        requisitionOrderMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    private void validateRequisitionOrderCancelCloseable(RequisitionOrderDO requisitionOrder) {
        if (!BusinessStatusEnum.CLOSED.getStatus().equals(requisitionOrder.getBusinessStatus())) {
            throw exception(REQUISITION_ORDER_CANCEL_CLOSE_FAIL_STATUS_NOT_CLOSED, requisitionOrder.getCode());
        }
    }

    @Override
    public void suspend(Long id) {
        RequisitionOrderDO requisitionOrder = validateRequisitionOrderExists(id);
        validateRequisitionOrderSuspendable(requisitionOrder);
        requisitionOrderMapper.updateBusinessStatusById(BusinessStatusEnum.SUSPENDED.getStatus(), id);
    }

    private void validateRequisitionOrderSuspendable(RequisitionOrderDO requisitionOrder) {
        if (BusinessStatusEnum.SUSPENDED.getStatus().equals(requisitionOrder.getBusinessStatus())) {
            throw exception(REQUISITION_ORDER_SUSPEND_FAIL_STATUS_SUSPENDED, requisitionOrder.getCode());
        }
    }

    @Override
    public void cancelSuspend(Long id) {
        RequisitionOrderDO requisitionOrder = validateRequisitionOrderExists(id);
        validateRequisitionOrderCancelSuspendable(requisitionOrder);
        requisitionOrderMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    private void validateRequisitionOrderCancelSuspendable(RequisitionOrderDO requisitionOrder) {
        if (!BusinessStatusEnum.SUSPENDED.getStatus().equals(requisitionOrder.getBusinessStatus())) {
            throw exception(REQUISITION_ORDER_CANCEL_SUSPEND_FAIL_STATUS_NOT_SUSPENDED, requisitionOrder.getCode());
        }
    }

    // 审核和取消审核方法由父类 AbstractDocumentAuditingService 提供

    // ==================== AbstractDocumentAuditingService 抽象方法实现 ====================

    @Override
    protected void doApprove(Long id, List<RequisitionOrderItemDO> items) {

        // 这里可以添加库存扣减等逻辑
        log.info("领料单审核通过，ID: {}", id);
    }

    @Override
    protected void doCancelApprove(Long id, List<RequisitionOrderItemDO> items) {
        // 领料单取消审核时的业务逻辑
        // 这里可以添加库存恢复等逻辑
        log.info("领料单取消审核，ID: {}", id);
    }

    @Override
    protected void validateBeforeApprove(Long id) {
        RequisitionOrderDO requisitionOrder = validateRequisitionOrderExists(id);
        validateRequisitionOrderApprovable(requisitionOrder);
    }

    private void validateRequisitionOrderApprovable(RequisitionOrderDO requisitionOrder) {
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(requisitionOrder.getDocumentStatus())) {
            throw exception(REQUISITION_ORDER_APPROVE_FAIL_STATUS_NOT_DRAFT, requisitionOrder.getCode());
        }
    }

    @Override
    protected void validateBeforeCancelApprove(Long id) {
        RequisitionOrderDO requisitionOrder = validateRequisitionOrderExists(id);
        validateRequisitionOrderCancelApprovable(requisitionOrder);
    }

    private void validateRequisitionOrderCancelApprovable(RequisitionOrderDO requisitionOrder) {
        if (!DocumentStatusEnum.APPROVE.getStatus().equals(requisitionOrder.getDocumentStatus())) {
            throw exception(REQUISITION_ORDER_CANCEL_APPROVE_FAIL_STATUS_NOT_APPROVE, requisitionOrder.getCode());
        }
    }

    @Override
    protected List<RequisitionOrderItemDO> getDocumentItemsWhenAuditing(Long id) {
        return requisitionOrderItemMapper.selectListByRequisitionOrderId(id);
    }

    @Override
    protected List<Long> getNeedLockedIds(List<RequisitionOrderItemDO> items) {
        // 领料单可能需要锁定物料或库存，这里返回需要锁定的ID列表
        return items.stream()
                .map(RequisitionOrderItemDO::getMaterialId)
                .distinct()
                .toList();
    }

    @Override
    protected void updateDocumentApproveStatus(Long id, Integer status) {
        requisitionOrderMapper.updateDocumentApproveStatusById(id, status);
    }

    @Override
    @PostConstruct
    protected void initLockTypeEnum() {
        this.documentLockType = LockTypeEnum.REQUISITION_ORDER;
        this.itemLockType = LockTypeEnum.REQUISITION_ORDER_ITEM;
    }

    // ==================== 子表（领料单明细） ====================

    @Override
    public List<RequisitionOrderItemDO> getRequisitionOrderItemListByRequisitionOrderId(Long requisitionOrderId) {
        return requisitionOrderItemMapper.selectListByRequisitionOrderId(requisitionOrderId);
    }

    // ==================== MaterialMasterDataRefer 接口实现 ====================

    @Override
    public void validateWhenMaterialDelete(Long materialId) {
        Long count = requisitionOrderItemMapper.countByMaterialId(materialId);
        if (count > 0) {
            throw exception(REQUISITION_ORDER_ITEM_REFER_MATERIAL_EXITS, materialId);
        }
    }

}
