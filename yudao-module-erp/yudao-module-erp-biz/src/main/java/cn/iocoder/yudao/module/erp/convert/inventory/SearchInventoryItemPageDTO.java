package cn.iocoder.yudao.module.erp.convert.inventory;

import cn.iocoder.yudao.module.erp.controller.admin.common.vo.InventoryItemSearchDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 出入库Item查询的全量参数
 * 这样从各个特定业务查询时可以做参数对应
 * 比如 生产入库查询填充人员
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchInventoryItemPageDTO extends InventoryItemSearchDTO {

    @Schema(description = "出入库清单ID", example = "1024")
    private Long billId;

    @Schema(description = "单据编号", example = "DH202401001")
    private String code;

    @Schema(description = "仓库ID", example = "1")
    private Long warehouseId;

    @Schema(description = "单据状态", example = "1")
    private Integer documentStatus;

    @Schema(description = "物料编码", example = "M001")
    private String materialCode;

    @Schema(description = "物料名称", example = "钢材")
    private String materialName;

    @Schema(description = "批次号", example = "B001")
    private String batchNo;

    @Schema(description = "唯一码", example = "U001")
    private String uniqueCode;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] createTime;

}
