package cn.iocoder.yudao.module.erp.dal.dataobject.abc;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 通用库存单据明细 DO
 *
 * <AUTHOR>
 */
@TableName("erp_inventory_bill_item")
@KeySequence("erp_inventory_bill_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBillItemDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 出入库单ID
     */
    private Long billId;
    /**
     * 物料ID
     */
    private Long materialId;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 规格型号
     */
    private String materialSpec;
    /**
     * 计量单位
     */
    private String materialUom;
    /**
     * 入库数量
     */
    private BigDecimal inQuantity;
    /**
     * 出库数量
     */
    private BigDecimal outQuantity;
    /**
     * 批次号
     */
    private String batchNumber;
    /**
     * 唯一码
     */
    private String uniqueCode;
    /**
     * 库位ID
     */
    private Long warehousePositionId;
    /**
     * 库位名称
     */
    private String warehousePositionName;
    /**
     * 来源单据类型
     */
    private String sourceType;
    /**
     * 来源单据ID
     */
    private Long sourceId;
    /**
     * 来源单据code
     */
    private String sourceCode;
    /**
     * 来源单据ItemID
     */
    private Long sourceItemId;
    /**
     * 包装类型id
     */
    private Long packagingTypeId;
    /**
     * 包装类型名称
     */
    private String packagingTypeName;
    /**
     * 包装规格id
     */
    private Long packagingSpecId;
    /**
     * 包装规格名称
     */
    private String packagingSpecName;
    /**
     * 包装物所属公司id，从customer中获取
     */
    private Long packagingCompanyId;
    /**
     * 包装物所属公司名称
     */
    private String packagingCompanyName;
    /**
     * 包装物归属
     */
    private String packagingOwner;
    /**
     * 包装码
     */
    private String packagingCode;
    /**
     * 挂牌车号
     */
    private String plateNumber;
    /**
     * 毛重
     */
    private BigDecimal grossWeight;
    /**
     * 空重
     */
    private BigDecimal tareWeight;
    /**
     * 充装日期
     */
    private LocalDate fillingDate;
    /**
     * 充装人id
     */
    private Long fillingUserId;
    /**
     * 充装人姓名
     */
    private String fillingUserName;
    /**
     * 已退货数量
     */
    private BigDecimal retunedQuantity;
    /**
     * 可退货数量：入库数量-已退货数量-已使用数量
     */
    private BigDecimal returnableQuantity;
    /**
     * 已使用数量:领料出库单审核通过时代表已使用
     */
    private BigDecimal usedQuantity;
    /**
     * 备注
     */
    private String remark;

}