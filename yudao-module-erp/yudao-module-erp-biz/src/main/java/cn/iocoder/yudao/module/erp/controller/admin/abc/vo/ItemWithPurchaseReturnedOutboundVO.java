package cn.iocoder.yudao.module.erp.controller.admin.abc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 采购退货出库单明细与主表组合 Response VO")
@Data
public class ItemWithPurchaseReturnedOutboundVO extends PurchaseReturnedOutboundItemVO {
    
    @Schema(description = "采购退货出库单信息")
    private SimplePurchaseReturnedOutboundVO purchaseReturnedOutbound;
    
}
