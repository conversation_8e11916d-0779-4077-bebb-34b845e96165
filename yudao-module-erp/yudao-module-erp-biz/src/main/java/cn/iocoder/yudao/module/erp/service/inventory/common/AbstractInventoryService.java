package cn.iocoder.yudao.module.erp.service.inventory.common;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.BusinessTypeEnum;
import cn.iocoder.yudao.module.erp.constant.DocumentStatusEnum;
import cn.iocoder.yudao.module.erp.controller.admin.inventory.common.CommonInventoryDocumentDTO;
import cn.iocoder.yudao.module.erp.controller.admin.inventory.common.CommonInventoryDocumentVO;
import cn.iocoder.yudao.module.erp.convert.inventory.SearchInventoryBillDTO;
import cn.iocoder.yudao.module.erp.convert.inventory.SearchInventoryItemPageDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryBillDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryBillItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.view.ItemWithInventoryBill;
import cn.iocoder.yudao.module.erp.dal.mysql.inventory.InventoryBillItemMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.inventory.InventoryBillMapper;
import cn.iocoder.yudao.module.erp.service.common.DocumentServiceUtil;
import cn.iocoder.yudao.module.erp.service.warehouse.WarehouseService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.INVENTORY_BILL_NOT_EDITABLE;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.INVENTORY_BILL_NOT_EXISTS;

public abstract class AbstractInventoryService<C extends CommonInventoryDocumentDTO, VO extends CommonInventoryDocumentVO> {

    @Resource
    private InventoryBillMapper billMapper;

    @Resource
    private InventoryBillItemMapper itemMapper;

    @Resource
    private DocumentServiceUtil documentServiceUtil;

    @Resource
    @Lazy
    private DeptApi deptApi;

    @Resource
    @Lazy
    private AdminUserApi adminUserApi;

    @Resource
    private WarehouseService warehouseService;


    @Transactional(rollbackFor = Exception.class)
    public Long create(C createDTO) {
        InventoryBillDO billDO = getBillDO(createDTO);
        List<InventoryBillItemDO> billItemDOS = getBillItemDO(createDTO);
        commonValidate(billDO, billItemDOS);

        documentServiceUtil.fillDocumentInfoWhenCreate(billDO, getBusinessTypeEnum());
        billDO.setDocumentType(getBusinessTypeEnum().getDocumentTypeEnum().getValue());

        fillName(billDO);
        billMapper.insert(billDO);
        doCreateItems(billDO.getId(), billItemDOS);
        return billDO.getId();
    }

    public void update(Long documentId, C updateDTO) {
        InventoryBillDO existBillDO = validateBillExists(documentId);
        validateBillEditable(existBillDO);
        InventoryBillDO billDO = getBillDO(updateDTO);
        List<InventoryBillItemDO> billItemDOS = getBillItemDO(updateDTO);
        commonValidate(billDO, billItemDOS);

        billDO.setId(documentId);
        fillName(billDO);
        billMapper.updateById(billDO);

        //处理明细表 - 分为新增、更新、删除三类
        Map<Boolean, List<InventoryBillItemDO>> partitionedItems = billItemDOS.stream()
                .collect(Collectors.partitioningBy(item -> item.getId() == null));

        // 获取现有明细项
        List<InventoryBillItemDO> existItemList = itemMapper.selectListByBillId(documentId);

        // 处理新增项
        List<InventoryBillItemDO> newItemList = partitionedItems.get(true);
        if (CollUtil.isNotEmpty(newItemList)) {
            doCreateItems(documentId, newItemList);
        }

        // 处理更新项
        List<InventoryBillItemDO> updateItemList = partitionedItems.get(false);
        if (CollUtil.isNotEmpty(updateItemList)) {
            doUpdateItemList(documentId, updateItemList);
        }

        // 处理删除项：找出存在于旧列表但不在更新列表中的项
        Set<Long> updateItemIds = updateItemList.stream()
                .map(InventoryBillItemDO::getId)
                .collect(Collectors.toSet());
        Set<Long> deleteItemIds = existItemList.stream()
                .map(InventoryBillItemDO::getId)
                .filter(itemId -> !updateItemIds.contains(itemId))
                .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(deleteItemIds)) {
            itemMapper.deleteByIds(deleteItemIds);
        }
    }

    public void delete(Long id) {
        InventoryBillDO billDO = validateBillExists(id);
        validateBillEditable(billDO);
        billMapper.deleteById(id);
        itemMapper.deleteByBillId(id);
    }

    /**
     * 获得出库单详情
     *
     * @param id Id
     * @return 采购退货出库单
     */
    public VO get(Long id) {
        InventoryBillDO billDO = validateBillExists(id);
        List<InventoryBillItemDO> items = itemMapper.selectListByBillId(id);

        return buildVO(billDO, items);
    }

    protected abstract VO buildVO(InventoryBillDO billDO, List<InventoryBillItemDO> items);

    private void doUpdateItemList(Long documentId, List<InventoryBillItemDO> updateItemList) {
        updateItemList.forEach(item -> {
            item.setBillId(documentId);
        });
        itemMapper.updateBatch(updateItemList);
    }

    private void doCreateItems(Long documentId, List<InventoryBillItemDO> billItemDOS) {
        billItemDOS.forEach(item -> {
            item.setBillId(documentId);
        });
        itemMapper.insertBatch(billItemDOS);
    }

    private void validateBillEditable(InventoryBillDO existBillDO) {
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(existBillDO.getDocumentStatus()) &&
                !DocumentStatusEnum.ROLLBACK.getStatus().equals(existBillDO.getDocumentStatus())) {
            throw exception(INVENTORY_BILL_NOT_EDITABLE);
        }
    }

    private InventoryBillDO validateBillExists(Long documentId) {
        InventoryBillDO billDO = billMapper.selectById(documentId);
        if (billDO == null) {
            throw exception(INVENTORY_BILL_NOT_EXISTS);
        }
        return billDO;
    }


    public void approve(Long id) {

    }

    /**
     * 取消审核采购退货出库单
     *
     * @param id Id
     */
    public void cancelApprove(Long id) {

    }

    protected void fillName(InventoryBillDO billDO) {
        if (billDO.getBusinessDepartmentId() != null) {
            DeptRespDTO dept = deptApi.getDept(billDO.getBusinessDepartmentId()).getCheckedData();
            if (dept != null) {
                billDO.setBusinessDepartmentName(dept.getName());
            }
        }
        if (billDO.getBusinessUserId() != null) {
            billDO.setBusinessUserName(adminUserApi.getUser(billDO.getBusinessUserId()).getCheckedData().getNickname());
        }
        if (billDO.getWarehouseId() != null) {
            billDO.setWarehouseName(warehouseService.getWarehouse(billDO.getWarehouseId()).getName());
        }

    }


    protected abstract BusinessTypeEnum getBusinessTypeEnum();

    //通用验证逻辑
    protected void commonValidate(InventoryBillDO billDO, List<InventoryBillItemDO> billItemDOS) {

    }
    
    protected abstract List<InventoryBillItemDO> getBillItemDO(C createDTO);

    protected abstract InventoryBillDO getBillDO(C createDTO);


    protected PageResult<InventoryBillDO> page(SearchInventoryBillDTO searchInventoryBillDTO) {
        return billMapper.selectPage(searchInventoryBillDTO);
    }

    protected PageResult<ItemWithInventoryBill> pageItem(SearchInventoryItemPageDTO searchInventoryItemPageDTO) {
        return itemMapper.selectItemPage(searchInventoryItemPageDTO);
    }
}
