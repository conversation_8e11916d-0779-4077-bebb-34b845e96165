package cn.iocoder.yudao.module.erp.dal.mysql.purchase.arrival;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.arrival.vo.SearchPurchaseArrivalDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.arrival.PurchaseArrivalDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 采购到货单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseArrivalMapper extends BaseMapperX<PurchaseArrivalDO> {

    default PageResult<PurchaseArrivalDO> selectPage(SearchPurchaseArrivalDTO searchDTO) {
        return selectPage(searchDTO, new LambdaQueryWrapperX<PurchaseArrivalDO>()
                .likeIfPresent(PurchaseArrivalDO::getCode, searchDTO.getCode())
                .eqIfPresent(PurchaseArrivalDO::getSupplierId, searchDTO.getSupplierId())
                .likeIfPresent(PurchaseArrivalDO::getSupplierName, searchDTO.getSupplierName())
                .eqIfPresent(PurchaseArrivalDO::getMaterialType, searchDTO.getMaterialType())
                .eqIfPresent(PurchaseArrivalDO::getBusinessDepartmentId, searchDTO.getBusinessDepartmentId())
                .eqIfPresent(PurchaseArrivalDO::getBusinessUserId, searchDTO.getBusinessUserId())
                .eqIfPresent(PurchaseArrivalDO::getWarehouseId, searchDTO.getWarehouseId())
                .likeIfPresent(PurchaseArrivalDO::getWarehouseName, searchDTO.getWarehouseName())
                .eqIfPresent(PurchaseArrivalDO::getDocumentStatus, searchDTO.getDocumentStatus())
                .eqIfPresent(PurchaseArrivalDO::getBusinessStatus, searchDTO.getBusinessStatus())
                .betweenIfPresent(PurchaseArrivalDO::getArrivalDate, searchDTO.getArrivalDate())
                .betweenIfPresent(PurchaseArrivalDO::getCreateTime, searchDTO.getCreateTime())
                .openBetweenClose(PurchaseArrivalDO::getUnconfirmedInboundQuantity, searchDTO.getUnconfirmedInboundQuantity())
                .orderByDesc(PurchaseArrivalDO::getId));
    }

    default void updateBusinessStatusById(Integer status, Long id) {
        PurchaseArrivalDO updateObj = new PurchaseArrivalDO();
        updateObj.setId(id);
        updateObj.setBusinessStatus(status);
        updateById(updateObj);
    }

    default void updateDocumentApproveStatusById(Long id, Integer status) {
        PurchaseArrivalDO updateObj = new PurchaseArrivalDO();
        updateObj.setId(id);
        updateObj.setDocumentStatus(status);
        updateObj.setAuditTime(LocalDateTime.now());
        updateById(updateObj);
    }

    default void updateUnconfirmedInboundQuantity(Long id, BigDecimal unconfirmedInboundQuantity) {
        update(new LambdaUpdateWrapper<PurchaseArrivalDO>()
                .set(PurchaseArrivalDO::getUnconfirmedInboundQuantity, unconfirmedInboundQuantity)
                .eq(PurchaseArrivalDO::getId, id));
    }

    default Long countBySupplierId(Long supplierId) {
        return selectCount(PurchaseArrivalDO::getSupplierId, supplierId);
    }
}
