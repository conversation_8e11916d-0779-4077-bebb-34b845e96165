package cn.iocoder.yudao.module.erp.controller.admin.inventory.common;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 通用库存单据 DTO
 */
@Data
public class CommonInventoryDocumentDTO {
    
    @Schema(description = "仓库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31837")
    @NotNull(message = "仓库ID不能为空")
    private Long warehouseId;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "仓库名称不能为空")
    private String warehouseName;

    @Schema(description = "业务部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13559")
    @NotNull(message = "业务部门ID不能为空")
    private Long businessDepartmentId;

    @Schema(description = "业务部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String businessDepartmentName;

    @Schema(description = "业务员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28466")
    @NotNull(message = "业务员ID不能为空")
    private Long businessUserId;

    @Schema(description = "业务员姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String businessUserName;

}
