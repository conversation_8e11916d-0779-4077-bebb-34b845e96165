package cn.iocoder.yudao.module.erp.service.purchase.returned;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.BusinessStatusEnum;
import cn.iocoder.yudao.module.erp.constant.BusinessTypeEnum;
import cn.iocoder.yudao.module.erp.constant.DocumentStatusEnum;
import cn.iocoder.yudao.module.erp.constant.LockTypeEnum;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.returned.vo.*;
import cn.iocoder.yudao.module.erp.convert.purchase.returned.PurchaseReturnedConvert;
import cn.iocoder.yudao.module.erp.convert.purchase.returned.PurchaseReturnedItemConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.returned.PurchaseReturnedDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.returned.PurchaseReturnedItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.returned.PurchaseReturnedQuantity;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.returned.view.ItemWithPurchaseReturned;
import cn.iocoder.yudao.module.erp.dal.mysql.purchase.returned.PurchaseReturnedItemMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.purchase.returned.PurchaseReturnedMapper;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataService;
import cn.iocoder.yudao.module.erp.service.common.AbstractDocumentAuditingService;
import cn.iocoder.yudao.module.erp.service.common.DocumentRelationHelper;
import cn.iocoder.yudao.module.erp.service.common.DocumentServiceUtil;
import cn.iocoder.yudao.module.erp.service.inbound.purchase.PurchaseInboundService;
import cn.iocoder.yudao.module.erp.service.supplier.SupplierService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.*;

/**
 * 采购退货通知单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseReturnedServiceImpl extends AbstractDocumentAuditingService<PurchaseReturnedItemDO>
        implements PurchaseReturnedService {

    @Resource
    private PurchaseReturnedMapper purchaseReturnedMapper;

    @Resource
    private PurchaseReturnedItemMapper purchaseReturnedItemMapper;

    @Resource
    private PurchaseReturnedItemConvert purchaseReturnedItemConvert;

    @Resource
    private DocumentServiceUtil documentServiceUtil;
    @Resource
    private PurchaseReturnedConvert purchaseReturnedConvert;

    @Resource
    @Lazy
    private AdminUserApi adminUserApi;

    @Resource
    @Lazy
    private SupplierService supplierService;

    @Resource
    @Lazy
    private DeptApi deptApi;

    @Resource
    @Lazy
    private DocumentRelationHelper documentRelationHelper;

    @Resource
    @Lazy
    private MaterialMasterDataService materialMasterDataService;

    @Resource
    @Lazy
    private PurchaseInboundService purchaseInboundService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(CreatePurchaseReturnedDTO createDTO) {
        // 1. 转换DO
        PurchaseReturnedDO purchaseReturned = purchaseReturnedConvert.toDO(createDTO);
        List<PurchaseReturnedItemDO> items = purchaseReturnedItemConvert.toDO(createDTO.getItems());

        // 2. 验证业务逻辑
        commonValidate(purchaseReturned, items);

        // 3. 插入主表
        documentServiceUtil.fillDocumentInfoWhenCreate(purchaseReturned, BusinessTypeEnum.PURCHASE_RETURNED);
        fillName(purchaseReturned);
        purchaseReturnedMapper.insert(purchaseReturned);

        // 4. 插入明细表
        doCreatePurchaseReturnedItems(purchaseReturned.getId(), items);

        return purchaseReturned.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Long id, UpdatePurchaseReturnedDTO updateDTO) {
        // 1. 验证存在
        PurchaseReturnedDO purchaseReturned = validatePurchaseReturnedExists(id);
        // 2. 验证可编辑
        validatePurchaseReturnedEditable(purchaseReturned);

        // 3. 转换DO
        PurchaseReturnedDO updateObj = purchaseReturnedConvert.toDO(updateDTO);
        List<PurchaseReturnedItemDO> updateItems = purchaseReturnedItemConvert.toDO(updateDTO.getItems());

        // 4. 验证业务逻辑
        commonValidate(updateObj, updateItems);

        // 5. 更新主表
        updateObj.setId(id);
        fillName(updateObj);
        purchaseReturnedMapper.updateById(updateObj);

        // 6. 处理明细表 - 分为新增、更新、删除三类
        Map<Boolean, List<PurchaseReturnedItemDO>> partitionedItems = updateItems.stream()
                .collect(Collectors.partitioningBy(item -> item.getId() == null));

        // 获取现有明细项
        List<PurchaseReturnedItemDO> existItemList = purchaseReturnedItemMapper.selectListByPurchaseReturnedId(id);

        // 处理新增项
        List<PurchaseReturnedItemDO> newItemList = partitionedItems.get(true);
        if (CollUtil.isNotEmpty(newItemList)) {
            doCreatePurchaseReturnedItems(id, newItemList);
        }

        // 处理更新项
        List<PurchaseReturnedItemDO> updateItemList = partitionedItems.get(false);
        if (CollUtil.isNotEmpty(updateItemList)) {
            doUpdatePurchaseReturnedItemList(id, updateItemList);
        }

        // 处理删除项：找出存在于旧列表但不在更新列表中的项
        Set<Long> updateItemIds = updateItemList.stream()
                .map(PurchaseReturnedItemDO::getId)
                .collect(Collectors.toSet());
        Set<Long> deleteItemIds = existItemList.stream()
                .map(PurchaseReturnedItemDO::getId)
                .filter(itemId -> !updateItemIds.contains(itemId))
                .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(deleteItemIds)) {
            purchaseReturnedItemMapper.deleteByIds(deleteItemIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        // 验证存在
        PurchaseReturnedDO purchaseReturned = validatePurchaseReturnedExists(id);
        // 验证可删除
        validatePurchaseReturnedEditable(purchaseReturned);

        // 删除主表
        purchaseReturnedMapper.deleteById(id);
        // 删除明细表
        purchaseReturnedItemMapper.deleteByPurchaseReturnedId(id);
    }

    @Override
    public PurchaseReturnedVO get(Long id) {
        PurchaseReturnedDO purchaseReturned = validatePurchaseReturnedExists(id);
        List<PurchaseReturnedItemDO> items = purchaseReturnedItemMapper.selectListByPurchaseReturnedId(id);

        PurchaseReturnedVO result = purchaseReturnedConvert.toVO(purchaseReturned);
        result.setItems(purchaseReturnedItemConvert.toVO(items));
        return result;
    }

    @Override
    public PageResult<SimplePurchaseReturnedVO> page(SearchPurchaseReturnedDTO searchDTO) {
        PageResult<PurchaseReturnedDO> page = purchaseReturnedMapper.selectPage(searchDTO);
        List<PurchaseReturnedDO> list = page.getList();
        List<SimplePurchaseReturnedVO> listVO = purchaseReturnedConvert.toSimpleVO(list);
        return new PageResult<>(listVO, page.getTotal());
    }
    // ==================== 文档操作方法实现 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void close(Long id) {
        PurchaseReturnedDO purchaseReturned = validatePurchaseReturnedExists(id);
        validateBusinessStatus(purchaseReturned, BusinessStatusEnum.NORMAL.getStatus());
        purchaseReturnedMapper.updateBusinessStatusById(BusinessStatusEnum.CLOSED.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelClose(Long id) {
        PurchaseReturnedDO purchaseReturned = validatePurchaseReturnedExists(id);
        validateBusinessStatus(purchaseReturned, BusinessStatusEnum.CLOSED.getStatus());
        purchaseReturnedMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspend(Long id) {
        PurchaseReturnedDO purchaseReturned = validatePurchaseReturnedExists(id);
        validateBusinessStatus(purchaseReturned, BusinessStatusEnum.NORMAL.getStatus());
        purchaseReturnedMapper.updateBusinessStatusById(BusinessStatusEnum.SUSPENDED.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSuspend(Long id) {
        PurchaseReturnedDO purchaseReturned = validatePurchaseReturnedExists(id);
        validateBusinessStatus(purchaseReturned, BusinessStatusEnum.SUSPENDED.getStatus());
        purchaseReturnedMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    @Override
    protected void validateBeforeApprove(Long id) {
        commonValidate(id);
    }


    @Override
    protected void doApprove(Long id, List<PurchaseReturnedItemDO> items) {
        initOutboundQuantity(id);
        //更新采购入库单的已退货数量
        List<PurchaseReturnedQuantity> returnedQuantityList = items.stream()
                .filter(item -> item.getPurchaseInboundItemId() != null)
                .map(item -> new PurchaseReturnedQuantity(item.getId(), item.getPurchaseInboundItemId(), item.getReturnedQuantity()))
                .toList();
        if (!returnedQuantityList.isEmpty()) {
            documentRelationHelper.tryUpdateReturnedQuantityWhenPurchaseReturnedApprove(returnedQuantityList);
        }
        // 更新状态
        purchaseReturnedMapper.updateDocumentStatusById(DocumentStatusEnum.APPROVE.getStatus(), id);

    }

    @Override
    protected List<PurchaseReturnedItemDO> getDocumentItemsWhenAuditing(Long id) {
        return purchaseReturnedItemMapper.selectListByPurchaseReturnedId(id);
    }

    @Override
    protected List<Long> getNeedLockedIds(List<PurchaseReturnedItemDO> purchaseReturnedItemDOS) {
        return purchaseReturnedItemDOS.stream()
                .map(PurchaseReturnedItemDO::getPurchaseInboundItemId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
    }

    @Override
    protected void updateDocumentApproveStatus(Long id, Integer status) {
        purchaseReturnedMapper.updateDocumentStatusById(status, id);
    }

    @Override
    @PostConstruct
    protected void initLockTypeEnum() {
        this.documentLockType = LockTypeEnum.PURCHASE_RETURNED;
        this.itemLockType = LockTypeEnum.PURCHASE_RETURNED_ITEM;
    }

    private void initOutboundQuantity(Long id) {
        purchaseReturnedItemMapper.initOutboundQuantity(id);
    }

    @Override
    protected void validateBeforeCancelApprove(Long id) {
        PurchaseReturnedDO purchaseReturned = validatePurchaseReturnedExists(id);
        validatePurchaseReturnedCancelApprovable(purchaseReturned);
        documentRelationHelper.validatePurchaseReturnedCanCancelApprove(id);
    }

    @Override
    protected void doCancelApprove(Long id, List<PurchaseReturnedItemDO> items) {
        List<PurchaseReturnedQuantity> returnedQuantityList = items.stream()
                .filter(item -> item.getPurchaseInboundItemId() != null)
                .map(item -> new PurchaseReturnedQuantity(item.getId(), item.getPurchaseInboundItemId(), item.getReturnedQuantity()))
                .toList();
        if (!returnedQuantityList.isEmpty()) {
            documentRelationHelper.updateReturnedQuantityWhenPurchaseReturnedCancelApprove(returnedQuantityList);
        }
        // 更新状态
        purchaseReturnedMapper.updateDocumentStatusById(DocumentStatusEnum.DRAFT.getStatus(), id);
    }


    @Override
    public PageResult<ItemWithPurchaseReturnedVO> pageItem(SearchItemPageDTO searchDTO) {
        PageResult<ItemWithPurchaseReturned> pageResult = purchaseReturnedItemMapper.selectItemPage(searchDTO);
        return purchaseReturnedItemConvert.convertPage(pageResult);
    }

    private PurchaseReturnedDO validatePurchaseReturnedExists(Long id) {
        PurchaseReturnedDO purchaseReturned = purchaseReturnedMapper.selectById(id);
        if (purchaseReturned == null) {
            throw exception(PURCHASE_RETURNED_NOT_EXISTS);
        }
        return purchaseReturned;
    }

    private void validatePurchaseReturnedEditable(PurchaseReturnedDO purchaseReturned) {
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(purchaseReturned.getDocumentStatus()) &&
                !DocumentStatusEnum.ROLLBACK.getStatus().equals(purchaseReturned.getDocumentStatus())) {
            throw exception(PURCHASE_RETURNED_NOT_EDITABLE);
        }
    }

    private void validatePurchaseReturnedApprovable(PurchaseReturnedDO purchaseReturned) {
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(purchaseReturned.getDocumentStatus()) &&
                !DocumentStatusEnum.ROLLBACK.getStatus().equals(purchaseReturned.getDocumentStatus())) {
            throw exception(PURCHASE_RETURNED_NOT_APPROVABLE);
        }
    }

    private void validatePurchaseReturnedCancelApprovable(PurchaseReturnedDO purchaseReturned) {
        if (!DocumentStatusEnum.APPROVE.getStatus().equals(purchaseReturned.getDocumentStatus())) {
            throw exception(PURCHASE_RETURNED_NOT_CANCEL_APPROVABLE);
        }
    }

    private void validateBusinessStatus(PurchaseReturnedDO purchaseReturned, Integer status) {
        if (!Objects.equals(purchaseReturned.getBusinessStatus(), status)) {
            throw exception(PURCHASE_RETURNED_BUSINESS_STATUS_NOT_SUPPORT_CHANGE, purchaseReturned.getCode());
        }
    }

    private void commonValidate(Long id) {
        PurchaseReturnedDO purchaseReturned = validatePurchaseReturnedExists(id);
        validatePurchaseReturnedApprovable(purchaseReturned);
        List<PurchaseReturnedItemDO> items = purchaseReturnedItemMapper.selectListByPurchaseReturnedId(id);
        commonValidate(purchaseReturned, items);
    }

    private void commonValidate(PurchaseReturnedDO purchaseReturned, List<PurchaseReturnedItemDO> items) {
        adminUserApi.validateUserAndDept(purchaseReturned.getBusinessUserId(), purchaseReturned.getBusinessDepartmentId()).checkError();
        supplierService.validateExists(purchaseReturned.getSupplierId());
        //验证items 到货单号一致
        Set<Long> purchaseArrivalIds = items.stream().map(PurchaseReturnedItemDO::getPurchaseArrivalId).collect(Collectors.toSet());
        if (purchaseArrivalIds.size() != 1) {
            throw exception(PURCHASE_RETURNED_ITEMS_NOT_SAME_PURCHASE_ARRIVAL);
        }
        //验证同一个仓库
        Set<Long> warehouseIds = items.stream().map(PurchaseReturnedItemDO::getWarehouseId).collect(Collectors.toSet());
        if (warehouseIds.size() != 1) {
            throw exception(PURCHASE_RETURNED_ITEMS_NOT_SAME_WAREHOUSE);
        }
        //验证物料存在：收集所有的物料Id，验证所有的物料都存在
        Set<Long> materialIds = items.stream().map(PurchaseReturnedItemDO::getMaterialId).collect(Collectors.toSet());
        materialMasterDataService.validateExist(new ArrayList<>(materialIds));
        //验证退货数量不能大于未退货数量
        items.forEach(item -> {
            if (item.getReturnedQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                throw exception(PURCHASE_RETURNED_ITEM_RETURNED_QUANTITY_NOT_POSITIVE, item.getReturnedQuantity());
            }
            purchaseInboundService.validateReturnedQuantity(item.getPurchaseInboundItemId(), item.getReturnedQuantity());
        });

    }

    private void fillName(PurchaseReturnedDO purchaseReturned) {
        if (purchaseReturned.getBusinessDepartmentId() != null) {
            DeptRespDTO dept = deptApi.getDept(purchaseReturned.getBusinessDepartmentId()).getCheckedData();
            if (dept != null) {
                purchaseReturned.setBusinessDepartmentName(dept.getName());
            }
        }
        if (purchaseReturned.getSupplierId() != null) {
            purchaseReturned.setSupplierName(supplierService.get(purchaseReturned.getSupplierId()).getName());
        }
    }

    private void doCreatePurchaseReturnedItems(Long purchaseReturnedId, List<PurchaseReturnedItemDO> items) {
        items.forEach(item -> {
            item.setPurchaseReturnedId(purchaseReturnedId);
        });
        purchaseReturnedItemMapper.insertBatch(items);
    }

    private void doUpdatePurchaseReturnedItemList(Long purchaseReturnedId, List<PurchaseReturnedItemDO> items) {
        items.forEach(item -> {
            item.setPurchaseReturnedId(purchaseReturnedId);
        });
        purchaseReturnedItemMapper.updateBatch(items);
    }

}