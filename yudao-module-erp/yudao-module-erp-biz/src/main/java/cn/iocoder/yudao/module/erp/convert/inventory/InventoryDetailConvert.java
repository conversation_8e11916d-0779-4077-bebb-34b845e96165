package cn.iocoder.yudao.module.erp.convert.inventory;


import cn.iocoder.yudao.module.erp.controller.admin.inventory.vo.CreateInventoryDetailDTO;
import cn.iocoder.yudao.module.erp.controller.admin.inventory.vo.InventoryDetailVO;
import cn.iocoder.yudao.module.erp.controller.admin.inventory.vo.UpdateInventoryDetailDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.produce.ProduceInboundLineDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.PurchaseInboundItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.returned.ReturnedInboundItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryDetailDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.outbound.SalesOutboundItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface InventoryDetailConvert {

    InventoryDetailDO toDO(CreateInventoryDetailDTO creatDTO);

    List<InventoryDetailDO> toDO(List<CreateInventoryDetailDTO> creatDTO);

    InventoryDetailDO toDO(UpdateInventoryDetailDTO updateDTO);

    InventoryDetailVO toVO(InventoryDetailDO inventoryDetailDO);

    List<InventoryDetailVO> toVO(List<InventoryDetailDO> inventoryDetailDOList);


    List<CreateInventoryDetailDTO> fromProduceInbound(List<ProduceInboundLineDO> lineDOList);

    @Mappings({
            @Mapping(target = "businessItemId", source = "id"),
            @Mapping(target = "operationType", expression = "java(cn.iocoder.yudao.module.erp.constant.StockOperationTypeEnum.IN.getType())")
    })
    CreateInventoryDetailDTO fromProduceInbound(ProduceInboundLineDO lineDOList);


    List<CreateInventoryDetailDTO> fromSalesOutbound(List<SalesOutboundItemDO> salesOutboundItemDOS);

    @Mappings({
            @Mapping(target = "businessItemId", source = "id"),
            @Mapping(target = "fromInventoryId", source = "inventoryDetailId"),
            @Mapping(target = "operationType", expression = "java(cn.iocoder.yudao.module.erp.constant.StockOperationTypeEnum.OUT.getType())")
    })
    CreateInventoryDetailDTO fromSalesOutbound(SalesOutboundItemDO salesOutboundItemDOS);


    List<CreateInventoryDetailDTO> fromReturnedInbound(List<ReturnedInboundItemDO> inboundItemDOList);

    @Mappings({
            @Mapping(target = "businessItemId", source = "id"),
            @Mapping(target = "operationType", expression = "java(cn.iocoder.yudao.module.erp.constant.StockOperationTypeEnum.IN.getType())")
    })
    CreateInventoryDetailDTO fromReturnedInbound(ReturnedInboundItemDO inboundItemDO);


    List<CreateInventoryDetailDTO> fromPurchaseInbound(List<PurchaseInboundItemDO> inboundItemDOList);

    @Mappings({
            @Mapping(target = "businessItemId", source = "id"),
            @Mapping(target = "inQuantity", source = "inboundQuantity"),
            @Mapping(target = "operationType", expression = "java(cn.iocoder.yudao.module.erp.constant.StockOperationTypeEnum.IN.getType())")
    })
    CreateInventoryDetailDTO fromPurchaseInbound(PurchaseInboundItemDO inboundItemDO);


}