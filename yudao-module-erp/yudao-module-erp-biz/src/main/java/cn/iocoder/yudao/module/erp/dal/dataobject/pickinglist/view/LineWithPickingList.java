package cn.iocoder.yudao.module.erp.dal.dataobject.pickinglist.view;

import cn.iocoder.yudao.module.erp.dal.dataobject.pickinglist.PickingListDO;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LineWithPickingList {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6242")
    @ExcelProperty("id")
    private Long id;


    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("物料名称")
    private String materialName;

    @Schema(description = "物料编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物料编码 ")
    private String materialCode;

    @Schema(description = "总瓶数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总瓶数")
    private Integer totalBottles;

    @Schema(description = "备货单据信息", example = "{code：BH202505081234,}")
    private PickingListDO pickingList;
}
