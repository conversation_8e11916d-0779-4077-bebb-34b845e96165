package cn.iocoder.yudao.module.erp.service.requisition;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.requisition.vo.*;
import cn.iocoder.yudao.module.erp.dal.dataobject.requisition.RequisitionOrderItemDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 领料单 Service 接口
 *
 * <AUTHOR>
 */
public interface RequisitionOrderService {

    /**
     * 创建领料单
     *
     * @param createDTO 创建信息
     * @return Id
     */
    Long create(@Valid CreateRequisitionOrderDTO createDTO);

    /**
     * 更新领料单
     *
     * @param id        编号
     * @param updateDTO 更新信息
     */
    void update(Long id, @Valid UpdateRequisitionOrderDTO updateDTO);

    /**
     * 删除领料单
     *
     * @param id Id
     */
    void delete(Long id);

    /**
     * 获得领料单
     *
     * @param id Id
     * @return 领料单
     */
    RequisitionOrderVO get(Long id);

    /**
     * 获得领料单分页
     *
     * @param searchDTO 分页查询
     * @return 领料单分页
     */
    PageResult<SimpleRequisitionOrderVO> page(SearchRequisitionOrderDTO searchDTO);

    /**
     * 获得领料单明细分页
     *
     * @param searchDTO 分页查询
     * @return 领料单明细分页
     */
    PageResult<ItemWithRequisitionOrderVO> pageItem(SearchItemPageDTO searchDTO);

    // ==================== 业务状态操作 ====================

    /**
     * 关闭领料单
     *
     * @param id 领料单ID
     */
    void close(Long id);

    /**
     * 取消关闭领料单
     *
     * @param id 领料单ID
     */
    void cancelClose(Long id);

    /**
     * 挂起领料单
     *
     * @param id 领料单ID
     */
    void suspend(Long id);

    /**
     * 取消挂起领料单
     *
     * @param id 领料单ID
     */
    void cancelSuspend(Long id);

    /**
     * 审核领料单
     *
     * @param id 领料单ID
     */
    void approve(Long id);

    /**
     * 取消审核领料单
     *
     * @param id 领料单ID
     */
    void cancelApprove(Long id);

    // ==================== 子表（领料单明细） ====================

    /**
     * 获得领料单明细列表
     *
     * @param requisitionOrderId 领料单ID
     * @return 领料单明细列表
     */
    List<RequisitionOrderItemDO> getRequisitionOrderItemListByRequisitionOrderId(Long requisitionOrderId);

}
