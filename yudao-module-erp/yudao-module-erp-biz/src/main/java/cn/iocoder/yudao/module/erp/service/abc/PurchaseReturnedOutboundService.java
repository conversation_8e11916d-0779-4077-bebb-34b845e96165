package cn.iocoder.yudao.module.erp.service.abc;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.erp.controller.admin.abc.vo.*;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.PurchaseReturnedOutboundDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.PurchaseReturnedOutboundItemDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 采购退货出库单 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseReturnedOutboundService {

    /**
     * 创建采购退货出库单
     *
     * @param createDTO 创建信息
     * @return Id
     */
    Long create(@Valid CreatePurchaseReturnedOutboundDTO createDTO);

    /**
     * 更新采购退货出库单
     *
     * @param updateDTO 更新信息
     */
    void update(Long id, @Valid UpdatePurchaseReturnedOutboundDTO updateDTO);

    /**
     * 删除采购退货出库单
     *
     * @param id Id
     */
    void delete(Long id);

    /**
     * 获得采购退货出库单
     *
     * @param id Id
     * @return 采购退货出库单
     */
    PurchaseReturnedOutboundVO get(Long id);

    /**
     * 获得采购退货出库单分页
     *
     * @param searchDTO 分页查询
     * @return 采购退货出库单分页
     */
    PageResult<SimplePurchaseReturnedOutboundVO> page(SearchPurchaseReturnedOutboundDTO searchDTO);

    // ==================== 子表（采购退货出库单明细） ====================

    /**
     * 获得采购退货出库单明细分页
     *
     * @param pageReqVO 分页查询
     * @param returnedOutboundId 出入库单ID
     * @return 采购退货出库单明细分页
     */
    PageResult<PurchaseReturnedOutboundItemDO> getPurchaseReturnedOutboundItemPage(PageParam pageReqVO, Long returnedOutboundId);

    /**
     * 创建采购退货出库单明细
     *
     * @param purchaseReturnedOutboundItem 创建信息
     * @return 编号
     */
    Long createPurchaseReturnedOutboundItem(@Valid PurchaseReturnedOutboundItemDO purchaseReturnedOutboundItem);

    /**
     * 更新采购退货出库单明细
     *
     * @param purchaseReturnedOutboundItem 更新信息
     */
    void updatePurchaseReturnedOutboundItem(@Valid PurchaseReturnedOutboundItemDO purchaseReturnedOutboundItem);

    /**
     * 删除采购退货出库单明细
     *
     * @param id 编号
     */
    void deletePurchaseReturnedOutboundItem(Long id);

	/**
	 * 获得采购退货出库单明细
	 *
	 * @param id 编号
     * @return 采购退货出库单明细
	 */
    PurchaseReturnedOutboundItemDO getPurchaseReturnedOutboundItem(Long id);


    // ==================== 文档操作方法 ====================

    /**
     * 关闭采购退货出库单
     *
     * @param id Id
     */
    void close(Long id);

    /**
     * 取消关闭采购退货出库单
     *
     * @param id Id
     */
    void cancelClose(Long id);

    /**
     * 审核采购退货出库单
     *
     * @param id Id
     */
    void approve(Long id);

    /**
     * 取消审核采购退货出库单
     *
     * @param id Id
     */
    void cancelApprove(Long id);

    /**
     * 挂起采购退货出库单
     *
     * @param id Id
     */
    void suspend(Long id);

    /**
     * 取消挂起采购退货出库单
     *
     * @param id Id
     */
    void cancelSuspend(Long id);

    /**
     * 获得采购退货出库单明细分页
     *
     * @param searchDTO 分页查询
     * @return 采购退货出库单明细分页
     */
    PageResult<ItemWithPurchaseReturnedOutboundVO> pageItem(SearchItemPageDTO searchDTO);

}