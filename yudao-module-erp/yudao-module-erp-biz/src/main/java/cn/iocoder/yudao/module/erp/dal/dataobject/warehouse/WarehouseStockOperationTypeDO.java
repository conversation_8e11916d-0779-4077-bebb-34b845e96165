package cn.iocoder.yudao.module.erp.dal.dataobject.warehouse;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 库存操作类型 DO
 *
 * <AUTHOR>
 */
@TableName("erp_warehouse_stock_operation_type")
@KeySequence("erp_warehouse_stock_operation_type_seq")
// 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseStockOperationTypeDO {

    /**
     * 仓库id
     */
    private Long warehouseId;
    /**
     * 操作类型,in 入库类型，out 出库类型
     */
    private String type;
    /**
     * 数据字典类型
     */
    private String dictType;
    /**
     * 数据字典值
     */
    private String dictValue;

}