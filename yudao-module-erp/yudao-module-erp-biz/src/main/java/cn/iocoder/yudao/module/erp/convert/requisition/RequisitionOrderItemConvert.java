package cn.iocoder.yudao.module.erp.convert.requisition;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.requisition.vo.ItemWithRequisitionOrderVO;
import cn.iocoder.yudao.module.erp.controller.admin.requisition.vo.RequisitionOrderItemDTO;
import cn.iocoder.yudao.module.erp.controller.admin.requisition.vo.RequisitionOrderItemVO;
import cn.iocoder.yudao.module.erp.dal.dataobject.requisition.RequisitionOrderItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.requisition.view.ItemWithRequisitionOrder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 领料单明细 Convert
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface RequisitionOrderItemConvert {

    RequisitionOrderItemConvert INSTANCE = Mappers.getMapper(RequisitionOrderItemConvert.class);

    RequisitionOrderItemDO toDO(RequisitionOrderItemDTO itemDTO);

    List<RequisitionOrderItemDO> toDO(List<RequisitionOrderItemDTO> list);

    RequisitionOrderItemVO toVO(RequisitionOrderItemDO item);

    List<RequisitionOrderItemVO> toVOList(List<RequisitionOrderItemDO> list);

    ItemWithRequisitionOrderVO toItemWithRequisitionOrderVO(ItemWithRequisitionOrder item);

    List<ItemWithRequisitionOrderVO> toItemWithRequisitionOrderVOList(List<ItemWithRequisitionOrder> list);

    PageResult<ItemWithRequisitionOrderVO> convertPage(PageResult<ItemWithRequisitionOrder> page);

}
