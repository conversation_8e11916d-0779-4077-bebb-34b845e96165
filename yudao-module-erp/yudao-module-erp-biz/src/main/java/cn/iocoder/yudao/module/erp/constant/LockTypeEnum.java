package cn.iocoder.yudao.module.erp.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LockTypeEnum {
    SALES_ORDER("XS"), // 销售订单
    PICKING_LIST("BH"),    //配货通知单
    SHIPPING("FH"), //发货通知单
    RETURNED_ORDER("TH"),//退货通知单
    PRODUCE_INBOUND("CPRK"),//产品入库
    SALES_OUTBOUND("XSCK"),//销售出库
    RETURNED_INBOUND("XSTK"),//销售退货入库
    PURCHASE_REQUEST("CGSH"),//采购申请单
    PURCHASE_ORDER("CGDD"),//采购订单
    PURCHASE_ARRIVAL("CGDH"),//采购到货单
    PURCHASE_INBOUND("CGRK"),//采购入库单
    PURCHASE_RETURNED("CGTH"),//采购退货单
    REQUISITION_ORDER("LL"),//领料单


    SHIPPING_ITEM("FHMX_ITEM"),
    INVENTORY_ITEM("CKMX_ITEM"),
    SALES_OUTBOUND_ITEM("XSCK_ITEM"),
    RETURNED_ORDER_ITEM("TH_ITEM"),
    PRODUCE_INBOUND_ITEM("CPRK_ITEM"),
    PURCHASE_REQUEST_ITEM("CGSH_ITEM"),
    PURCHASE_ORDER_ITEM("CGDD_ITEM"),
    PURCHASE_ARRIVAL_ITEM("CGDH_ITEM"),
    PURCHASE_INBOUND_ITEM("CGRK_ITEM"),
    REQUISITION_ORDER_ITEM("LL_ITEM"),

    INVENTORY_DETAIL("KC_ITEM"),

    PURCHASE_RETURNED_ITEM("CGTH_ITEM");


    private final String prefix;


    @JsonValue
    public String toString() {
        return prefix;
    }
}
