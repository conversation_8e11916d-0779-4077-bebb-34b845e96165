package cn.iocoder.yudao.module.erp.convert.returned;

import cn.iocoder.yudao.module.erp.controller.admin.returned.vo.ItemWithReturnedOrderVO;
import cn.iocoder.yudao.module.erp.controller.admin.returned.vo.ReturnedOrderItemDTO;
import cn.iocoder.yudao.module.erp.controller.admin.returned.vo.ReturnedOrderItemVO;
import cn.iocoder.yudao.module.erp.dal.dataobject.returned.ReturnedOrderItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.returned.view.ItemWithReturnedOrder;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ReturnedOrderItemConvert {

    ReturnedOrderItemDO toDO(ReturnedOrderItemDTO returnedorderitemDTO);

    List<ReturnedOrderItemDO> toDO(List<ReturnedOrderItemDTO> returnedorderitemDTO);

    ReturnedOrderItemVO toVO(ReturnedOrderItemDO returnedorderitemDO);

    List<ReturnedOrderItemVO> toVO(List<ReturnedOrderItemDO> returnedorderitemDOList);

    List<ItemWithReturnedOrderVO> toItemWithReturnedVO(List<ItemWithReturnedOrder> itemWithReturnedOrderList);

    ItemWithReturnedOrderVO toItemWithReturnedVO(ItemWithReturnedOrder itemWithReturnedOrder);
}