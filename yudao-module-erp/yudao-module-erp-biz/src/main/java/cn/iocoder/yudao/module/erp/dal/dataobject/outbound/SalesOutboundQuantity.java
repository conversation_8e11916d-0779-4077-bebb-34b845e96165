package cn.iocoder.yudao.module.erp.dal.dataobject.outbound;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
public class SalesOutboundQuantity {
    /**
     * 销售出库单-itemId
     */
    private Long salesOutboundItemId;

    /**
     * 发货通知单-itemId
     */
    private Long shippingItemId;

    /**
     * 出库数量
     */
    private BigDecimal quantity;
}
