package cn.iocoder.yudao.module.erp.controller.admin.requisition.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 创建领料单 Request DTO")
@Data
public class CreateRequisitionOrderDTO {

    @Schema(description = "领料用途", requiredMode = Schema.RequiredMode.REQUIRED, example = "PRODUCTION")
    private String purpose;

    @Schema(description = "领料日期", requiredMode = Schema.RequiredMode.REQUIRED, type = "number", example = "1735660800000")
    @NotNull(message = "领料日期不能为空")
    private LocalDateTime requisitionDate;

    @Schema(description = "仓库ID", requiredMode = Schema.RequiredMode.REQUIRED, type = "number", example = "1")
    @NotNull(message = "仓库ID不能为空")
    private Long warehouseId;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "生产仓库")
    @NotNull(message = "仓库名称不能为空")
    private String warehouseName;

    @Schema(description = "业务部门ID", requiredMode = Schema.RequiredMode.REQUIRED, type = "number", example = "331")
    @NotNull(message = "业务部门ID不能为空")
    private Long businessDepartmentId;

    @Schema(description = "业务部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "数字化部")
    @NotNull(message = "业务部门名称不能为空")
    private String businessDepartmentName;

    @Schema(description = "业务员ID", requiredMode = Schema.RequiredMode.REQUIRED, type = "number", example = "952")
    @NotNull(message = "业务员ID不能为空")
    private Long businessUserId;

    @Schema(description = "业务员姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "依力哈木·塔依尔")
    @NotNull(message = "业务员姓名不能为空")
    private String businessUserName;

    @Schema(description = "备注", example = "备注信息")
    private String remark;

    @Schema(description = "领料单明细", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "领料单明细不能为空")
    @Valid
    private List<RequisitionOrderItemDTO> items;

}
