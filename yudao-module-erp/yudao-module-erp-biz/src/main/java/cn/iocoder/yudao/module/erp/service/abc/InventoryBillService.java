package cn.iocoder.yudao.module.erp.service.abc;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.abc.vo.*;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryBillItemDO;
import jakarta.validation.Valid;

/**
 * 出入库清单 Service 接口
 *
 * <AUTHOR>
 */
public interface InventoryBillService {

    /**
     * 创建出入库清单
     *
     * @param createDTO 创建信息
     * @return Id
     */
    Long create(@Valid CreateInventoryBillDTO createDTO);

    /**
     * 更新出入库清单
     *
     * @param updateDTO 更新信息
     */
    void update(Long id, @Valid UpdateInventoryBillDTO updateDTO);

    /**
     * 删除出入库清单
     *
     * @param id Id
     */
    void delete(Long id);

    /**
     * 获得出入库清单
     *
     * @param id Id
     * @return 出入库清单
     */
    InventoryBillVO get(Long id);

    /**
     * 获得出入库清单分页
     *
     * @param searchDTO 分页查询
     * @return 出入库清单分页
     */
    PageResult<SimpleInventoryBillVO> page(SearchInventoryBillDTO searchDTO);

    // ==================== 子表（通用库存单据明细） ====================

    /**
     * 获得通用库存单据明细分页
     *
     * @param pageReqVO 分页查询
     * @param billId    出入库单ID
     * @return 通用库存单据明细分页
     */
    PageResult<InventoryBillItemDO> getInventoryBillItemPage(PageParam pageReqVO, Long billId);

    /**
     * 创建通用库存单据明细
     *
     * @param inventoryBillItem 创建信息
     * @return 编号
     */
    Long createInventoryBillItem(@Valid InventoryBillItemDO inventoryBillItem);

    /**
     * 更新通用库存单据明细
     *
     * @param inventoryBillItem 更新信息
     */
    void updateInventoryBillItem(@Valid InventoryBillItemDO inventoryBillItem);

    /**
     * 删除通用库存单据明细
     *
     * @param id 编号
     */
    void deleteInventoryBillItem(Long id);

    /**
     * 获得通用库存单据明细
     *
     * @param id 编号
     * @return 通用库存单据明细
     */
    InventoryBillItemDO getInventoryBillItem(Long id);


    // ==================== 文档操作方法 ====================

    /**
     * 关闭出入库清单
     *
     * @param id Id
     */
    void close(Long id);

    /**
     * 取消关闭出入库清单
     *
     * @param id Id
     */
    void cancelClose(Long id);

    /**
     * 审核出入库清单
     *
     * @param id Id
     */
    void approve(Long id);

    /**
     * 取消审核出入库清单
     *
     * @param id Id
     */
    void cancelApprove(Long id);

    /**
     * 挂起出入库清单
     *
     * @param id Id
     */
    void suspend(Long id);

    /**
     * 取消挂起出入库清单
     *
     * @param id Id
     */
    void cancelSuspend(Long id);

    /**
     * 获得出入库清单明细分页
     *
     * @param searchDTO 分页查询
     * @return 出入库清单明细分页
     */
    PageResult<ItemWithInventoryBillVO> pageItem(SearchItemPageDTO searchDTO);

}