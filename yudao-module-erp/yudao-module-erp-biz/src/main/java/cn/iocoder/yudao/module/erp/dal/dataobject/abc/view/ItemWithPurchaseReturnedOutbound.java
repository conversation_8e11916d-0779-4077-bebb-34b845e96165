package cn.iocoder.yudao.module.erp.dal.dataobject.abc.view;

import cn.iocoder.yudao.module.erp.dal.dataobject.abc.PurchaseReturnedOutboundDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.PurchaseReturnedOutboundItemDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 采购退货出库单明细与主表组合 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ItemWithPurchaseReturnedOutbound extends PurchaseReturnedOutboundItemDO {

    /**
     * 采购退货出库单信息
     */
    private PurchaseReturnedOutboundDO purchaseReturnedOutbound;

}
