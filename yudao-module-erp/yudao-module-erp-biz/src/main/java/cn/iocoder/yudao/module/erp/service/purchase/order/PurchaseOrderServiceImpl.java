package cn.iocoder.yudao.module.erp.service.purchase.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.BusinessStatusEnum;
import cn.iocoder.yudao.module.erp.constant.BusinessTypeEnum;
import cn.iocoder.yudao.module.erp.constant.DocumentStatusEnum;
import cn.iocoder.yudao.module.erp.constant.LockTypeEnum;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.order.vo.*;
import cn.iocoder.yudao.module.erp.convert.purchase.order.PurchaseOrderConvert;
import cn.iocoder.yudao.module.erp.convert.purchase.order.PurchaseOrderItemConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.order.PurchaseOrderDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.order.PurchaseOrderItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.order.PurchaseQuantity;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.order.view.ItemWithPurchaseOrder;
import cn.iocoder.yudao.module.erp.dal.mysql.purchase.order.PurchaseOrderItemMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.purchase.order.PurchaseOrderMapper;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataRefer;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataService;
import cn.iocoder.yudao.module.erp.service.common.AbstractDocumentAuditingService;
import cn.iocoder.yudao.module.erp.service.common.DocumentRelationHelper;
import cn.iocoder.yudao.module.erp.service.common.DocumentServiceUtil;
import cn.iocoder.yudao.module.erp.service.purchase.request.PurchaseRequestService;
import cn.iocoder.yudao.module.erp.service.supplier.SupplierRefer;
import cn.iocoder.yudao.module.erp.service.supplier.SupplierService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.*;


/**
 * 采购订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseOrderServiceImpl extends AbstractDocumentAuditingService<PurchaseOrderItemDO>
        implements PurchaseOrderService, MaterialMasterDataRefer, SupplierRefer {

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private PurchaseOrderConvert purchaseOrderConvert;
    @Resource
    private PurchaseOrderItemConvert purchaseOrderItemConvert;
    @Resource
    private PurchaseOrderItemMapper purchaseOrderItemMapper;

    @Resource
    @Lazy
    private MaterialMasterDataService materialMasterDataService;
    @Resource
    @Lazy
    private AdminUserApi adminUserApi;

    @Resource
    private DocumentServiceUtil documentServiceUtil;

    @Resource
    @Lazy
    private PurchaseRequestService purchaseRequestService;

    @Resource
    private DocumentRelationHelper documentRelationHelper;

    @Resource
    @Lazy
    private DeptApi deptApi;

    @Resource
    @Lazy
    private SupplierService supplierService;

    @PostConstruct
    @Override
    protected void initLockTypeEnum() {
        this.documentLockType = LockTypeEnum.PURCHASE_ORDER;
        this.itemLockType = LockTypeEnum.PURCHASE_ORDER_ITEM;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(CreatePurchaseOrderDTO createDTO) {
        // 插入
        PurchaseOrderDO purchaseOrder = purchaseOrderConvert.toDO(createDTO);
        List<PurchaseOrderItemDO> itemDOList = purchaseOrderItemConvert.toDO(createDTO.getItems());
        commonValidate(purchaseOrder, itemDOList);

        documentServiceUtil.fillDocumentInfoWhenCreate(purchaseOrder, BusinessTypeEnum.PURCHASE_ORDER);
        fillName(purchaseOrder);
        purchaseOrderMapper.insert(purchaseOrder);

        // 插入子表
        doCreatePurchaseOrderItem(purchaseOrder.getId(), itemDOList);
        // 返回
        return purchaseOrder.getId();
    }

    private void fillName(PurchaseOrderDO purchaseOrder) {
        if (purchaseOrder.getBusinessDepartmentId() != null) {
            DeptRespDTO dept = deptApi.getDept(purchaseOrder.getBusinessDepartmentId()).getCheckedData();
            if (dept != null) {
                purchaseOrder.setBusinessDepartmentName(dept.getName());
            }
        }
        if (Objects.nonNull(purchaseOrder.getSupplierId())) {
            purchaseOrder.setSupplierName(supplierService.get(purchaseOrder.getSupplierId()).getName());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Long id, UpdatePurchaseOrderDTO updateDTO) {
        // 校验存在
        validatePurchaseOrderExists(id);
        validateCanEdit(id);

        PurchaseOrderDO updateObj = purchaseOrderConvert.toDO(updateDTO);
        List<PurchaseOrderItemDO> orderItemDOS = purchaseOrderItemConvert.toDO(updateDTO.getItems());
        // 验证业务逻辑
        commonValidate(updateObj, orderItemDOS);

        // 更新
        fillName(updateObj);
        updateObj.setId(id);
        purchaseOrderMapper.updateById(updateObj);

        // 更新子表 - 智能处理增删改
        updatePurchaseOrderItemSmart(id, orderItemDOS);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        // 校验存在
        validatePurchaseOrderExists(id);
        validateCanEdit(id);
        // 删除
        purchaseOrderMapper.deleteById(id);

        // 删除子表
        deletePurchaseOrderItemByPurchaseOrderId(id);
    }

    @Override
    public PurchaseOrderVO get(Long id) {
        PurchaseOrderDO purchaseOrder = purchaseOrderMapper.selectById(id);
        if (purchaseOrder == null) {
            return null;
        }
        List<PurchaseOrderItemDO> purchaseOrderItems = getPurchaseOrderItemListByPurchaseOrderId(id);
        PurchaseOrderVO purchaseOrderVO = purchaseOrderConvert.toVO(purchaseOrder);
        purchaseOrderVO.setItems(purchaseOrderItemConvert.toVO(purchaseOrderItems));
        return purchaseOrderVO;
    }

    @Override
    public PageResult<SimplePurchaseOrderVO> page(SearchPurchaseOrderDTO searchDTO) {
        PageResult<PurchaseOrderDO> page = purchaseOrderMapper.selectPage(searchDTO);
        return purchaseOrderConvert.toSimpleVOPage(page);
    }

    @Override
    public PageResult<ItemWithPurchaseOrderVO> pageItem(SearchItemPageDTO searchDTO) {
        PageResult<ItemWithPurchaseOrder> page = purchaseOrderItemMapper.selectItemPage(searchDTO);
        return purchaseOrderConvert.toItemWithPurchaseOrderVOPage(page);
    }

    // ==================== 业务状态操作 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void close(Long id) {
        validatePurchaseOrderExists(id);
        purchaseOrderMapper.updateBusinessStatusById(BusinessStatusEnum.CLOSED.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelClose(Long id) {
        validatePurchaseOrderExists(id);
        validateBusinessStatus(id, BusinessStatusEnum.CLOSED.getStatus());
        purchaseOrderMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspend(Long id) {
        validatePurchaseOrderExists(id);
        purchaseOrderMapper.updateBusinessStatusById(BusinessStatusEnum.SUSPENDED.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSuspend(Long id) {
        validatePurchaseOrderExists(id);
        validateBusinessStatus(id, BusinessStatusEnum.SUSPENDED.getStatus());
        purchaseOrderMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    // 审核和取消审核方法由父类 AbstractDocumentAuditingService 提供

    // ==================== AbstractDocumentAuditingService 抽象方法实现 ====================

    @Override
    protected void doApprove(Long id, List<PurchaseOrderItemDO> items) {
        // 审核时初始化采购订单的到货数量
        initArrivedQuantity(id);
        // 更新采购申请单明细的已采购数量
        List<PurchaseQuantity> purchaseQuantityList = items.stream()
                .filter(item -> item.getPurchaseRequestItemId() != null)
                .map(item -> new PurchaseQuantity(item.getId(), item.getPurchaseRequestItemId(), item.getPurchaseQuantity()))
                .toList();
        if (!purchaseQuantityList.isEmpty()) {
            documentRelationHelper.tryUpdateQuantityWhenPurchaseOrderApprove(purchaseQuantityList);
        }
    }

    @Override
    protected void doCancelApprove(Long id, List<PurchaseOrderItemDO> items) {
        // 取消审核时减少采购申请单明细的已采购数量
        List<PurchaseQuantity> purchaseQuantityList = items.stream()
                .filter(item -> item.getPurchaseRequestItemId() != null)
                .map(item -> new PurchaseQuantity(item.getId(), item.getPurchaseRequestItemId(), item.getPurchaseQuantity()))
                .toList();
        if (!purchaseQuantityList.isEmpty()) {
            documentRelationHelper.updateQuantityWhenPurchaseOrderCancelApprove(purchaseQuantityList);
        }
    }

    @Override
    protected void validateBeforeApprove(Long id) {
        validatePurchaseOrderExists(id);
        validateDocumentStatus(id, DocumentStatusEnum.approveExpectStatus());
        commonValidate(id);
    }

    @Override
    protected void validateBeforeCancelApprove(Long id) {
        validatePurchaseOrderExists(id);
        validateDocumentStatus(id, DocumentStatusEnum.cancelApproveExpectStatus());
        //验证是否可以取消
        documentRelationHelper.validatePurchaseOrderCanCancelApprove(id);
    }

    @Override
    protected List<PurchaseOrderItemDO> getDocumentItemsWhenAuditing(Long id) {
        return purchaseOrderItemMapper.selectListByPurchaseOrderId(id);
    }

    @Override
    protected List<Long> getNeedLockedIds(List<PurchaseOrderItemDO> items) {
        // 锁定采购申请单明细ID
        return items.stream()
                .map(PurchaseOrderItemDO::getPurchaseRequestItemId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    protected void updateDocumentApproveStatus(Long id, Integer status) {
        purchaseOrderMapper.updateApproveStatusById(status, id);
    }

    // ==================== MaterialMasterDataRefer 接口实现 ====================

    @Override
    public void validateWhenMaterialDelete(Long materialId) {
        if (purchaseOrderItemMapper.countByMaterialId(materialId) > 0) {
            throw exception(PURCHASE_ORDER_ITEM_REFER_MATERIAL_EXITS, materialId);
        }
    }

    // ==================== 验证方法 ====================

    private void validateBusinessStatus(Long id, Integer expectedStatus) {
        PurchaseOrderDO purchaseOrder = purchaseOrderMapper.selectById(id);
        if (!expectedStatus.equals(purchaseOrder.getBusinessStatus())) {
            throw exception(PURCHASE_ORDER_BUSINESS_STATUS_NOT_SUPPORT_CHANGE, purchaseOrder.getCode());
        }
    }

    private void validateDocumentStatus(Long id, Integer[] expectedStatuses) {
        PurchaseOrderDO purchaseOrder = purchaseOrderMapper.selectById(id);
        if (!ArrayUtil.contains(expectedStatuses, purchaseOrder.getDocumentStatus())) {
            throw exception(PURCHASE_ORDER_DOCUMENT_STATUS_NOT_SUPPORT_CHANGE, purchaseOrder.getCode());
        }
    }

    // ==================== 子表（采购订单明细） ====================

    @Override
    public List<PurchaseOrderItemDO> getPurchaseOrderItemListByPurchaseOrderId(Long purchaseOrderId) {
        return purchaseOrderItemMapper.selectListByPurchaseOrderId(purchaseOrderId);
    }

    @Override
    public void validateArrivalQuantity(Long purchaseOrderItemId, BigDecimal totalArrivalQuantity) {
        PurchaseOrderItemDO purchaseOrderItemDO = purchaseOrderItemMapper.selectById(purchaseOrderItemId);
        if (totalArrivalQuantity.compareTo(purchaseOrderItemDO.getUnarrivedQuantity()) > 0) {
            throw exception(PURCHASE_ARRIVAL_ARRIVAL_QUANTITY_BIG_THAN_UNARRIVED_QUANTITY, totalArrivalQuantity, purchaseOrderItemDO.getUnarrivedQuantity());
        }
    }

    private void doCreatePurchaseOrderItem(Long purchaseOrderId, List<PurchaseOrderItemDO> list) {
        list.forEach(o -> o.setPurchaseOrderId(purchaseOrderId));
        purchaseOrderItemMapper.insertBatch(list);
    }

    private void doUpdatePurchaseOrderItem(Long purchaseOrderId, List<PurchaseOrderItemDO> toUpdate) {
        toUpdate.forEach(o -> o.setPurchaseOrderId(purchaseOrderId));
        purchaseOrderItemMapper.updateBatch(toUpdate);
    }

    @Override
    public void validateExistsAndApprove(Long purchaseOrderId, Set<Long> purchaseOrderItemIds) {
        PurchaseOrderDO purchaseOrderDO = validatePurchaseOrderExists(purchaseOrderId);
        if (!DocumentStatusEnum.APPROVE.getStatus().equals(purchaseOrderDO.getDocumentStatus())) {
            throw exception(PURCHASE_ORDER_NOT_APPROVE, purchaseOrderDO.getCode());
        }
        List<PurchaseOrderItemDO> purchaseOrderItemDOS = purchaseOrderItemMapper.selectByPurchaseOrderIdAndItemIds(purchaseOrderId, purchaseOrderItemIds);
        Set<Long> existIds = purchaseOrderItemDOS.stream()
                .map(PurchaseOrderItemDO::getId)
                .collect(Collectors.toSet());
        List<Long> notExistIds = purchaseOrderItemIds.stream()
                .filter(id -> !existIds.contains(id))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(notExistIds)) {
            throw exception(PURCHASE_ORDER_ITEM_NOT_EXISTS, notExistIds);
        }
    }

    @Override
    public void updateArrivalQuantityWhenPurchaseArrivalApprove(Long purchaseOrderItemId, BigDecimal arrivalQuantity) {
        purchaseOrderItemMapper.updateArrivalQuantityWhenPurchaseArrivalApprove(purchaseOrderItemId, arrivalQuantity);
    }

    @Override
    public void updateArrivalQuantityWhenPurchaseArrivalCancelApprove(Long purchaseOrderItemId, BigDecimal arrivalQuantity) {
        purchaseOrderItemMapper.updateArrivalQuantityWhenPurchaseArrivalCancelApprove(purchaseOrderItemId, arrivalQuantity);
    }

    /**
     * 智能更新采购订单明细 - 区分新增、修改、删除
     */
    private void updatePurchaseOrderItemSmart(Long purchaseOrderId, List<PurchaseOrderItemDO> orderItemDTOList) {
        // 获取现有明细
        List<PurchaseOrderItemDO> existingItems = purchaseOrderItemMapper.selectListByPurchaseOrderId(purchaseOrderId);
        Map<Long, PurchaseOrderItemDO> existingItemMap = existingItems.stream()
                .collect(Collectors.toMap(PurchaseOrderItemDO::getId, item -> item));

        // 分类处理
        List<PurchaseOrderItemDO> toInsert = new ArrayList<>();
        List<PurchaseOrderItemDO> toUpdate = new ArrayList<>();
        Set<Long> updateItemIds = new HashSet<>();

        for (PurchaseOrderItemDO itemDTO : orderItemDTOList) {
            if (itemDTO.getId() == null) {
                // 新增
                toInsert.add(itemDTO);
            } else {
                // 修改
                updateItemIds.add(itemDTO.getId());
                toUpdate.add(itemDTO);
            }
        }

        // 找出要删除的明细
        List<Long> toDelete = existingItems.stream()
                .map(PurchaseOrderItemDO::getId)
                .filter(id -> !updateItemIds.contains(id))
                .collect(Collectors.toList());

        // 执行操作
        if (CollUtil.isNotEmpty(toInsert)) {
            doCreatePurchaseOrderItem(purchaseOrderId, toInsert);
        }
        if (CollUtil.isNotEmpty(toUpdate)) {
            doUpdatePurchaseOrderItem(purchaseOrderId, toUpdate);
        }
        if (CollUtil.isNotEmpty(toDelete)) {
            purchaseOrderItemMapper.deleteByIds(toDelete);
        }
    }


    private void deletePurchaseOrderItemByPurchaseOrderId(Long purchaseOrderId) {
        purchaseOrderItemMapper.deleteByPurchaseOrderId(purchaseOrderId);
    }

    private void commonValidate(PurchaseOrderDO purchaseOrderDO, List<PurchaseOrderItemDO> purchaseOrderItemDOList) {

        //FIXME:验证供应商
        adminUserApi.validateUserAndDept(purchaseOrderDO.getBusinessUserId(), purchaseOrderDO.getBusinessDepartmentId()).checkError();

        List<Long> materialIds = purchaseOrderItemDOList.stream()
                .map(PurchaseOrderItemDO::getMaterialId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(materialIds)) {
            materialMasterDataService.validateExist(materialIds);
        }
        //验证所有的引用的采购申请单都是存在，并且是审核通过的
        Set<Long> purchaseRequestIds = purchaseOrderItemDOList.stream()
                .map(PurchaseOrderItemDO::getPurchaseRequestId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(purchaseRequestIds)) {
            purchaseRequestService.validateExistsAndApprove(purchaseRequestIds);
        }
        //  验证采购数量不超过申请数量
        validatePurchaseQuantity(purchaseOrderItemDOList);
    }

    private void validatePurchaseQuantity(List<PurchaseOrderItemDO> items) {
        // 按采购申请单明细ID分组，验证采购数量不超过申请数量
        Map<Long, List<PurchaseOrderItemDO>> itemsByRequestItemId = items.stream()
                .filter(item -> item.getPurchaseRequestItemId() != null)
                .collect(Collectors.groupingBy(PurchaseOrderItemDO::getPurchaseRequestItemId));

        for (Map.Entry<Long, List<PurchaseOrderItemDO>> entry : itemsByRequestItemId.entrySet()) {
            Long requestItemId = entry.getKey();
            List<PurchaseOrderItemDO> purchaseItems = entry.getValue();

            // 计算总采购数量
            BigDecimal totalPurchaseQuantity = purchaseItems.stream()
                    .map(PurchaseOrderItemDO::getPurchaseQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 通过purchaseRequestService验证不能超过申请数量
            purchaseRequestService.validatePurchaseQuantity(requestItemId, totalPurchaseQuantity);
        }
        // 验证税价合计 = 采购数量 * 含税单价
        for (PurchaseOrderItemDO item : items) {
            BigDecimal calculatedTotalPrice = item.getPurchaseQuantity()
                    .multiply(item.getTaxIncludedPrice());
            if (item.getTaxIncludedTotal() == null ||
                    item.getTaxIncludedTotal().compareTo(calculatedTotalPrice) != 0) {
                throw exception(PURCHASE_ORDER_ITEM_TAX_PRICE_MISMATCH,
                        item.getPurchaseQuantity(), item.getTaxIncludedPrice(), item.getTaxIncludedTotal());
            }
        }
    }

    private void commonValidate(Long id) {
        PurchaseOrderDO purchaseOrderDO = validatePurchaseOrderExists(id);
        List<PurchaseOrderItemDO> purchaseOrderItemDOList = purchaseOrderItemMapper.selectListByPurchaseOrderId(id);
        commonValidate(purchaseOrderDO, purchaseOrderItemDOList);

    }

    private PurchaseOrderDO validatePurchaseOrderExists(Long id) {
        PurchaseOrderDO purchaseOrder = purchaseOrderMapper.selectById(id);
        if (purchaseOrder == null) {
            throw exception(PURCHASE_ORDER_NOT_EXISTS, id);
        }
        return purchaseOrder;
    }

    private void validateCanEdit(Long id) {
        PurchaseOrderDO purchaseOrder = purchaseOrderMapper.selectById(id);
        if (!ArrayUtil.contains(new Integer[]{DocumentStatusEnum.DRAFT.getStatus(), DocumentStatusEnum.ROLLBACK.getStatus()}, purchaseOrder.getDocumentStatus())) {
            throw exception(PURCHASE_ORDER_DOCUMENT_STATUS_NOT_SUPPORT_CHANGE, purchaseOrder.getCode(), purchaseOrder.getDocumentStatus());
        }
    }

    /**
     * 初始化采购订单的到货数量
     * 审核通过时执行，已到货数量为0，未到货数量为采购数量
     *
     * @param purchaseOrderId 采购订单ID
     */
    private void initArrivedQuantity(Long purchaseOrderId) {
        purchaseOrderItemMapper.initArrivedQuantity(purchaseOrderId);
    }

    @Override
    public void validateWhenSupplierDelete(Long supplierId) {
        Long countBySupplierId = purchaseOrderMapper.countBySupplierId(supplierId);
        if (countBySupplierId > 0) {
            throw exception(PURCHASE_ORDER_REFER_SUPPLIER_EXITS, supplierId);
        }
    }
}