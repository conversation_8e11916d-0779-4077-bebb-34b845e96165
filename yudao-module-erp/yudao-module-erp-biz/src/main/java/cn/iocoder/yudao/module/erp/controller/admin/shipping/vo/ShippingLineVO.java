package cn.iocoder.yudao.module.erp.controller.admin.shipping.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 发货通知单 VO")
@Data
@ExcelIgnoreUnannotated
public class ShippingLineVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19963")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "发货单id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31049")
    @ExcelProperty("发货单id")
    private Long shippingId;

    @Schema(description = "销售订单id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11721")
    @ExcelProperty("销售订单id")
    private Long salesOrderId;

    @Schema(description = "销售订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("销售订单号")
    private String salesOrderCode;

    @Schema(description = "销售单据表中的客户订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("销售单据表中的客户订单号")
    private String salesCustomerOrderCode;

    @Schema(description = "销售订单行id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11971")
    @ExcelProperty("销售订单行id")
    private Long salesOrderLineId;

    @Schema(description = "物料id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19859")
    @ExcelProperty("物料id")
    private Long materialId;

    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("物料名称")
    private String materialName;

    @Schema(description = "物料编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物料编码")
    private String materialCode;

    @Schema(description = "物料规格型号")
    @ExcelProperty("物料规格型号")
    private String materialSpec;

    @Schema(description = "物料主计量单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物料主计量单位")
    private String materialUom;

    @Schema(description = "包装类型ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("包装类型ID")
    private Long packagingTypeId;

    @Schema(description = "包装类型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("包装类型名称")
    private String packagingTypeName;


    @Schema(description = "包装规格id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7367")
    @ExcelProperty("包装规格id")
    private Long packagingSpecId;

    @Schema(description = "包装规格名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("包装规格名称")
    private String packagingSpecName;

    @Schema(description = "发货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发货数量")
    private BigDecimal shippingQuantity;

    @Schema(description = "车/瓶数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("车/瓶数")
    private Integer shippingBottles;

    @Schema(description = "备货通知单id", requiredMode = Schema.RequiredMode.REQUIRED, example = "496")
    @ExcelProperty("备货通知单id")
    private Long pickingListId;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;


    /**
     * 已出库数量
     */
    private BigDecimal shippedQuantity;

    /**
     * 未出库数量
     */
    private BigDecimal unshippedQuantity;

    /**
     * 安装说明
     */
    @Schema(description = "安装说明", example = "安装说明")
    @ExcelProperty("安装说明")
    private String installInfo;


}