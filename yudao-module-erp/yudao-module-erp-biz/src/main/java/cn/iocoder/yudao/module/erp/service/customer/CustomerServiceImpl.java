package cn.iocoder.yudao.module.erp.service.customer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.OrganizationIdentitySpecEnum;
import cn.iocoder.yudao.module.erp.controller.admin.base.organization.vo.OrganizationVO;
import cn.iocoder.yudao.module.erp.controller.admin.customer.vo.*;
import cn.iocoder.yudao.module.erp.convert.customer.CustomerConvert;
import cn.iocoder.yudao.module.erp.convert.customer.CustomerInvoiceInfoConvert;
import cn.iocoder.yudao.module.erp.convert.customer.CustomerReceiveAddressConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.customer.CustomerDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.customer.CustomerInvoiceInfoDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.customer.CustomerReceiveAddressDO;
import cn.iocoder.yudao.module.erp.dal.mysql.customer.CustomerInvoiceInfoMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.customer.CustomerMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.customer.CustomerReceiveAddressMapper;
import cn.iocoder.yudao.module.erp.service.base.organization.AbstractOrganizationEntityService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.*;

/**
 * 客户基本信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CustomerServiceImpl extends AbstractOrganizationEntityService<CreateCustomerDTO, UpdateCustomerDTO> implements CustomerService {

    @Resource
    private CustomerMapper customerMapper;

    @Resource
    private CustomerConvert customerConvert;

    @Resource
    private CustomerReceiveAddressConvert customerReceiveAddressConvert;

    @Resource
    private CustomerReceiveAddressMapper customerReceiveAddressMapper;

    @Resource
    private CustomerInvoiceInfoConvert customerInvoiceInfoConvert;

    @Resource
    private CustomerInvoiceInfoMapper customerInvoiceInfoMapper;

    @Resource
    @Lazy
    private List<CustomerRefer> customerRefers;

    @Override
    public CustomerVO get(Long id) {
        CustomerDO customer = customerMapper.selectById(id);
        if (customer == null) {
            throw exception(CUSTOMER_NOT_EXISTS, id);
        }
        return customerConvert.toVO(customer);
    }

    @Override
    public PageResult<CustomerVO> page(SearchCustomerDTO searchCustomerDTO) {
        PageResult<CustomerDO> customerDOPageResult = customerMapper.selectPage(searchCustomerDTO);
        List<CustomerVO> customerVOList = customerConvert.toVOList(customerDOPageResult.getList());
        return new PageResult<>(customerVOList, customerDOPageResult.getTotal());
    }

    @Override
    public void disable(Long id) {
        validateCustomerExists(id);
        CustomerDO updateObj = new CustomerDO();
        updateObj.setId(id);
        updateObj.setStatus(CommonStatusEnum.DISABLE.getStatus());
        customerMapper.updateById(updateObj);
    }

    @Override
    public void enable(Long id) {
        validateCustomerExists(id);
        CustomerDO updateObj = new CustomerDO();
        updateObj.setId(id);
        updateObj.setStatus(CommonStatusEnum.ENABLE.getStatus());
        customerMapper.updateById(updateObj);
    }

    @Override
    public CustomerReceiveAddressVO createReceiveAddress(Long id, CreateCustomerReceiveAddressDTO createCustomerReceiveAddressDTO) {
        validateCustomerExists(id);
        checkCustomerUpdatable(id);
        CustomerReceiveAddressDO customerReceiveAddressDO = customerReceiveAddressConvert.toDO(createCustomerReceiveAddressDTO);
        customerReceiveAddressDO.setCustomerId(id);
        customerReceiveAddressMapper.insert(customerReceiveAddressDO);
        return customerReceiveAddressConvert.toVO(customerReceiveAddressDO);
    }

    @Override
    public CustomerReceiveAddressVO updateReceiveAddress(Long id, Long receiveAddressId, UpdateCustomerReceiveAddressDTO updateCustomerReceiveAddressDTO) {
        validateCustomerExists(id);
        checkCustomerUpdatable(id);
        validateCustomerReceiveAddressExists(id, receiveAddressId);
        CustomerReceiveAddressDO customerReceiveAddressDO = customerReceiveAddressConvert.toDO(updateCustomerReceiveAddressDTO);
        customerReceiveAddressDO.setId(receiveAddressId);
        customerReceiveAddressMapper.updateById(customerReceiveAddressDO);
        return customerReceiveAddressConvert.toVO(customerReceiveAddressMapper.selectById(receiveAddressId));
    }

    @Override
    public void deleteReceiveAddress(Long id, Long receiveAddressId) {
        validateCustomerExists(id);
        checkCustomerUpdatable(id);
        validateCustomerReceiveAddressExists(id, receiveAddressId);
        customerReceiveAddressMapper.deleteById(receiveAddressId);
    }

    @Override
    public CustomerReceiveAddressVO getReceiveAddress(Long id, Long receiveAddressId) {
        validateCustomerExists(id);
        validateCustomerReceiveAddressExists(id, receiveAddressId);
        CustomerReceiveAddressDO customerReceiveAddress = customerReceiveAddressMapper.selectById(receiveAddressId);
        return customerReceiveAddressConvert.toVO(customerReceiveAddress);
    }

    @Override
    public List<CustomerReceiveAddressVO> listReceiveAddress(Long id) {
        validateCustomerExists(id);
        List<CustomerReceiveAddressDO> customerReceiveAddressList = customerReceiveAddressMapper.selectListByCustomerId(id);
        return customerReceiveAddressConvert.toVOList(customerReceiveAddressList);
    }

    @Override
    public CustomerInvoiceInfoVO createInvoiceInfo(Long id, CreateCustomerInvoiceInfoDTO createCustomerInvoiceInfoDTO) {
        validateInvoice(createCustomerInvoiceInfoDTO);
        validateCustomerExists(id);
        checkCustomerUpdatable(id);
        CustomerInvoiceInfoDO customerInvoiceInfoDO = customerInvoiceInfoConvert.toDO(createCustomerInvoiceInfoDTO);
        customerInvoiceInfoDO.setCustomerId(id);
        customerInvoiceInfoMapper.insert(customerInvoiceInfoDO);
        return customerInvoiceInfoConvert.toVO(customerInvoiceInfoDO);
    }

    private void validateInvoice(CreateCustomerInvoiceInfoDTO createCustomerInvoiceInfoDTO) {
        //如果所有的属性都为空，则报错
        if (StrUtil.isEmpty(createCustomerInvoiceInfoDTO.getUscc())
                && StrUtil.isEmpty(createCustomerInvoiceInfoDTO.getBankName())
                && StrUtil.isEmpty(createCustomerInvoiceInfoDTO.getBankAccount())
                && StrUtil.isEmpty(createCustomerInvoiceInfoDTO.getAddress())) {
            throw exception(CUSTOMER_INVOICE_INFO_EMPTY);
        }
    }

    @Override
    public CustomerInvoiceInfoVO updateInvoiceInfo(Long id, Long invoiceInfoId, UpdateCustomerInvoiceInfoDTO updateCustomerInvoiceInfoDTO) {
        validateCustomerExists(id);
        checkCustomerUpdatable(id);
        validateCustomerInvoiceInfoExists(id, invoiceInfoId);
        CustomerInvoiceInfoDO customerInvoiceInfoDO = customerInvoiceInfoConvert.toDO(updateCustomerInvoiceInfoDTO);

        customerInvoiceInfoDO.setId(invoiceInfoId);
        customerInvoiceInfoMapper.updateById(customerInvoiceInfoDO);
        return customerInvoiceInfoConvert.toVO(customerInvoiceInfoMapper.selectById(invoiceInfoId));
    }

    @Override
    public void deleteInvoiceInfo(Long id, Long invoiceInfoId) {
        validateCustomerExists(id);
        checkCustomerUpdatable(id);
        validateCustomerInvoiceInfoExists(id, invoiceInfoId);
        customerInvoiceInfoMapper.deleteById(invoiceInfoId);
    }

    @Override
    public CustomerInvoiceInfoVO getInvoiceInfo(Long id, Long invoiceInfoId) {
        validateCustomerExists(id);
        validateCustomerInvoiceInfoExists(id, invoiceInfoId);
        CustomerInvoiceInfoDO customerInvoiceInfo = customerInvoiceInfoMapper.selectById(invoiceInfoId);
        return customerInvoiceInfoConvert.toVO(customerInvoiceInfo);
    }

    @Override
    public List<CustomerInvoiceInfoVO> listInvoiceInfo(Long id) {
        validateCustomerExists(id);
        List<CustomerInvoiceInfoDO> customerInvoiceInfoList = customerInvoiceInfoMapper.selectListByCustomerId(id);
        return customerInvoiceInfoConvert.toVOList(customerInvoiceInfoList);
    }

    @Override
    public List<SimpleCustomerVO> simpleList() {
        List<CustomerDO> customerList = customerMapper.selectList();
        return customerConvert.toSimpleVOList(customerList);
    }

    @Override
    public void validateEnable(Long customerId) {
        CustomerDO customerDO = customerMapper.selectById(customerId);
        if (customerDO == null) {
            throw exception(CUSTOMER_NOT_EXISTS, customerId);
        }

        if (Objects.equals(customerDO.getStatus(), CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(CUSTOMER_NOT_ENABLE, customerDO.getName());
        }
    }

    @Override
    public void validateExistAndEnable(List<Long> customerIds) {
        List<CustomerDO> customerDOS = customerMapper.selectByIds(customerIds);
        //找customerIds中Id不在customerDOS中的id集合
        List<Long> notExistsIds = customerIds.stream().filter(id -> customerDOS.stream().map(CustomerDO::getId).noneMatch(dbId -> Objects.equals(dbId, id))).toList();
        if (CollUtil.isNotEmpty(notExistsIds)) {
            throw exception(CUSTOMER_NOT_EXISTS, notExistsIds);
        }
        //从customerDOS获取状态为停用的集合
        List<CustomerDO> disabledCustomerDOS = customerDOS.stream().filter(item -> item.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())).toList();
        if (CollUtil.isNotEmpty(disabledCustomerDOS)) {
            throw exception(CUSTOMER_NOT_ENABLE, disabledCustomerDOS.stream().map(CustomerDO::getName).toList());
        }
    }

    @Override
    public void validateCustomerExists(Long id) {
        if (customerMapper.selectById(id) == null) {
            throw exception(CUSTOMER_NOT_EXISTS, id);
        }
    }

    private void validateCustomerCodeDuplicate(Long id, String code) {
        CustomerDO customer = customerMapper.selectByCode(code);
        if (customer != null && !customer.getId().equals(id)) {
            throw exception(CUSTOMER_CODE_DUPLICATE, code);
        }
    }

    private void validateCustomerNameDuplicate(Long id, String name) {
        CustomerDO customer = customerMapper.selectByName(name);
        if (customer != null && !customer.getId().equals(id)) {
            throw exception(CUSTOMER_NAME_DUPLICATE, name);
        }
    }

    private void validateCustomerShortNameDuplicate(Long id, String shortName) {
        CustomerDO customer = customerMapper.selectByShortName(shortName);
        if (customer != null && !customer.getId().equals(id)) {
            throw exception(CUSTOMER_SHORT_NAME_DUPLICATE, shortName);
        }
    }

    private void validateCustomerReceiveAddressExists(Long id, Long receiveAddressId) {
        CustomerReceiveAddressDO customerReceiveAddress = customerReceiveAddressMapper.selectById(receiveAddressId);
        if (customerReceiveAddress == null || !customerReceiveAddress.getCustomerId().equals(id)) {
            throw exception(CUSTOMER_RECEIVE_ADDRESS_NOT_EXISTS, receiveAddressId);
        }
    }

    private void validateCustomerInvoiceInfoExists(Long id, Long invoiceInfoId) {
        CustomerInvoiceInfoDO customerInvoiceInfo = customerInvoiceInfoMapper.selectById(invoiceInfoId);
        if (customerInvoiceInfo == null || !customerInvoiceInfo.getCustomerId().equals(id)) {
            throw exception(CUSTOMER_INVOICE_INFO_NOT_EXISTS, invoiceInfoId);
        }
    }

    private void checkCustomerUpdatable(Long id) {
        CustomerDO customer = customerMapper.selectById(id);
        if (customer != null && customer.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(CUSTOMER_NOT_UPDATABLE, id);
        }
    }

    @Override
    public List<SimpleCustomerVO> listByIds(List<Long> list) {
        return customerConvert.toSimpleVOList(customerMapper.selectByIds(list));
    }

    // =================   AbstractOrganizationEntityService 需要的实现方法 =============================
    @Override
    protected void updateOrganizationCommonField(Long specIdByOrganizationId, OrganizationVO organization) {
        CustomerDO customerDO = customerConvert.toDO(organization);
        customerDO.setId(specIdByOrganizationId);
        customerMapper.updateById(customerDO);
    }

    @Override
    protected Long doCreate(CreateCustomerDTO createDTO) {
        CustomerDO customer = customerConvert.toDO(createDTO);
        customer.setStatus(CommonStatusEnum.ENABLE.getStatus());
        customerMapper.insert(customer);
        return customer.getId();
    }

    @Override
    protected void doUpdate(Long id, UpdateCustomerDTO updateDTO) {
        // 更新
        CustomerDO updateObj = customerConvert.toDO(updateDTO);
        updateObj.setId(id);
        customerMapper.updateById(updateObj);
    }

    @Override
    protected OrganizationIdentitySpecEnum getOrganizationIdentitySpecEnum() {
        return OrganizationIdentitySpecEnum.CUSTOMER;
    }

    @Override
    protected void doDelete(Long id) {
        customerMapper.deleteById(id);
        customerReceiveAddressMapper.deleteByCustomerId(id);
        customerInvoiceInfoMapper.deleteByCustomerId(id);
    }

    @Override
    protected void validateSelfBeforeCreate(CreateCustomerDTO createDTO) {
        validateCustomerCodeDuplicate(null, createDTO.getCode());
        validateCustomerNameDuplicate(null, createDTO.getName());
        validateCustomerShortNameDuplicate(null, createDTO.getShortName());
    }

    @Override
    protected void validateSelfBeforeUpdate(Long id, UpdateCustomerDTO updateDTO) {
        validateCustomerExists(id);
        validateCustomerCodeDuplicate(id, updateDTO.getCode());
        validateCustomerNameDuplicate(id, updateDTO.getName());
        validateCustomerShortNameDuplicate(id, updateDTO.getShortName());
    }

    @Override
    protected void validateBeforeDelete(Long id) {
        validateCustomerExists(id);
        for (CustomerRefer customerRefer : customerRefers) {
            customerRefer.validateWhenCustomerDelete(id);
        }
    }
}