package cn.iocoder.yudao.module.erp.dal.dataobject.base.organization;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 组织身份：供应商和客户的身份信息 DO
 *
 * <AUTHOR>
 */
@TableName("erp_organization_identity")
@KeySequence("erp_organization_identity_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationIdentityDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 组织ID
     */
    private Long organizationId;
    /**
     * 身份类型(客户/供应商)
     */
    private String identityType;
    /**
     * 身份类别（比如供应商下面的采购供应商，承运商）
     */
    private String identitySpec;
    /**
     * 身份类别ID (比如spec是客服代表客户表的Id，如果是承运商代表承运商的Id)
     */
    private Long identitySpecId;

}