package cn.iocoder.yudao.module.erp.dal.mysql.outbound.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.SearchPurchaseReturnedOutboundDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.outbound.purchase.PurchaseReturnedOutboundDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

/**
 * 采购退货出库单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseReturnedOutboundMapper extends BaseMapperX<PurchaseReturnedOutboundDO> {

    default PageResult<PurchaseReturnedOutboundDO> selectPage(SearchPurchaseReturnedOutboundDTO searchDTO) {
        return selectPage(searchDTO, new LambdaQueryWrapperX<PurchaseReturnedOutboundDO>()
                .eqIfPresent(PurchaseReturnedOutboundDO::getCode, searchDTO.getCode())
                .eqIfPresent(PurchaseReturnedOutboundDO::getBusinessType, searchDTO.getBusinessType())
                .eqIfPresent(PurchaseReturnedOutboundDO::getDocumentStatus, searchDTO.getDocumentStatus())
                .eqIfPresent(PurchaseReturnedOutboundDO::getBusinessStatus, searchDTO.getBusinessStatus())
                .eqIfPresent(PurchaseReturnedOutboundDO::getWarehouseId, searchDTO.getWarehouseId())
                .likeIfPresent(PurchaseReturnedOutboundDO::getWarehouseName, searchDTO.getWarehouseName())
                .eqIfPresent(PurchaseReturnedOutboundDO::getSupplierId, searchDTO.getSupplierId())
                .likeIfPresent(PurchaseReturnedOutboundDO::getSupplierName, searchDTO.getSupplierName())
                .eqIfPresent(PurchaseReturnedOutboundDO::getBusinessDepartmentId, searchDTO.getBusinessDepartmentId())
                .likeIfPresent(PurchaseReturnedOutboundDO::getBusinessDepartmentName, searchDTO.getBusinessDepartmentName())
                .eqIfPresent(PurchaseReturnedOutboundDO::getBusinessUserId, searchDTO.getBusinessUserId())
                .likeIfPresent(PurchaseReturnedOutboundDO::getBusinessUserName, searchDTO.getBusinessUserName())
                .eqIfPresent(PurchaseReturnedOutboundDO::getMaterialType, searchDTO.getMaterialType())
                .eqIfPresent(PurchaseReturnedOutboundDO::getSourceId, searchDTO.getReturnedId())
                .eqIfPresent(PurchaseReturnedOutboundDO::getSourceCode, searchDTO.getReturnedCode())
                .eqIfPresent(PurchaseReturnedOutboundDO::getRemark, searchDTO.getRemark())
                .betweenIfPresent(PurchaseReturnedOutboundDO::getAuditTime, searchDTO.getAuditTime())
                .betweenIfPresent(PurchaseReturnedOutboundDO::getCreateTime, searchDTO.getCreateTime())
                .orderByDesc(PurchaseReturnedOutboundDO::getId));
    }

    /**
     * 更新业务状态
     *
     * @param businessStatus 业务状态
     * @param id             编号
     */
    default void updateBusinessStatusById(Integer businessStatus, Long id) {
        PurchaseReturnedOutboundDO updateObj = new PurchaseReturnedOutboundDO();
        updateObj.setBusinessStatus(businessStatus);
        updateObj.setId(id);
        updateById(updateObj);
    }

    /**
     * 更新单据状态
     *
     * @param documentStatus 单据状态
     * @param id             编号
     */
    default void updateDocumentStatusById(Integer documentStatus, Long id) {
        PurchaseReturnedOutboundDO updateObj = new PurchaseReturnedOutboundDO();
        updateObj.setDocumentStatus(documentStatus);
        updateObj.setId(id);
        updateObj.setAuditTime(LocalDateTime.now());
        updateById(updateObj);
    }

}