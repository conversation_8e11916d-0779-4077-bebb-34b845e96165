package cn.iocoder.yudao.module.erp.controller.admin.purchase.arrival;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.arrival.vo.*;
import cn.iocoder.yudao.module.erp.service.purchase.arrival.PurchaseArrivalService;
import db.migration.PermissionName;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 采购到货单")
@RestController
@RequestMapping("/erp/purchase-arrival")
@Validated
public class PurchaseArrivalController {

    @Resource
    private PurchaseArrivalService purchaseArrivalService;

    @PostMapping
    @Operation(summary = "创建采购到货单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-arrival:create')")
    @PermissionName(name = "创建采购到货单", sort = 1)
    public CommonResult<Long> create(@Valid @RequestBody CreatePurchaseArrivalDTO createDTO) {
        return success(purchaseArrivalService.create(createDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新采购到货单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-arrival:update')")
    @PermissionName(name = "更新采购到货单", sort = 2)
    public CommonResult<Boolean> update(@PathVariable("id") Long id, @Valid @RequestBody UpdatePurchaseArrivalDTO updateDTO) {
        purchaseArrivalService.update(id, updateDTO);
        return success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除采购到货单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-arrival:delete')")
    @PermissionName(name = "删除采购到货单", sort = 3)
    public CommonResult<Boolean> delete(@PathVariable("id") Long id) {
        purchaseArrivalService.delete(id);
        return success(true);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取采购到货单详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:purchase-arrival:view')")
    @PermissionName(name = "查看采购到货单详情", sort = 4)
    public CommonResult<PurchaseArrivalVO> get(@PathVariable("id") Long id) {
        return success(purchaseArrivalService.get(id));
    }

    @GetMapping("/page")
    @Operation(summary = "查询采购到货单分页")
    @PreAuthorize("@ss.hasAnyPermissions('erp:purchase-arrival:query','erp:purchase-inbound:create','erp:purchase-inbound:update')")
    @PermissionName(name = "查询采购到货单-单据模式", sort = 9)
    public CommonResult<PageResult<SimplePurchaseArrivalVO>> page(@Valid SearchPurchaseArrivalDTO searchDTO) {
        PageResult<SimplePurchaseArrivalVO> pageResult = purchaseArrivalService.page(searchDTO);
        return success(pageResult);
    }

    @GetMapping("/item/page")
    @Operation(summary = "查询采购到货单明细分页")
    @PreAuthorize("@ss.hasAnyPermissions('erp:purchase-arrival:item:query','erp:purchase-inbound:create','erp:purchase-inbound:update')")
    @PermissionName(name = "查询采购到货单-明细模式", sort = 8)
    public CommonResult<PageResult<ItemWithPurchaseArrivalVO>> pageItem(@Valid SearchItemPageDTO searchDTO) {
        PageResult<ItemWithPurchaseArrivalVO> pageResult = purchaseArrivalService.pageItem(searchDTO);
        return success(pageResult);
    }

    // ==================== 业务状态操作 ====================

    @PutMapping("/close/{id}")
    @Operation(summary = "关闭采购到货单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-arrival:close')")
    @PermissionName(name = "关闭/取消关闭采购到货单", sort = 6)
    public CommonResult<Boolean> close(@PathVariable("id") Long id) {
        purchaseArrivalService.close(id);
        return success(true);
    }

    @PutMapping("/cancel-close/{id}")
    @Operation(summary = "取消关闭采购到货单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-arrival:close')")
    public CommonResult<Boolean> cancelClose(@PathVariable("id") Long id) {
        purchaseArrivalService.cancelClose(id);
        return success(true);
    }

    @PutMapping("/suspend/{id}")
    @Operation(summary = "挂起采购到货单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-arrival:suspend')")
    @PermissionName(name = "挂起/取消挂起采购到货单", sort = 7)
    public CommonResult<Boolean> suspend(@PathVariable("id") Long id) {
        purchaseArrivalService.suspend(id);
        return success(true);
    }

    @PutMapping("/cancel-suspend/{id}")
    @Operation(summary = "取消挂起采购到货单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-arrival:suspend')")
    public CommonResult<Boolean> cancelSuspend(@PathVariable("id") Long id) {
        purchaseArrivalService.cancelSuspend(id);
        return success(true);
    }

    @PutMapping("/approve/{id}")
    @Operation(summary = "审核采购到货单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-arrival:approve')")
    @PermissionName(name = "审核/取消审核采购到货单", sort = 5)
    public CommonResult<Boolean> approve(@PathVariable("id") Long id) {
        purchaseArrivalService.approve(id);
        return success(true);
    }

    @PutMapping("/cancel-approve/{id}")
    @Operation(summary = "取消审核采购到货单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-arrival:approve')")
    public CommonResult<Boolean> cancelApprove(@PathVariable("id") Long id) {
        purchaseArrivalService.cancelApprove(id);
        return success(true);
    }

}
