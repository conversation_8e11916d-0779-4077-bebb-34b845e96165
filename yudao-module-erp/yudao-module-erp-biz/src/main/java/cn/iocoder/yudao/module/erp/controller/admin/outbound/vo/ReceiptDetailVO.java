package cn.iocoder.yudao.module.erp.controller.admin.outbound.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 签收单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ReceiptDetailVO {

    // ========== 来源发货通知单信息 ==========
    @Schema(description = "客户名称", example = "张三公司")
    @ExcelProperty("客户名称")
    private String customerName;

    @Schema(description = "收货地址", example = "北京市朝阳区xxx街道")
    @ExcelProperty("收货地址")
    private String customerReceiveAddress;

    @Schema(description = "收货联系人", example = "李四")
    @ExcelProperty("收货联系人")
    private String customerReceiveContact;

    @Schema(description = "收货联系人电话", example = "13800138000")
    @ExcelProperty("收货联系人电话")
    private String customerReceiveContactPhone;

    @Schema(description = "发货单号", example = "FH202501010001")
    @ExcelProperty("发货单号")
    private String shippingCode;

    @Schema(description = "发货日期")
    @ExcelProperty("发货日期")
    private LocalDateTime deliveryDate;

    @Schema(description = "客户订单号", example = "CU202501010001")
    @ExcelProperty("客户订单号")
    private String customerOrderCode;

    @Schema(description = "承运商名称", example = "顺丰物流")
    @ExcelProperty("承运商名称")
    private String carrierName;

    // ========== 用户输入信息 ==========
    @Schema(description = "承运日期")
    @ExcelProperty("承运日期")
    private LocalDate carrierDate;

    @Schema(description = "送货司机ID", example = "1001")
    @ExcelProperty("送货司机ID")
    private Long carrierDriverId;

    @Schema(description = "送货司机姓名", example = "王五")
    @ExcelProperty("送货司机姓名")
    private String carrierDriverName;

    @Schema(description = "送货司机电话", example = "13900139000")
    @ExcelProperty("送货司机电话")
    private String carrierDriverPhone;

    @Schema(description = "客户合同", example = "HT202501010001")
    @ExcelProperty("客户合同")
    private String customerContract;

    @Schema(description = "材料编号", example = "CL202501010001")
    @ExcelProperty("材料编号")
    private String materialNumber;

    // ========== 来源出库单明细信息 ==========
    @Schema(description = "产品名称", example = "液化石油气")
    @ExcelProperty("产品名称")
    private String materialName;

    @Schema(description = "钢瓶代码/集装箱编号", example = "GB202501010001")
    @ExcelProperty("钢瓶代码/集装箱编号")
    private String packagingCode;

    @Schema(description = "包装类型", example = "钢瓶")
    @ExcelProperty("包装类型")
    private String packagingTypeName;

    @Schema(description = "包装规格", example = "50KG")
    @ExcelProperty("包装规格")
    private String packagingSpecName;

    @Schema(description = "挂牌车号", example = "京A12345")
    @ExcelProperty("挂牌车号")
    private String plateNumber;

    @Schema(description = "安装说明", example = "请按照标准流程安装")
    @ExcelProperty("安装说明")
    private String installationInstructions;

    @Schema(description = "归属公司", example = "兴洋科技")
    @ExcelProperty("归属公司")
    private String ownerCompany;

    @Schema(description = "空瓶重", example = "15.5")
    @ExcelProperty("空瓶重")
    private BigDecimal tareWeight;

    @Schema(description = "毛重", example = "65.5")
    @ExcelProperty("毛重")
    private BigDecimal grossWeight;

    @Schema(description = "标准充装量", example = "50.0")
    @ExcelProperty("标准充装量")
    private BigDecimal outQuantity;

    @Schema(description = "货物完好", example = "完好")
    @ExcelProperty("货物完好")
    private String goodsCondition;

    @Schema(description = "备注", example = "无异常")
    @ExcelProperty("备注")
    private String remark;

    // ========== 关联ID信息 ==========
    @Schema(description = "出库单ID", example = "1001")
    private Long salesOutboundId;

    @Schema(description = "出库单明细ID", example = "2001")
    private Long salesOutboundItemId;

    @Schema(description = "发货通知单ID", example = "3001")
    private Long shippingId;

}
