package cn.iocoder.yudao.module.erp.controller.admin.shipping.vo;

import cn.iocoder.yudao.module.erp.constant.DTOCommonValidationConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 发货通知单新增 DTO")
@Data
public class CreateShippingDTO {

    @Schema(description = "交货日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "交货日期不能为空")
    private LocalDateTime deliveryDate;

    @Schema(description = "业务部门", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务部门不能为空")
    private Long businessDepartment;

    @Schema(description = "业务员", requiredMode = Schema.RequiredMode.REQUIRED, example = "12957")
    @NotNull(message = "业务员不能为空")
    private Long businessUserId;

    @Schema(description = "备注", example = "你猜")
    @Size(max = DTOCommonValidationConstants.MAX_MULTI_LINE_INPUT_LENGTH, message = "备注长度不能超过 {value}")
    private String remark;

    @Schema(description = "客户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25496")
    @NotNull(message = "客户id不能为空")
    private Long customerId;

    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "客户名称不能为空")
    private String customerName;

    @Schema(description = "联系人")
    private String customerContact;

    @Schema(description = "联系电话")
    private String customerContactPhone;

    @Schema(description = "客户收货地址id", example = "16561")
    private Long customerReceiveAddressId;

    @Schema(description = "客户收货地区")
    private Integer customerReceiveArea;

    @Schema(description = "客户收货地址")
    private String customerReceiveAddress;

    @Schema(description = "客户收货联系人")
    private String customerReceiveContact;

    @Schema(description = "客户收货联系人电话")
    private String customerReceiveContactPhone;

    @Schema(description = "承运商id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31823")
    @NotNull(message = "承运商id不能为空")
    private Long carrierId;

    @Schema(description = "承运商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "承运商名称不能为空")
    private String carrierName;

    @Schema(description = "承运商司机id", example = "20441")
    private Long carrierDriverId;

    @Schema(description = "承运商司机姓名", example = "王五")
    private String carrierDriverName;

    @Schema(description = "承运商司机电话")
    private String carrierDriverPhone;

    @Schema(description = "发货通知单行列表")
    @Valid
    @Size(min = DTOCommonValidationConstants.MIN_POSITIVE_INTEGER, message = "请设置发货通知明细")
    private List<ShippingLineDTO> shippingLines;

}