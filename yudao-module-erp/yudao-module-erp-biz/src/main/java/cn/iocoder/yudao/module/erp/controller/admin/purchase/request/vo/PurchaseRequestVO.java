package cn.iocoder.yudao.module.erp.controller.admin.purchase.request.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 采购申请单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PurchaseRequestVO extends SimplePurchaseRequestVO {

    @Schema(description = "申请部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long requestDeptId;

    @Schema(description = "申请人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long requestUserId;

    @Schema(description = "备注", example = "紧急采购申请")
    private String remark;

    @Schema(description = "物料类型", example = "general")
    private String materialType;

    @Schema(description = "附件URL列表")
    private List<String> attachmentUrls;

    @Schema(description = "采购申请单明细")
    private List<PurchaseRequestItemVO> items;

}
