package cn.iocoder.yudao.module.erp.controller.admin.base.vo.material;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 物料档案 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MaterialMasterDataVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8366")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "物料分类Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9718")
    @ExcelProperty("物料分类")
    private Long categoryId;

    /**
     * 物料分类名称
     */
    @Schema(description = "物料分类名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "测试")
    private String categoryName;

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单位")
    private String unitOfMeasure;

    @Schema(description = "规格型号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("规格型号")
    private String specification;

    @Schema(description = "是否包装物", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否包装物")
    private Boolean packaging;

    @Schema(description = "是否唯一码管理", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否唯一码管理")
    private Boolean uniqueCodeManaged;

    @Schema(description = "包装单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("包装单位")
    private String packagingUom;

    @Schema(description = "包装数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("包装数量")
    private BigDecimal packagingQuantity;

    @Schema(description = "是否包装产品", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否包装产品")
    private Boolean packagedProduct;

    @Schema(description = "是否开启库存控制", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否开启库存控制")
    private Boolean inventoryControl;

    @Schema(description = "是否开启安全库存控制", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否开启安全库存控制")
    private Boolean safetyInventoryControl;

    @Schema(description = "是否开启最小库存控制", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否开启最小库存控制")
    private Boolean minimumInventoryControl;

    @Schema(description = "是否开启最大库存控制", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否开启最大库存控制")
    private Boolean maximumInventoryControl;

    @Schema(description = "最小库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最小库存数量")
    private BigDecimal minimumInventoryQuantity;

    @Schema(description = "最大库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最大库存数量")
    private BigDecimal maximumInventoryQuantity;

    @Schema(description = "安全库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("安全库存数量")
    private BigDecimal safetyInventoryQuantity;

    @Schema(description = "状态（停用/启用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态（停用/启用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    @Schema(description = "包装类型Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long packagingTypeId;

    @Schema(description = "包装规格Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long packagingSpecId;

}