package cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 采购退货出库单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PurchaseReturnedOutboundVO extends SimplePurchaseReturnedOutboundVO {

    @Schema(description = "采购退货出库单明细")
    private List<PurchaseReturnedOutboundItemVO> items;

}
