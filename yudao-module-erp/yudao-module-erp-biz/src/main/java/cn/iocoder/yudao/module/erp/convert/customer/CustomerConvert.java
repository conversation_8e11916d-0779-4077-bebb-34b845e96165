package cn.iocoder.yudao.module.erp.convert.customer;

import cn.iocoder.yudao.module.erp.controller.admin.customer.vo.CreateCustomerDTO;
import cn.iocoder.yudao.module.erp.controller.admin.customer.vo.CustomerVO;
import cn.iocoder.yudao.module.erp.controller.admin.customer.vo.SimpleCustomerVO;
import cn.iocoder.yudao.module.erp.controller.admin.customer.vo.UpdateCustomerDTO;
import cn.iocoder.yudao.module.erp.convert.base.organization.OrganizationCommonFiledConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.customer.CustomerDO;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
@DecoratedWith(CustomerConvertDecorator.class)
public interface CustomerConvert extends OrganizationCommonFiledConvert<CustomerDO> {
    CustomerDO toDO(CreateCustomerDTO createCustomerDTO);

    CustomerVO toVO(CustomerDO customerDO);

    CustomerDO toDO(UpdateCustomerDTO updateCustomerDTO);

    List<CustomerVO> toVOList(List<CustomerDO> customerDOList);

    SimpleCustomerVO toSimpleVO(CustomerDO customerDO);

    List<SimpleCustomerVO> toSimpleVOList(List<CustomerDO> customerDOList);
}
