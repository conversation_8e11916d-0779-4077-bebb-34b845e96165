package cn.iocoder.yudao.module.erp.service.base.organization;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.OrganizationIdentitySpecEnum;
import cn.iocoder.yudao.module.erp.controller.admin.base.organization.vo.CreateOrganizationDTO;
import cn.iocoder.yudao.module.erp.controller.admin.base.organization.vo.OrganizationVO;
import cn.iocoder.yudao.module.erp.controller.admin.base.organization.vo.SearchOrganizationDTO;
import cn.iocoder.yudao.module.erp.controller.admin.base.organization.vo.UpdateOrganizationDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.base.organization.OrganizationDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.base.organization.OrganizationIdentityDO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;

/**
 * 组织：是客户，供应商等的公共信息 Service 接口
 *
 * <AUTHOR>
 */
public interface OrganizationService {

    /**
     * 创建组织：是客户，供应商等的公共信息
     *
     * @param createDTO 创建信息
     * @return Id
     */
    Long create(@Valid CreateOrganizationDTO createDTO);

    /**
     * 更新组织：是客户，供应商等的公共信息
     *
     * @param updateDTO 更新信息
     */
    void update(Long id, @Valid UpdateOrganizationDTO updateDTO);

    /**
     * 删除组织：是客户，供应商等的公共信息
     *
     * @param id Id
     */
    void delete(Long id);

    /**
     * 获得组织：是客户，供应商等的公共信息
     *
     * @param id Id
     * @return 组织：是客户，供应商等的公共信息
     */
    OrganizationVO get(Long id);

    /**
     * 获得组织：是客户，供应商等的公共信息分页
     *
     * @param searchDTO 分页查询
     * @return 组织：是客户，供应商等的公共信息分页
     */
    PageResult<OrganizationVO> page(SearchOrganizationDTO searchDTO);

    // ==================== 子表（组织身份：供应商和客户的身份信息） ====================

    /**
     * 获得组织身份：供应商和客户的身份信息列表
     *
     * @param organizationId 组织ID
     * @return 组织身份：供应商和客户的身份信息列表
     */
    List<OrganizationIdentityDO> getOrganizationIdentityListByOrganizationId(Long organizationId);

    /**
     * 验证组织是否存在
     *
     * @param organizationId
     */
    OrganizationDO validateOrganizationExists(Long organizationId);


    /**
     * 验证名称是否有相似
     *
     * @param name
     */
    void validateNameLike(@NotEmpty(message = "名称不能为空") String name);

    /**
     * 验证名称是否重复
     *
     * @param specType
     * @param organizationIdentitySpecId
     * @param name
     */
    void validateNameDuplicate(String specType, Long organizationIdentitySpecId, @NotEmpty(message = "名称不能为空") String name);

    /**
     * 验证简称是否重复
     *
     * @param specType
     * @param organizationIdentitySpecId
     * @param shortName
     */
    void validateShortNameDuplicate(String specType, Long organizationIdentitySpecId, @NotEmpty(message = "客户简称") String shortName);

    void validateNameDuplicate(Long organizationId, @NotEmpty(message = "名称不能为空") String name);

    void validateShortNameDuplicate(Long organizationId, @NotEmpty(message = "客户简称") String shortName);

    /**
     * 根据spec和specId查询organizationId
     *
     * @param sourceSpec
     * @param sourceId
     * @return
     */
    Long getOrganizationIdBySpecAndSpecId(String sourceSpec, Long sourceId);

    void bindOrganization(OrganizationIdentitySpecEnum organizationIdentitySpecEnum, Long specId, Long organizationId);

    void unbindOrganizationBySpecAndSpecId(String spec, Long specId);

    Long getSpecIdByOrganizationIdAndSpecType(Long organizationIdBySpecAndSpecId, String spec);
}