package cn.iocoder.yudao.module.erp.convert.abc;



import cn.iocoder.yudao.module.erp.controller.admin.abc.vo.*;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.PurchaseReturnedOutboundDO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface  PurchaseReturnedOutboundConvert {

    PurchaseReturnedOutboundDO toDO(CreatePurchaseReturnedOutboundDTO creatDTO);

    PurchaseReturnedOutboundDO toDO(UpdatePurchaseReturnedOutboundDTO updateDTO);

    PurchaseReturnedOutboundVO toVO(PurchaseReturnedOutboundDO purchaseReturnedOutboundDO);

    List<PurchaseReturnedOutboundVO> toVO(List<PurchaseReturnedOutboundDO> purchaseReturnedOutboundDOList);

    SimplePurchaseReturnedOutboundVO toSimpleVO(PurchaseReturnedOutboundDO purchaseReturnedOutboundDO);

    List<SimplePurchaseReturnedOutboundVO> toSimpleVO(List<PurchaseReturnedOutboundDO> purchaseReturnedOutboundDOList);
}