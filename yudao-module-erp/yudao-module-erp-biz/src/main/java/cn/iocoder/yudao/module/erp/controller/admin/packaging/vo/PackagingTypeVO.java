package cn.iocoder.yudao.module.erp.controller.admin.packaging.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 包装类型VO")
@Data
public class PackagingTypeVO {
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "名称", example = "气瓶")
    private String name;

    // 包装类型编号
    @Schema(description = "包装类型编号", example = "XJ99222")
    private String code;

    // 包装类型类别
    @Schema(description = "包装类型类别", example = "气瓶")
    private String category;

    // 包装类型排序
    @Schema(description = "包装类型排序", example = "1")
    private Integer sort;

    // 包装类型状态
    @Schema(description = "状态（停用/启用）", example = "0", defaultValue = "0")
    private Integer status;
}
