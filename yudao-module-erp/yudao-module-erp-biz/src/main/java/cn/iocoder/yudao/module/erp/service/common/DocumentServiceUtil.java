package cn.iocoder.yudao.module.erp.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.DocumentDO;
import cn.iocoder.yudao.module.erp.constant.BusinessStatusEnum;
import cn.iocoder.yudao.module.erp.constant.BusinessTypeEnum;
import cn.iocoder.yudao.module.erp.constant.DocumentStatusEnum;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataService;
import cn.iocoder.yudao.module.erp.service.customer.CustomerService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;

@Component
public class DocumentServiceUtil {

    @Resource
    private SerialNumberService serialNumberService;

    @Resource
    @Lazy
    private CustomerService customerService;

    @Resource
    @Lazy
    private MaterialMasterDataService materialMasterDataService;

    /**
     * 填充单据相关信息
     *
     * @param documentDO
     */
    public void fillDocumentInfoWhenCreate(DocumentDO documentDO, BusinessTypeEnum businessTypeEnum) {
        documentDO.setCode(serialNumberService.generateOrderNumber(businessTypeEnum.getPrefix()));
        documentDO.setDocumentStatus(DocumentStatusEnum.DRAFT.getStatus());
        documentDO.setBusinessStatus(BusinessStatusEnum.NORMAL.getStatus());
        documentDO.setBusinessType(businessTypeEnum.getType());
    }

    /**
     * 校验客户是否存在
     * 1,除了销售订单，后续的备货单，发货单等不需要验证客户是否启用，只要求客户存在
     *
     * @param customerId
     */
    public void validateCustomer(Long customerId) {
        customerService.validateCustomerExists(customerId);
    }


    /**
     * 从明细项中提取物料ID并验证
     */
    public <T> void validateMaterialsFromItems(List<T> items, Function<T, Long> materialIdExtractor) {
        List<Long> materialIds = items.stream()
                .map(materialIdExtractor)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        validateMaterials(materialIds);
    }

    /**
     * 验证物料ID列表中的物料是否存在且启用
     */
    public void validateMaterials(List<Long> materialIds) {
        List<Long> validIds = materialIds.stream()
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        if (CollUtil.isNotEmpty(validIds)) {
            materialMasterDataService.validateExistAndEnableByIds(validIds);
        }
    }

}
