package cn.iocoder.yudao.module.erp.controller.admin.purchase.order;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.order.vo.*;
import cn.iocoder.yudao.module.erp.service.purchase.order.PurchaseOrderService;
import db.migration.PermissionName;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 采购订单")
@RestController
@RequestMapping("/erp/purchase-order")
@Validated
public class PurchaseOrderController {

    @Resource
    private PurchaseOrderService purchaseOrderService;

    @PostMapping
    @Operation(summary = "创建采购订单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-order:create')")
    @PermissionName(name = "创建采购订单", sort = 1)
    public CommonResult<Long> create(@Valid @RequestBody CreatePurchaseOrderDTO createDTO) {
        return success(purchaseOrderService.create(createDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新采购订单")
    @PermissionName(name = "更新采购订单", sort = 2)
    @PreAuthorize("@ss.hasPermission('erp:purchase-order:update')")
    public CommonResult<Boolean> update(@PathVariable("id") Long id, @Valid @RequestBody UpdatePurchaseOrderDTO updateDTO) {
        purchaseOrderService.update(id, updateDTO);
        return success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除采购订单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-order:delete')")
    @PermissionName(name = "删除采购订单", sort = 3)
    public CommonResult<Boolean> delete(@PathVariable("id") Long id) {
        purchaseOrderService.delete(id);
        return success(true);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取采购订单详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:purchase-order:view')")
    @PermissionName(name = "查看采购订单详情", sort = 4)
    public CommonResult<PurchaseOrderVO> get(@PathVariable("id") Long id) {
        return success(purchaseOrderService.get(id));
    }

    @GetMapping("/page")
    @Operation(summary = "查询采购订单分页")
    @PreAuthorize("@ss.hasPermission('erp:purchase-order:query')")
    @PermissionName(name = "查询采购订单-单据模式", sort = 9)
    public CommonResult<PageResult<SimplePurchaseOrderVO>> page(@Valid SearchPurchaseOrderDTO searchDTO) {
        PageResult<SimplePurchaseOrderVO> pageResult = purchaseOrderService.page(searchDTO);
        return success(pageResult);
    }

    @GetMapping("/item/page")
    @Operation(summary = "查询采购订单明细分页")
    @PreAuthorize("@ss.hasAnyPermissions('erp:purchase-order:item:query','erp:purchase-arrival:create','erp:purchase-arrival:update')")
    @PermissionName(name = "查询采购订单-明细模式", sort = 8)
    public CommonResult<PageResult<ItemWithPurchaseOrderVO>> pageItem(@Valid SearchItemPageDTO searchDTO) {
        PageResult<ItemWithPurchaseOrderVO> pageResult = purchaseOrderService.pageItem(searchDTO);
        return success(pageResult);
    }

    // ==================== 业务状态操作 ====================

    @PutMapping("/{id}/close")
    @Operation(summary = "关闭采购订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:purchase-order:close')")
    @PermissionName(name = "关闭/取消关闭采购订单", sort = 6)
    public CommonResult<Boolean> close(@PathVariable("id") Long id) {
        purchaseOrderService.close(id);
        return success(true);
    }

    @PutMapping("/{id}/cancel-close")
    @Operation(summary = "取消关闭采购订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:purchase-order:close')")
    public CommonResult<Boolean> cancelClose(@PathVariable("id") Long id) {
        purchaseOrderService.cancelClose(id);
        return success(true);
    }

    @PutMapping("/{id}/suspend")
    @Operation(summary = "挂起采购订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:purchase-order:suspend')")
    @PermissionName(name = "挂起/取消挂起采购订单", sort = 7)
    public CommonResult<Boolean> suspend(@PathVariable("id") Long id) {
        purchaseOrderService.suspend(id);
        return success(true);
    }

    @PutMapping("/{id}/cancel-suspend")
    @Operation(summary = "取消挂起采购订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:purchase-order:suspend')")
    public CommonResult<Boolean> cancelSuspend(@PathVariable("id") Long id) {
        purchaseOrderService.cancelSuspend(id);
        return success(true);
    }

    @PutMapping("/{id}/approve")
    @Operation(summary = "审核采购订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:purchase-order:approve')")
    @PermissionName(name = "审核/取消审核采购订单", sort = 5)
    public CommonResult<Boolean> approve(@PathVariable("id") Long id) {
        purchaseOrderService.approve(id);
        return success(true);
    }

    @PutMapping("/{id}/cancel-approve")
    @Operation(summary = "取消审核采购订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:purchase-order:approve')")
    public CommonResult<Boolean> cancelApprove(@PathVariable("id") Long id) {
        purchaseOrderService.cancelApprove(id);
        return success(true);
    }

}