package cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo;

import cn.iocoder.yudao.module.erp.controller.admin.inventory.common.CommonInventoryItemDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 采购退货出库单明细 DTO")
@Data
public class PurchaseReturnedOutboundItemDTO extends CommonInventoryItemDTO {
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15640")
    private Long id;

    @Schema(description = "出入库单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32103")
    @NotNull(message = "出入库单ID不能为空")
    private Long returnedOutboundId;


    @Schema(description = "出库数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "出库数量不能为空")
    private BigDecimal outQuantity;

    @Schema(description = "批次号")
    private String batchNumber;

    @Schema(description = "唯一码")
    private String uniqueCode;


    @Schema(description = "退货入库单Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28215")
    private Long purchaseInboundId;

    @Schema(description = "退货入库单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "THRK202507030001")
    private String purchaseInboundCode;

    @Schema(description = "退货入库单明细Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28215")
    private Long purchaseInboundItemId;


}