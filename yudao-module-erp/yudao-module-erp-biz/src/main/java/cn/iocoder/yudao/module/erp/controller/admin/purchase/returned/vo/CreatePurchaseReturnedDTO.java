package cn.iocoder.yudao.module.erp.controller.admin.purchase.returned.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 采购退货通知单新增 DTO")
@Data
public class CreatePurchaseReturnedDTO {

    @Schema(description = "退货通知单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "退货日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "退货日期不能为空")
    private LocalDateTime returnedDate;

    @Schema(description = "供应商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7740")
    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;

    @Schema(description = "供应商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "供应商名称不能为空")
    private String supplierName;

    @Schema(description = "物料类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "物料类型不能为空")
    private String materialType;

    @Schema(description = "业务部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31955")
    @NotNull(message = "业务部门ID不能为空")
    private Long businessDepartmentId;

    @Schema(description = "业务部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String businessDepartmentName;

    @Schema(description = "业务员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27867")
    @NotNull(message = "业务员ID不能为空")
    private Long businessUserId;

    @Schema(description = "业务员姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "业务员姓名不能为空")
    private String businessUserName;

    @Schema(description = "来源到货单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30409")
    @NotNull(message = "来源到货单ID不能为空")
    private Long purchaseArrivalId;

    @Schema(description = "来源到货单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "来源到货单号不能为空")
    private String purchaseArrivalCode;

    @Schema(description = "单据状态：1编制中 2审核中 3已审核 4退回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer documentStatus;

    @Schema(description = "业务状态：1正常 2关闭 3挂起", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer businessStatus;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;


    @Schema(description = "采购退货通知单明细列表")
    private List<PurchaseReturnedItemDTO> items;
}