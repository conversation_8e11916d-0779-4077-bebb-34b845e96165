package cn.iocoder.yudao.module.erp.controller.admin.requisition.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 领料单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RequisitionOrderVO extends SimpleRequisitionOrderVO {

    @Schema(description = "领料单明细")
    private List<RequisitionOrderItemVO> items;

}
