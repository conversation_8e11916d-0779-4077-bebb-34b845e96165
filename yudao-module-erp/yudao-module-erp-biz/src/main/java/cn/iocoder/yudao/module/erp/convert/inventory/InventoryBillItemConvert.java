package cn.iocoder.yudao.module.erp.convert.inventory;

import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.PurchaseReturnedOutboundItemDTO;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.PurchaseReturnedOutboundItemVO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryBillItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface InventoryBillItemConvert {

    List<InventoryBillItemDO> fromPurchaseReturnedOutboundItemDO(List<PurchaseReturnedOutboundItemDTO> items);


    InventoryBillItemDO fromPurchaseReturnedOutboundItemDO(PurchaseReturnedOutboundItemDTO items);

    List<PurchaseReturnedOutboundItemVO> toPurchaseReturnedOutboundItemVO(List<InventoryBillItemDO> billItemDOS);

    @Mappings({
            @Mapping(target = "returnedOutboundId", source = "billId")
    })
    PurchaseReturnedOutboundItemVO toPurchaseReturnedOutboundItemVO(InventoryBillItemDO billItemDOS);
    
}