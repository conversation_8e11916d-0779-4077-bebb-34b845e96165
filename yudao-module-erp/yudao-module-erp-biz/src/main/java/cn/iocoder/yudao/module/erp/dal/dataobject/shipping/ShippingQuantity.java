package cn.iocoder.yudao.module.erp.dal.dataobject.shipping;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 发货通知单：
 * 发送数量
 */
@Data
@AllArgsConstructor
public class ShippingQuantity {
    /**
     * 发货通知单-itemId
     */
    private Long shippingItemId;

    /**
     * 销售订单-itemId
     */
    private Long salesOrderItemId;


    /**
     * 发货通知单明细中用户输入的发货数量
     */
    private BigDecimal quantity;
}
