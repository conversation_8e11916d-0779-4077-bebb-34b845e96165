package cn.iocoder.yudao.module.erp.controller.admin.supplier.vo;

import cn.iocoder.yudao.module.erp.controller.admin.base.organization.vo.OrganizationCommonFiledDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 供应商新增 DTO")
@Data
public class CreateSupplierDTO extends OrganizationCommonFiledDTO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "编号不能为空")
    private String code;
    
    @Schema(description = "结算货币", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "结算货币不能为空")
    private String settlementCurrency;


}