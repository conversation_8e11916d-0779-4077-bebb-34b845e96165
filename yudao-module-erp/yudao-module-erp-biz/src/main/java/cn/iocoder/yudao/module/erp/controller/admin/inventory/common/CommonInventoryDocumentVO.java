package cn.iocoder.yudao.module.erp.controller.admin.inventory.common;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CommonInventoryDocumentVO {
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15613")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "单据号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单据号")
    private String code;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("业务类型")
    private String businessType;

    @Schema(description = "0编制中 1审核通过 2回退", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("0编制中 1审核通过 2回退")
    private Integer documentStatus;

    @Schema(description = "业务状态：0关闭 1正常 2挂起", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("业务状态：0关闭 1正常 2挂起")
    private Integer businessStatus;


    @Schema(description = "仓库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31837")
    @ExcelProperty("仓库ID")
    private Long warehouseId;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("仓库名称")
    private String warehouseName;


    @Schema(description = "业务部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13559")
    @ExcelProperty("业务部门ID")
    private Long businessDepartmentId;

    @Schema(description = "业务部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("业务部门名称")
    private String businessDepartmentName;

    @Schema(description = "业务员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28466")
    @ExcelProperty("业务员ID")
    private Long businessUserId;

    @Schema(description = "业务员姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("业务员姓名")
    private String businessUserName;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
