package cn.iocoder.yudao.module.erp.service.common;

import cn.iocoder.yudao.module.erp.controller.admin.outbound.vo.SalesOutboundItemVO;
import cn.iocoder.yudao.module.erp.controller.admin.returned.vo.ReturnedOrderItemVO;
import cn.iocoder.yudao.module.erp.controller.admin.sales.vo.SalesOrderLineVO;
import cn.iocoder.yudao.module.erp.controller.admin.shipping.vo.ShippingLineVO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.PurchaseInboundQuantity;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.returned.ReturnedInboundQuantity;
import cn.iocoder.yudao.module.erp.dal.dataobject.outbound.SalesOutboundQuantity;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.arrival.ArrivalQuantity;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.order.PurchaseQuantity;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.returned.PurchaseReturnedQuantity;
import cn.iocoder.yudao.module.erp.dal.dataobject.returned.ReturnedQuantity;
import cn.iocoder.yudao.module.erp.dal.dataobject.shipping.ShippingQuantity;
import cn.iocoder.yudao.module.erp.service.inbound.purchase.PurchaseInboundService;
import cn.iocoder.yudao.module.erp.service.inbound.returned.ReturnedInboundService;
import cn.iocoder.yudao.module.erp.service.outbound.SalesOutboundService;
import cn.iocoder.yudao.module.erp.service.purchase.arrival.PurchaseArrivalService;
import cn.iocoder.yudao.module.erp.service.purchase.order.PurchaseOrderService;
import cn.iocoder.yudao.module.erp.service.purchase.request.PurchaseRequestService;
import cn.iocoder.yudao.module.erp.service.returned.ReturnedOrderService;
import cn.iocoder.yudao.module.erp.service.sales.SalesOrderService;
import cn.iocoder.yudao.module.erp.service.shipping.ShippingService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.*;

@Component
public class DocumentRelationHelper {

    @Resource
    @Lazy
    private ShippingService shippingService;


    @Resource
    @Lazy
    private SalesOrderService salesOrderService;

    @Resource
    @Lazy
    private SalesOutboundService salesOutboundService;

    @Resource
    @Lazy
    private ReturnedOrderService returnedOrderService;

    @Resource
    @Lazy
    private ReturnedInboundService returnedInboundService;

    @Resource
    @Lazy
    private PurchaseRequestService purchaseRequestService;

    @Resource
    @Lazy
    private PurchaseArrivalService purchaseArrivalService;

    @Resource
    @Lazy
    private PurchaseOrderService purchaseOrderService;

    @Resource
    @Lazy
    private PurchaseInboundService purchaseInboundService;


    /**
     * 校验销售订单是否可以取消审核
     *
     * @param salesOrderId
     */
    public void validateSalesOrderCanCancelApprove(Long salesOrderId) {
        Boolean flag = shippingService.isShippingBySalesOrderId(salesOrderId);
        if (flag) {
            throw exception(SALES_ORDER_IN_SHIPPING, salesOrderId);
        }
    }


    /**
     * 发货通知单审核
     * <br/>1,验证发货梳理是否合法
     * <br/>2,修改销售订单的已发货数量
     * 发货通知单审核通过时，尝试修改销售订单的已发货数量
     *
     * @param shippingQuantityList
     */
    public void tryUpdateShippedQuantityWhenShippingApprove(List<ShippingQuantity> shippingQuantityList) {
        shippingQuantityList.forEach(shippingQuantity -> {
            SalesOrderLineVO salesOrderLine = salesOrderService.selectItemBySalesOrderItemId(shippingQuantity.getSalesOrderItemId());
            //如果发货数量大于未发货数量
            if (shippingQuantity.getQuantity().compareTo(salesOrderLine.getUnshippedNum()) > 0) {
                throw exception(SALES_ORDER_LINE_SHIPPING_QUANTITY_ERROR, shippingQuantity.getQuantity());
            }
            salesOrderService.updateShippedQuantityWhenShippingApprove(shippingQuantity.getSalesOrderItemId(), shippingQuantity.getQuantity());
        });
    }


    /**
     * 发货通知单取消审核
     * 发货通知单取消审核时，修改销售订单的已发货数量
     *
     * @param shippingQuantityList
     */
    public void updateShippedQuantityWhenShippingCancelApprove(List<ShippingQuantity> shippingQuantityList) {
        shippingQuantityList.forEach(shippingQuantity -> {
            salesOrderService.updateShippedQuantityWhenShippingCancelApprove(shippingQuantity.getSalesOrderItemId(), shippingQuantity.getQuantity());
        });
    }

    /**
     * 验证发货通知单是否可以取消审核
     *
     * @param shippingId
     */
    public void validateShippingCanCancelApprove(Long shippingId) {
        //根据发货通知单查询是否已经有销售出库单
        if (salesOutboundService.isOutboundByShippingId(shippingId)) {
            throw exception(SHIPPING_IN_OUTBOUND, shippingId);
        }
    }

    /**
     * 销售出库单审核通过时，尝试修改发货通知单的已出库数量
     * 业务上是允许一个出库单中出现相同的发货通知单明细的。比如发100桶硅烷，因为涉及到包装物，所以出库记录有100条，这100条出库记录都记录的同一个出库通知单itemId
     *
     * @param salesOutboundQuantities
     */
    public void tryUpdateOutboundQuantityWhenOutboundApprove(List<SalesOutboundQuantity> salesOutboundQuantities) {
        //收集 shippingItemId
        Set<Long> shippingItemIds = salesOutboundQuantities.stream().map(SalesOutboundQuantity::getShippingItemId).collect(Collectors.toSet());
        List<ShippingLineVO> shippingLineVOS = shippingService.selectItemListByItemIds(shippingItemIds.stream().toList());
        //shippingLineVOS 安装id转成Map
        Map<Long, ShippingLineVO> shippingItemIdShippingLineMap = shippingLineVOS.stream().collect(Collectors.toMap(ShippingLineVO::getId, itemVO -> itemVO));
//        salesOutboundQuantities按照shippingItemId 分组，计算每组中quantity的和
        Map<Long, BigDecimal> groupedQuantity = salesOutboundQuantities.stream()
                .collect(Collectors.groupingBy(SalesOutboundQuantity::getShippingItemId, Collectors.mapping(SalesOutboundQuantity::getQuantity, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));


        salesOutboundQuantities.forEach(salesOutboundQuantity -> {
            ShippingLineVO shippingLineVO = shippingItemIdShippingLineMap.get(salesOutboundQuantity.getShippingItemId());
            BigDecimal outboundQuantity = groupedQuantity.get(salesOutboundQuantity.getShippingItemId());
            if (outboundQuantity.compareTo(shippingLineVO.getUnshippedQuantity()) > 0) {
                throw exception(SHIPPING_LINE_OUT_QUANTITY_EXCEEDS_UNSHIPPED, outboundQuantity, shippingLineVO.getUnshippedQuantity());
            }
            shippingService.updateOutboundQuantityWhenOutboundApprove(salesOutboundQuantity.getShippingItemId(), outboundQuantity);
        });
    }

    /**
     * 销售出库单取消审核
     *
     * @param salesOutboundQuantities
     */
    public void updateOutboundQuantityWhenOutboundCancelApprove(List<SalesOutboundQuantity> salesOutboundQuantities) {
        salesOutboundQuantities.forEach(salesOutboundQuantity -> {
            shippingService.updateOutboundQuantityWhenOutboundCancelApprove(salesOutboundQuantity.getShippingItemId(), salesOutboundQuantity.getQuantity());
        });
    }

    /**
     * 校验销售出库单是否可以取消审核
     * 如有销退单，则不允许取消审核
     *
     * @param id
     */
    public void validateSalesOutboundCanCancelApprove(Long id) {
        if (returnedOrderService.isReturnedOrderBySalesOutboundId(id)) {
            throw exception(SALES_OUTBOUND_IN_RETURNED_ORDER, id);
        }
    }

    /**
     * 退货通知单审核
     * 尝试修改销售出库单的已退数量
     *
     * @param changedQuantity
     */
    public void tryUpdateReturnedQuantityWhenReturnedApprove(List<ReturnedQuantity> changedQuantity) {
        changedQuantity.forEach(returnedQuantity -> {
            SalesOutboundItemVO salesOutboundItem = salesOutboundService.selectItemByItemId(returnedQuantity.getSalesOutboundItemId());
            if (returnedQuantity.getQuantity().compareTo(salesOutboundItem.getUnreturnedQuantity()) > 0) {
                throw exception(SALES_OUTBOUND_ITEM_RETURNED_QUANTITY_NOT_ENOUGH, returnedQuantity.getQuantity(), salesOutboundItem.getUnreturnedQuantity());
            }
            salesOutboundService.updateReturnedQuantityWhenReturnedApprove(returnedQuantity.getSalesOutboundItemId(), returnedQuantity.getQuantity());
        });
    }

    /**
     * 退货通知单取消审核
     *
     * @param changedQuantity
     */
    public void updateReturnedQuantityWhenReturnedCancelApprove(List<ReturnedQuantity> changedQuantity) {
        changedQuantity.forEach(returnedQuantity -> {
            salesOutboundService.updateReturnedQuantityWhenReturnedCancelApprove(returnedQuantity.getSalesOutboundItemId(), returnedQuantity.getQuantity());
        });
    }

    /**
     * 验证退货通知单是否可以取消审核
     *
     * @param id
     */
    public void validateReturnedOrderCanCancelApprove(Long id) {
        if (returnedInboundService.isInboundByReturnedOrderId(id)) {
            throw exception(RETURNED_ORDER_IN_RETURNED_INBOUND, id);
        }
    }

    public void tryUpdateInboundQuantityWhenReturnedInboundApprove(List<ReturnedInboundQuantity> returnedInboundQuantityList) {
        returnedInboundQuantityList.forEach(returnedInboundQuantity -> {
            ReturnedOrderItemVO returnedOrderItem = returnedOrderService.selectItemByItemId(returnedInboundQuantity.getReturnedOrderItemId());
            if (returnedInboundQuantity.getQuantity().compareTo(returnedOrderItem.getUnconfirmedInboundQuantity()) > 0) {
                throw exception(RETURNED_INBOUND_IN_QUANTITY_NOT_ENOUGH, returnedInboundQuantity.getQuantity(), returnedOrderItem.getUnconfirmedInboundQuantity());
            }
            returnedOrderService.updateInboundQuantityWhenReturnedInboundApprove(returnedInboundQuantity.getReturnedOrderItemId(), returnedInboundQuantity.getQuantity());
        });
    }

    public void updateInboundQuantityWhenReturnedInboundCancelApprove(List<ReturnedInboundQuantity> returnedInboundQuantityList) {
        returnedInboundQuantityList.forEach(returnedInboundQuantity -> {
            returnedOrderService.updateInboundQuantityWhenReturnedInboundCancelApprove(returnedInboundQuantity.getReturnedOrderItemId(), returnedInboundQuantity.getQuantity());
        });
    }

    /**
     * 采购订单审核
     * 尝试修改采购申请单明细的已采购数量
     *
     * @param purchaseQuantityList
     */
    public void tryUpdateQuantityWhenPurchaseOrderApprove(List<PurchaseQuantity> purchaseQuantityList) {
        //先校验purchaseRequestItemId相同的记录，采购数量不能超过申请数量
        Map<Long, BigDecimal> purchaseQuantityMap = purchaseQuantityList.stream()
                .filter(purchaseQuantity -> purchaseQuantity.getPurchaseRequestItemId() != null)
                .collect(Collectors.groupingBy(PurchaseQuantity::getPurchaseRequestItemId,
                        Collectors.mapping(PurchaseQuantity::getQuantity, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        purchaseQuantityMap.forEach((purchaseRequestItemId, totalPurchaseQuantity) -> {
            purchaseRequestService.validatePurchaseQuantity(purchaseRequestItemId, totalPurchaseQuantity);
        });

        purchaseQuantityList.forEach(purchaseQuantity -> {
            if (purchaseQuantity.getPurchaseRequestItemId() != null) {
                purchaseRequestService.updatePurchaseQuantityWhenPurchaseApprove(
                        purchaseQuantity.getPurchaseRequestItemId(),
                        purchaseQuantity.getQuantity()
                );
            }
        });
    }

    /**
     * 采购订单取消审核
     * 修改采购申请单明细的已采购数量
     *
     * @param purchaseQuantityList
     */
    public void updateQuantityWhenPurchaseOrderCancelApprove(List<PurchaseQuantity> purchaseQuantityList) {
        purchaseQuantityList.forEach(purchaseQuantity -> {
            if (purchaseQuantity.getPurchaseRequestItemId() != null) {
                purchaseRequestService.updatePurchaseQuantityWhenPurchaseCancelApprove(
                        purchaseQuantity.getPurchaseRequestItemId(),
                        purchaseQuantity.getQuantity()
                );
            }
        });
    }

    public void validatePurchaseOrderCanCancelApprove(Long id) {
        if (purchaseArrivalService.isArrivalByPurchaseOrderId(id)) {
            throw exception(PURCHASE_ORDER_IN_PURCHASE_ARRIVAL);
        }
    }

    /**
     * 修改了审核的验证逻辑，现在审核的验证和新增修改是一样的。已经验证了数量等必要条件，所以这里可以直接更新数据
     *
     * @param arrivalQuantityList
     */
    public void tryUpdateArrivalQuantityWhenPurchaseArrivalApprove(List<ArrivalQuantity> arrivalQuantityList) {
        arrivalQuantityList.forEach(
                arrivalQuantity -> purchaseOrderService.updateArrivalQuantityWhenPurchaseArrivalApprove(
                        arrivalQuantity.getPurchaseOrderItemId(),
                        arrivalQuantity.getArrivalQuantity()
                )
        );
    }

    public void updateArrivalQuantityWhenPurchaseArrivalCancelApprove(List<ArrivalQuantity> arrivalQuantityList) {
        arrivalQuantityList.forEach(
                arrivalQuantity -> purchaseOrderService.updateArrivalQuantityWhenPurchaseArrivalCancelApprove(
                        arrivalQuantity.getPurchaseOrderItemId(),
                        arrivalQuantity.getArrivalQuantity()
                )
        );
    }

    public void validatePurchaseArrivalCanCancelApprove(Long id) {
        if (purchaseInboundService.isInboundByPurchaseArrivalId(id)) {
            throw exception(PURCHASE_ARRIVAL_IN_PURCHASE_INBOUND, id);
        }

    }

    /**
     * 采购入库单审核,修改到货单的入库数量
     *
     * @param inboundQuantityList
     */
    public void updateInboundQuantityWhenPurchaseInboundApprove(List<PurchaseInboundQuantity> inboundQuantityList) {
        inboundQuantityList.forEach(inboundQuantity -> {
            purchaseArrivalService.updateInboundQuantityWhenPurchaseInboundApprove(inboundQuantity.getPurchaseArrivalItemId(), inboundQuantity.getQuantity());
        });
    }

    /**
     * 采购入库单取消审核,修改到货单的入库数量
     *
     * @param inboundQuantityList
     */
    public void updateInboundQuantityWhenPurchaseInboundCancelApprove(List<PurchaseInboundQuantity> inboundQuantityList) {
        inboundQuantityList.forEach(inboundQuantity -> {
            purchaseArrivalService.updateInboundQuantityWhenPurchaseInboundCancelApprove(inboundQuantity.getPurchaseArrivalItemId(), inboundQuantity.getQuantity());
        });
    }

    /**
     * 采购退货单审核,修改采购入库单的退货数量
     *
     * @param returnedQuantityList
     */
    public void tryUpdateReturnedQuantityWhenPurchaseReturnedApprove(List<PurchaseReturnedQuantity> returnedQuantityList) {
        returnedQuantityList.forEach(returnedQuantity -> {
            purchaseInboundService.updateReturnedQuantityWhenPurchaseReturnedApprove(returnedQuantity.getPurchaseInboundItemId(), returnedQuantity.getQuantity());
        });
    }

    public void validatePurchaseReturnedCanCancelApprove(Long id) {
        //FIXME：验证是否有出库单
    }

    public void updateReturnedQuantityWhenPurchaseReturnedCancelApprove(List<PurchaseReturnedQuantity> returnedQuantityList) {
        returnedQuantityList.forEach(returnedQuantity -> {
            purchaseInboundService.updateReturnedQuantityWhenPurchaseReturnedCancelApprove(returnedQuantity.getPurchaseInboundItemId(), returnedQuantity.getQuantity());
        });
    }
}
