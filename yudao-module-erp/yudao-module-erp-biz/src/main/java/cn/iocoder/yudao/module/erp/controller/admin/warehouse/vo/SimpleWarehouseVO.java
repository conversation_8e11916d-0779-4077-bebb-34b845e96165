package cn.iocoder.yudao.module.erp.controller.admin.warehouse.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SimpleWarehouseVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18864")
    private Long id;

    @Schema(description = "仓库编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    private String name;
}
