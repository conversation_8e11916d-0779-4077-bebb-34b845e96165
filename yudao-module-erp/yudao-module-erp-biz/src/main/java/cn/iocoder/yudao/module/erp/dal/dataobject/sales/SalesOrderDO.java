package cn.iocoder.yudao.module.erp.dal.dataobject.sales;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 销售订单 DO
 *
 * <AUTHOR>
 */
@TableName("erp_sales_order")
@KeySequence("erp_sales_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 销售订单号
     */
    private String code;
    /**
     * 客户订单号
     */
    private String customerOrderCode;
    /**
     * 客户订单号状态
     */
    private Boolean customerOrderStatus;
    /**
     * 订单日期
     */
    private LocalDate orderDate;

    /**
     * 交货日期
     */
    private LocalDate deliveryDate;
    /**
     * 业务部门
     */
    private Long businessDepartment;
    /**
     * 业务员
     */
    private Long businessUserId;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 业务状态
     */
    private Integer businessStatus;
    /**
     * 单据状态
     */
    private Integer documentStatus;
    /**
     * 备注
     */
    private String remark;
    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户地区
     */
    private String customerArea;
    /**
     * 客户地址
     */
    private String customerAddress;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 销售类型：内销/外销
     */
    private String saleType;

    /**
     * 销售订单行数量合计
     */
    private BigDecimal summaryQuantity;

    /**
     * 销售订单行价税合计
     */
    private BigDecimal summaryTaxIncluded;
}