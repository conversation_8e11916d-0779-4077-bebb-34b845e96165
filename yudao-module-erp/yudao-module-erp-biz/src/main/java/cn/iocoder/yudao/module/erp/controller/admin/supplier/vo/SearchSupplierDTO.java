package cn.iocoder.yudao.module.erp.controller.admin.supplier.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import java.time.LocalDate;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 供应商分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchSupplierDTO extends PageParam {

    @Schema(description = "编号")
    private String code;

    @Schema(description = "名称", example = "王五")
    private String name;

    @Schema(description = "客户简称", example = "李四")
    private String shortName;

    @Schema(description = "地区")
    private Long area;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "财务联系人")
    private String financeContact;

    @Schema(description = "财务联系人电话")
    private String financeContactPhone;

    @Schema(description = "财务联系人邮箱")
    private String financeContactEmail;

    @Schema(description = "业务联系人")
    private String businessContact;

    @Schema(description = "业务联系电话")
    private String businessContactPhone;

    @Schema(description = "业务联系人邮箱")
    private String businessContactEmail;

    @Schema(description = "结算货币")
    private String settlementCurrency;

    @Schema(description = "供应商分类", example = "2")
    private String type;

    @Schema(description = "状态（停用/启用）", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;


}