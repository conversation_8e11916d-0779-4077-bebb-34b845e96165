package cn.iocoder.yudao.module.erp.service.base.material;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.base.vo.category.MaterialCategoryVO;
import cn.iocoder.yudao.module.erp.controller.admin.base.vo.material.*;
import cn.iocoder.yudao.module.erp.convert.base.MaterialMasterDataConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.base.MaterialMasterDataDO;
import cn.iocoder.yudao.module.erp.dal.mysql.base.MaterialMasterDataMapper;
import cn.iocoder.yudao.module.erp.service.packaging.PackagingTypeService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.*;

/**
 * 物料档案 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialMasterDataServiceImpl implements MaterialMasterDataService {

    @Resource
    private MaterialCategoryService materialCategoryService;
    @Resource
    private MaterialMasterDataMapper materialMasterDataMapper;
    @Resource
    private MaterialMasterDataConvert materialMasterDataConvert;

    @Resource
    @Lazy
    private PackagingTypeService packagingTypeService;

    @Resource
    @Lazy
    private List<MaterialMasterDataRefer> materialMasterDataRefers;

    @Override
    public Long createMaterialMasterData(CreateMaterialMasterDataDTO createDTO) {
        // 校验物料分类存在，并且是启用的
        validateMaterialCategoryExistsAndEnabled(createDTO.getCategoryId());
        // 校验编码是否重复
        validateCodeUnique(null, createDTO.getCode());
        //验证库存管理
        validateMaterialInventoryControl(createDTO);
        //验证当是包装物时，唯一码，包装单位，最大包装量必填
        validatePackagingSetting(createDTO);

        // 插入
        MaterialMasterDataDO materialMasterData = materialMasterDataConvert.toDO(createDTO);
//        processInventoryController(materialMasterData);

        materialMasterData.setStatus(CommonStatusEnum.ENABLE.getStatus());
        materialMasterDataMapper.insert(materialMasterData);
        // 返回
        return materialMasterData.getId();
    }

    private void validatePackagingSetting(BaseMaterialMasterDataDTO masterDataDTO) {
        if (!masterDataDTO.getPackaging()) {
            return;
        }

        packagingTypeService.validateSpecAndTypeEnable(masterDataDTO.getPackagingTypeId(), masterDataDTO.getPackagingSpecId());
        if (masterDataDTO.getUniqueCodeManaged() == null || masterDataDTO.getPackagingUom() == null || masterDataDTO.getPackagingQuantity() == null) {
            throw exception(MATERIAL_MASTER_DATA_PACKAGING_SETTING_ERROR);
        }

    }

    private void validateCodeUnique(Long id, String code) {
        MaterialMasterDataDO materialMasterDataDO = materialMasterDataMapper.selectByCode(code);
        if (null == materialMasterDataDO) {
            return;
        }
        if (id == null) { // 新增时，说明重复
            throw exception(MATERIAL_MASTER_DATA_CODE_DUPLICATE);
        }
        if (!Objects.equals(materialMasterDataDO.getId(), id)) {
            throw exception(MATERIAL_MASTER_DATA_CODE_DUPLICATE);
        }


    }

    private void processInventoryController(MaterialMasterDataDO materialMasterData) {
        if (!materialMasterData.getInventoryControl()) {
            closeInventoryControllerAndClearInventoryQuantities(materialMasterData);
        } else {
            if (!materialMasterData.getMinimumInventoryControl()) {
                materialMasterData.setMinimumInventoryQuantity(null);
            }
            if (!materialMasterData.getMaximumInventoryControl()) {
                materialMasterData.setMaximumInventoryQuantity(null);
            }
            if (!materialMasterData.getSafetyInventoryControl()) {
                materialMasterData.setSafetyInventoryQuantity(null);
            }
        }
    }

    @Override
    public void updateMaterialMasterData(Long id, UpdateMaterialMasterDataDTO updateDTO) {
        // 校验记录是否存在
        validateMaterialMasterDataExists(id);
        // 校验物料分类存在，并且是启用的
        validateMaterialCategoryExistsAndEnabled(updateDTO.getCategoryId());
        // 校验编码是否重复
        validateCodeUnique(id, updateDTO.getCode());
        //验证库存管理
        validateMaterialInventoryControl(updateDTO);
        validatePackagingSetting(updateDTO);

        MaterialMasterDataDO materialMasterData = materialMasterDataConvert.toDO(updateDTO);
        // 如果不开启库存管理，则清除库存数量字段
//        processInventoryController(materialMasterData);
        materialMasterData.setId(id);
        materialMasterDataMapper.updateById(materialMasterData);
    }

    // 辅助方法：清除所有库存数量字段
    private void closeInventoryControllerAndClearInventoryQuantities(MaterialMasterDataDO materialMasterData) {
        materialMasterData.setMinimumInventoryControl(false);
        materialMasterData.setMaximumInventoryControl(false);
        materialMasterData.setSafetyInventoryControl(false);

        materialMasterData.setMinimumInventoryQuantity(null);
        materialMasterData.setMaximumInventoryQuantity(null);
        materialMasterData.setSafetyInventoryQuantity(null);
    }

    //    inventory_control
    private void validateMaterialInventoryControl(BaseMaterialMasterDataDTO createDTO) {
        if (!createDTO.getInventoryControl()) {
            return;
        }

        validateAtLeastOneControlSelected(createDTO);
        validateInventoryQuantities(createDTO);
        validateInventoryRelationships(createDTO);
    }

    private void validateAtLeastOneControlSelected(BaseMaterialMasterDataDTO dto) {
        if (!dto.getMaximumInventoryControl()
                && !dto.getSafetyInventoryControl()
                && !dto.getMinimumInventoryControl()) {
            throw exception(MATERIAL_INVENTORY_CONTROL_MASTER_SELECT);
        }
    }

    private void validateInventoryQuantities(BaseMaterialMasterDataDTO dto) {
        validateInventoryQuantity(dto.getMaximumInventoryControl(), dto.getMaximumInventoryQuantity());
        validateInventoryQuantity(dto.getSafetyInventoryControl(), dto.getSafetyInventoryQuantity());
        validateInventoryQuantity(dto.getMinimumInventoryControl(), dto.getMinimumInventoryQuantity());
    }

    private void validateInventoryQuantity(boolean controlEnabled, BigDecimal quantity) {
        if (controlEnabled && (quantity == null || quantity.compareTo(BigDecimal.ZERO) <= 0)) {
            throw exception(MATERIAL_INVENTORY_CONTROL_MASTER_VALUE);
        }
    }

    private void validateInventoryRelationships(BaseMaterialMasterDataDTO dto) {
        BigDecimal minQty = dto.getMinimumInventoryQuantity();
        BigDecimal safetyQty = dto.getSafetyInventoryQuantity();
        BigDecimal maxQty = dto.getMaximumInventoryQuantity();

        if (dto.getMinimumInventoryControl()) {
            validateMinVsSafety(minQty, safetyQty, dto.getSafetyInventoryControl());
            validateMinVsMax(minQty, maxQty, dto.getMaximumInventoryControl());
        }
        if (dto.getSafetyInventoryControl()) {
            validateSafetyVsMin(safetyQty, minQty, dto.getMinimumInventoryControl());
            validateSafetyVsMax(safetyQty, maxQty, dto.getMaximumInventoryControl());
        }
        if (dto.getMaximumInventoryControl()) {
            validateMaxVsMin(maxQty, minQty, dto.getMinimumInventoryControl());
            validateMaxVsSafety(maxQty, safetyQty, dto.getSafetyInventoryControl());
        }
    }

    // 辅助方法：校验最小库存 <= 安全库存
    private void validateMinVsSafety(BigDecimal minQty, BigDecimal safetyQty, boolean isSafetyEnabled) {
        if (isSafetyEnabled && minQty.compareTo(safetyQty) > 0) {
            throw exception(MATERIAL_INVENTORY_MINIMUM_MASTER_SMALLER_THAN_SAFETY);
        }
    }

    // 辅助方法：校验最小库存 <= 最大库存
    private void validateMinVsMax(BigDecimal minQty, BigDecimal maxQty, boolean isMaxEnabled) {
        if (isMaxEnabled && minQty.compareTo(maxQty) > 0) {
            throw exception(MATERIAL_INVENTORY_MINIMUM_MASTER_SMALLER_THAN_MAXIMUM);
        }
    }

    // 辅助方法：校验安全库存 >= 最小库存
    private void validateSafetyVsMin(BigDecimal safetyQty, BigDecimal minQty, boolean isMinEnabled) {
        if (isMinEnabled && safetyQty.compareTo(minQty) < 0) {
            throw exception(MATERIAL_INVENTORY_SAFETY_MASTER_BIGGER_THAN_MINIMUM);
        }
    }

    // 辅助方法：校验安全库存 <= 最大库存
    private void validateSafetyVsMax(BigDecimal safetyQty, BigDecimal maxQty, boolean isMaxEnabled) {
        if (isMaxEnabled && safetyQty.compareTo(maxQty) > 0) {
            throw exception(MATERIAL_INVENTORY_SAFETY_MASTER_SMALLER_THAN_MAXIMUM);
        }
    }

    // 辅助方法：校验最大库存 >= 最小库存
    private void validateMaxVsMin(BigDecimal maxQty, BigDecimal minQty, boolean isMinEnabled) {
        if (isMinEnabled && maxQty.compareTo(minQty) < 0) {
            throw exception(MATERIAL_INVENTORY_MAXIMUM_MASTER_BIGGER_THAN_MINIMUM);
        }
    }

    // 辅助方法：校验最大库存 >= 安全库存
    private void validateMaxVsSafety(BigDecimal maxQty, BigDecimal safetyQty, boolean isSafetyEnabled) {
        if (isSafetyEnabled && maxQty.compareTo(safetyQty) < 0) {
            throw exception(MATERIAL_INVENTORY_MAXIMUM_MASTER_BIGGER_THAN_SAFETY);
        }
    }


    private void validateMaterialCategoryExistsAndEnabled(Long categoryId) {
        MaterialCategoryVO materialCategory = materialCategoryService.getMaterialCategory(categoryId);
        if (null == materialCategory) {
            throw exception(MATERIAL_CATEGORY_NOT_EXISTS);
        }
        if (!CommonStatusEnum.ENABLE.getStatus().equals(materialCategory.getStatus())) {
            throw exception(MATERIAL_CATEGORY_STATUS_DISABLE);
        }
    }


    @Override
    public void deleteMaterialMasterData(Long id) {
        // 校验存在
        validateMaterialMasterDataExists(id);
        // 校验是否被引用
        for (MaterialMasterDataRefer materialMasterDataRefer : materialMasterDataRefers) {
            materialMasterDataRefer.validateWhenMaterialDelete(id);
        }
        // 删除
        materialMasterDataMapper.deleteById(id);
    }

    private MaterialMasterDataDO validateMaterialMasterDataExists(Long id) {
        MaterialMasterDataDO materialMasterDataDO = materialMasterDataMapper.selectById(id);
        if (Objects.isNull(materialMasterDataDO)) {
            throw exception(MATERIAL_MASTER_DATA_NOT_EXISTS);
        }
        return materialMasterDataDO;
    }

    @Override
    public MaterialMasterDataVO getMaterialMasterData(Long id) {
        return materialMasterDataConvert.toVO(materialMasterDataMapper.selectById(id));
    }

    @Override
    public PageResult<MaterialMasterDataVO> getMaterialMasterDataPage(SearchMaterialMasterDataDTO pageSearchDTO) {

        if (Objects.nonNull(pageSearchDTO.getCategoryId())) {
            fillCategoryIds(pageSearchDTO);
        }
        PageResult<MaterialMasterDataDO> materialMasterDataDOPageResult = materialMasterDataMapper.selectPage(pageSearchDTO);
        if (materialMasterDataDOPageResult == null || materialMasterDataDOPageResult.getList() == null) {
            return PageResult.empty();
        }

        List<MaterialMasterDataDO> list = materialMasterDataDOPageResult.getList();
        List<MaterialMasterDataVO> materialMasterVolist = materialMasterDataConvert.toVOList(list);

        fillCategoryName(materialMasterVolist, list);
        return new PageResult<>(materialMasterVolist, materialMasterDataDOPageResult.getTotal());
    }

    private void fillCategoryIds(SearchMaterialMasterDataDTO pageSearchDTO) {
        Set<Long> allChildrenIds = new HashSet<>();
        if (Objects.nonNull(pageSearchDTO.getCategoryId())) {
            //根据分类id查询，查询分类的所有子分类，递归查询所有子分类
            allChildrenIds = materialCategoryService.getAllChildrenIds(pageSearchDTO.getCategoryId());
        }
        //如果有categoryIds，把categoryIds添加到allChildrenIds中
        if (Objects.nonNull(pageSearchDTO.getCategoryIds())) {
            allChildrenIds.addAll(pageSearchDTO.getCategoryIds());
        }
        //如果allChildrenIds不为空，设置categoryIds
        if (ArrayUtil.isNotEmpty(allChildrenIds)) {
            pageSearchDTO.setCategoryIds(allChildrenIds);
        }
    }

    private void fillCategoryName(List<MaterialMasterDataVO> materialMasterVolist, List<MaterialMasterDataDO> list) {
        //如果list不为空，获取其中每一个的categoryId，组成集合。根据categoriesId查询categoryName
        if (ArrayUtil.isNotEmpty(materialMasterVolist)) {
            List<Long> categoryIds = list.stream().map(MaterialMasterDataDO::getCategoryId).collect(Collectors.toList());
            Map<Long, String> categoryNameMap = materialCategoryService.getMaterialCategoryNameMap(categoryIds);
            materialMasterVolist.forEach(materialMasterDataVO -> materialMasterDataVO.setCategoryName(categoryNameMap.get(materialMasterDataVO.getCategoryId())));
        }
    }

    @Override
    public void disableMaterialMasterData(Long id) {
        MaterialMasterDataDO materialMasterDataDO = new MaterialMasterDataDO();
        materialMasterDataDO.setStatus(CommonStatusEnum.DISABLE.getStatus());
        materialMasterDataDO.setId(id);

        materialMasterDataMapper.updateById(materialMasterDataDO);
    }

    @Override
    public void enableMaterialMasterData(Long id) {
        MaterialMasterDataDO materialMasterDataDO = new MaterialMasterDataDO();
        materialMasterDataDO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        materialMasterDataDO.setId(id);

        materialMasterDataMapper.updateById(materialMasterDataDO);
    }

    @Override
    public Long countByCategoryId(Long id) {
        return materialMasterDataMapper.countByCategoryId(id);
    }

    @Override
    public List<MaterialMasterDataDO> listIdByIds(List<Long> materialIds) {
        if (CollUtil.isEmpty(materialIds)) {
            return Collections.emptyList();
        }
        return materialMasterDataMapper.selectListByIds(materialIds);
    }

    @Override
    public PageResult<SimpleMaterialMasterDataVO> pageSimpleMaterialMasterData(SearchMaterialMasterDataDTO pageDTO) {
        if (Objects.nonNull(pageDTO.getCategoryId())) {
            fillCategoryIds(pageDTO);
        }

        PageResult<MaterialMasterDataDO> materialMasterDataDOPageResult = materialMasterDataMapper.selectPage(pageDTO);
        if (materialMasterDataDOPageResult == null || materialMasterDataDOPageResult.getList() == null) {
            return PageResult.empty();
        }

        List<MaterialMasterDataDO> list = materialMasterDataDOPageResult.getList();
        List<SimpleMaterialMasterDataVO> materialMasterVolist = materialMasterDataConvert.toSimpleVO(list);

        return new PageResult<>(materialMasterVolist, materialMasterDataDOPageResult.getTotal());
    }

    @Override
    public void validateEnableAndIsPackagedProduct(List<Long> materialIds) {
        List<MaterialMasterDataDO> materialMasterDataDOS = materialMasterDataMapper.selectListByIds(materialIds);
        Set<Long> existIdSet = materialMasterDataDOS.stream()
                .map(MaterialMasterDataDO::getId)
                .collect(Collectors.toSet());
        List<Long> notExistMaterialIds = materialIds.stream()
                .filter(id -> !existIdSet.contains(id))
                .toList();
        if (CollUtil.isNotEmpty(notExistMaterialIds)) {
            throw exception(MATERIAL_MASTER_DATA_LIST_NOT_EXISTS, notExistMaterialIds);
        }

        // 收集不可用的物料Id集合：未启用或不是包装产品的ids，是否包装产品通过packagedProduct判断
        List<String> invalidMaterialNames = materialMasterDataDOS.stream()
                .filter(materialMasterDataDO -> !CommonStatusEnum.ENABLE.getStatus().equals(materialMasterDataDO.getStatus())
                        || !materialMasterDataDO.getPackagedProduct())
                .map(MaterialMasterDataDO::getName)
                .toList();
        if (CollUtil.isNotEmpty(invalidMaterialNames)) {
            throw exception(MATERIAL_MASTER_DATA_LIST_NOT_ENABLE_OR_NOT_PACKAGED_PRODUCT, invalidMaterialNames);
        }
    }

    @Override
    public List<SimpleMaterialMasterDataVO> simpleList(SearchSimpleMaterialDTO searchDTO) {
        List<MaterialMasterDataDO> list = materialMasterDataMapper.simpleList(searchDTO);
        return materialMasterDataConvert.toSimpleVO(list);
    }

    @Override
    public void validateExistAndEnableByIds(List<Long> materialIds) {
        if (CollUtil.isEmpty(materialIds)) {
            return;
        }
        // 过滤掉 null 值，防止后续 NPE
        List<Long> filteredMaterialIds = materialIds.stream()
                .filter(Objects::nonNull)
                .toList();
        if (filteredMaterialIds.isEmpty()) {
            return;
        }

        List<MaterialMasterDataDO> existList = materialMasterDataMapper.selectListByIds(materialIds);
        if (CollUtil.isEmpty(existList)) {
            throw exception(MATERIAL_MASTER_DATA_NOT_EXISTS);
        }
        Set<Long> existIdSet = existList.stream()
                .map(MaterialMasterDataDO::getId)
                .collect(Collectors.toSet());
        List<Long> notExistMaterialIds = filteredMaterialIds.stream()
                .filter(id -> !existIdSet.contains(id))
                .toList();

        if (CollUtil.isNotEmpty(notExistMaterialIds)) {
            throw exception(MATERIAL_MASTER_DATA_LIST_NOT_EXISTS, notExistMaterialIds);
        }

        List<String> disableMasterNames = new ArrayList<>();
        for (MaterialMasterDataDO material : existList) {
            if (CommonStatusEnum.DISABLE.getStatus().equals(material.getStatus())) {
                disableMasterNames.add(material.getName());
            }
        }
        if (CollUtil.isNotEmpty(disableMasterNames)) {
            throw exception(MATERIAL_MASTER_DATA_LIST_NOT_ENABLE, disableMasterNames);
        }
    }

    @Override
    public void validateExist(List<Long> materialIds) {
        if (CollUtil.isEmpty(materialIds)) {
            return;
        }
        // 过滤掉 null 值，防止后续 NPE
        List<Long> filteredMaterialIds = materialIds.stream()
                .filter(Objects::nonNull)
                .toList();
        if (filteredMaterialIds.isEmpty()) {
            return;
        }

        List<MaterialMasterDataDO> existList = materialMasterDataMapper.selectListByIds(materialIds);
        if (CollUtil.isEmpty(existList)) {
            throw exception(MATERIAL_MASTER_DATA_NOT_EXISTS);
        }
        Set<Long> existIdSet = existList.stream()
                .map(MaterialMasterDataDO::getId)
                .collect(Collectors.toSet());
        List<Long> notExistMaterialIds = filteredMaterialIds.stream()
                .filter(id -> !existIdSet.contains(id))
                .toList();

        if (CollUtil.isNotEmpty(notExistMaterialIds)) {
            throw exception(MATERIAL_MASTER_DATA_LIST_NOT_EXISTS, notExistMaterialIds);
        }
    }

    @Override
    public Boolean isUniqueCodeManaged(Long materialId) {
        MaterialMasterDataDO materialMasterDataDO = validateMaterialMasterDataExists(materialId);
        return materialMasterDataDO.getPackaging() && materialMasterDataDO.getUniqueCodeManaged();
    }
}