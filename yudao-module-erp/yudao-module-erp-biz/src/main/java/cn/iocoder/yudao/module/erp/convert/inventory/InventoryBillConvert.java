package cn.iocoder.yudao.module.erp.convert.inventory;


import cn.iocoder.yudao.module.erp.constant.BusinessTypeEnum;
import cn.iocoder.yudao.module.erp.constant.DocumentTypeEnum;
import cn.iocoder.yudao.module.erp.constant.StockOperationTypeEnum;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.*;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryBillDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.view.ItemWithInventoryBill;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring")
public interface InventoryBillConvert {


    @AfterMapping
    default void setDefaultValues(@MappingTarget InventoryBillDO billDO, CreatePurchaseReturnedOutboundDTO createDTO) {
        billDO.setDirection(StockOperationTypeEnum.OUT.getType());
        billDO.setDocumentType(DocumentTypeEnum.OUTBOUND.getValue());
        if (createDTO.getOutboundDate() != null) {
            billDO.setDocumentDate(createDTO.getOutboundDate().toLocalDate());
        }
    }


    //
    InventoryBillDO fromPurchaseReturnedOutboundDO(CreatePurchaseReturnedOutboundDTO createDTO);

    PurchaseReturnedOutboundVO toPurchaseReturnedOutboundVO(InventoryBillDO billDO);


    SearchInventoryBillDTO fromPurchaseReturnedOutboundSearchDTO(SearchPurchaseReturnedOutboundDTO searchDTO);

    @AfterMapping
    default void setDefaultValues(@MappingTarget SearchInventoryBillDTO billSearchDTO, SearchPurchaseReturnedOutboundDTO returnedOutSearchDTO) {
        billSearchDTO.setDocumentType(DocumentTypeEnum.OUTBOUND.getValue());
        billSearchDTO.setBusinessType(BusinessTypeEnum.PURCHASE_RETURNED_OUTBOUND.getType());
    }

    SimplePurchaseReturnedOutboundVO fromInventoryBillDO(InventoryBillDO billDO);

    List<SimplePurchaseReturnedOutboundVO> fromInventoryBillDO(List<InventoryBillDO> list);

    @AfterMapping
    default void setDefaultValues(@MappingTarget SearchInventoryItemPageDTO itemSearchDTO, SearchItemPageDTO searchDTO) {
        itemSearchDTO.setDocumentType(DocumentTypeEnum.OUTBOUND.getValue());
        itemSearchDTO.setBusinessType(BusinessTypeEnum.PURCHASE_RETURNED_OUTBOUND.getType());
    }

    SearchInventoryItemPageDTO fromSpecItemSearch(SearchItemPageDTO searchDTO);

    List<ItemWithPurchaseReturnedOutboundVO> fromItemWithInventoryBill(List<ItemWithInventoryBill> list);

    ItemWithPurchaseReturnedOutboundVO fromItemWithInventoryBill(ItemWithInventoryBill itemWithInventoryBill);
}