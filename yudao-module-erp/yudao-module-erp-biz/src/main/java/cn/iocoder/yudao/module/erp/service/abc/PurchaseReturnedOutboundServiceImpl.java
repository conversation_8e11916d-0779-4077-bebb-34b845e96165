package cn.iocoder.yudao.module.erp.service.abc;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.erp.convert.abc.*;
import cn.iocoder.yudao.module.erp.controller.admin.abc.vo.*;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.PurchaseReturnedOutboundDO;
import cn.iocoder.yudao.module.erp.service.common.AbstractDocumentAuditingService;
import cn.iocoder.yudao.module.erp.constant.*;
import cn.iocoder.yudao.module.erp.convert.abc.PurchaseReturnedOutboundItemConvert;
import cn.iocoder.yudao.module.erp.service.common.DocumentServiceUtil;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.view.ItemWithPurchaseReturnedOutbound;
import jakarta.annotation.PostConstruct;
import cn.hutool.core.collection.CollUtil;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.Set;
import java.util.Objects;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.PurchaseReturnedOutboundItemDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.erp.dal.mysql.abc.PurchaseReturnedOutboundMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.abc.PurchaseReturnedOutboundItemMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.*;

/**
 * 采购退货出库单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseReturnedOutboundServiceImpl extends AbstractDocumentAuditingService<PurchaseReturnedOutboundItemDO>
        implements PurchaseReturnedOutboundService {

    @Resource
    private PurchaseReturnedOutboundMapper purchaseReturnedOutboundMapper;

    @Resource
    private PurchaseReturnedOutboundItemMapper purchaseReturnedOutboundItemMapper;

    @Resource
    private PurchaseReturnedOutboundItemConvert purchaseReturnedOutboundItemConvert;

    @Resource
    private DocumentServiceUtil documentServiceUtil;
    @Resource
    private PurchaseReturnedOutboundConvert purchaseReturnedOutboundConvert;
    @Resource
    private PurchaseReturnedOutboundItemMapper purchaseReturnedOutboundItemMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(CreatePurchaseReturnedOutboundDTO createDTO) {
        // 1. 转换DO
        PurchaseReturnedOutboundDO purchaseReturnedOutbound = purchaseReturnedOutboundConvert.toDO(createDTO);
        List<PurchaseReturnedOutboundItemDO> items = purchaseReturnedOutboundItemConvert.toDO(createDTO.getItems());

        // 2. 验证业务逻辑
        commonValidate(purchaseReturnedOutbound, items);

        // 3. 插入主表
        documentServiceUtil.fillDocumentInfoWhenCreate(purchaseReturnedOutbound, BusinessTypeEnum.PURCHASERETURNEDOUTBOUND);
        fillDeptName(purchaseReturnedOutbound);
        purchaseReturnedOutboundMapper.insert(purchaseReturnedOutbound);

        // 4. 插入明细表
        doCreatePurchaseReturnedOutboundItems(purchaseReturnedOutbound.getId(), items);

        return purchaseReturnedOutbound.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Long id, UpdatePurchaseReturnedOutboundDTO updateDTO) {
        // 1. 验证存在
        PurchaseReturnedOutboundDO purchaseReturnedOutbound = validatePurchaseReturnedOutboundExists(id);
        // 2. 验证可编辑
        validatePurchaseReturnedOutboundEditable(purchaseReturnedOutbound);

        // 3. 转换DO
        PurchaseReturnedOutboundDO updateObj = purchaseReturnedOutboundConvert.toDO(updateDTO);
        List<PurchaseReturnedOutboundItemDO> updateItems = purchaseReturnedOutboundItemConvert.toDO(updateDTO.getItems());

        // 4. 验证业务逻辑
        commonValidate(updateObj, updateItems);

        // 5. 更新主表
        updateObj.setId(id);
        fillDeptName(updateObj);
        purchaseReturnedOutboundMapper.updateById(updateObj);

        // 6. 处理明细表 - 分为新增、更新、删除三类
        Map<Boolean, List<PurchaseReturnedOutboundItemDO>> partitionedItems = updateItems.stream()
                .collect(Collectors.partitioningBy(item -> item.getId() == null));

        // 获取现有明细项
        List<PurchaseReturnedOutboundItemDO> existItemList = purchaseReturnedOutboundItemMapper.selectListByPurchaseReturnedOutboundId(id);

        // 处理新增项
        List<PurchaseReturnedOutboundItemDO> newItemList = partitionedItems.get(true);
        if (CollUtil.isNotEmpty(newItemList)) {
            doCreatePurchaseReturnedOutboundItems(id, newItemList);
        }

        // 处理更新项
        List<PurchaseReturnedOutboundItemDO> updateItemList = partitionedItems.get(false);
        if (CollUtil.isNotEmpty(updateItemList)) {
            doUpdatePurchaseReturnedOutboundItemList(id, updateItemList);
        }

        // 处理删除项：找出存在于旧列表但不在更新列表中的项
        Set<Long> updateItemIds = updateItemList.stream()
                .map(PurchaseReturnedOutboundItemDO::getId)
                .collect(Collectors.toSet());
        Set<Long> deleteItemIds = existItemList.stream()
                .map(PurchaseReturnedOutboundItemDO::getId)
                .filter(itemId -> !updateItemIds.contains(itemId))
                .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(deleteItemIds)) {
            purchaseReturnedOutboundItemMapper.deleteByIds(deleteItemIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        // 验证存在
        PurchaseReturnedOutboundDO purchaseReturnedOutbound = validatePurchaseReturnedOutboundExists(id);
        // 验证可删除
        validatePurchaseReturnedOutboundEditable(purchaseReturnedOutbound);

        // 删除主表
        purchaseReturnedOutboundMapper.deleteById(id);
        // 删除明细表
        purchaseReturnedOutboundItemMapper.deleteByPurchaseReturnedOutboundId(id);
    }

    private PurchaseReturnedOutboundDO validatePurchaseReturnedOutboundExists(Long id) {
        PurchaseReturnedOutboundDO purchaseReturnedOutbound = purchaseReturnedOutboundMapper.selectById(id);
        if (purchaseReturnedOutbound == null) {
            throw exception(PURCHASERETURNEDOUTBOUND_NOT_EXISTS);
        }
        return purchaseReturnedOutbound;
    }

    @Override
    public PurchaseReturnedOutboundVO get(Long id) {
        PurchaseReturnedOutboundDO purchaseReturnedOutbound = validatePurchaseReturnedOutboundExists(id);
        List<PurchaseReturnedOutboundItemDO> items = purchaseReturnedOutboundItemMapper.selectListByPurchaseReturnedOutboundId(id);

        PurchaseReturnedOutboundVO result = purchaseReturnedOutboundConvert.toVO(purchaseReturnedOutbound);
        result.setItems(purchaseReturnedOutboundItemConvert.toVO(items));
        return result;
    }

    @Override
    public PageResult<SimplePurchaseReturnedOutboundVO> page(SearchPurchaseReturnedOutboundDTO searchDTO) {
        PageResult<PurchaseReturnedOutboundDO> page =  purchaseReturnedOutboundMapper.selectPage(searchDTO);
        List<PurchaseReturnedOutboundDO> list = page.getList();
        List<SimplePurchaseReturnedOutboundVO> listVO = purchaseReturnedOutboundConvert.toSimpleVO(list);
        return new PageResult<>(listVO, page.getTotal());
    }

    // ==================== 子表（采购退货出库单明细） ====================

    @Override
    public PageResult<PurchaseReturnedOutboundItemDO> getPurchaseReturnedOutboundItemPage(PageParam pageReqVO, Long returnedOutboundId) {
        return purchaseReturnedOutboundItemMapper.selectPage(pageReqVO, returnedOutboundId);
    }

    @Override
    public Long createPurchaseReturnedOutboundItem(PurchaseReturnedOutboundItemDO purchaseReturnedOutboundItem) {
        purchaseReturnedOutboundItemMapper.insert(purchaseReturnedOutboundItem);
        return purchaseReturnedOutboundItem.getId();
    }

    @Override
    public void updatePurchaseReturnedOutboundItem(PurchaseReturnedOutboundItemDO purchaseReturnedOutboundItem) {
        // 校验存在
        PurchaseReturnedOutboundItemDO existingPurchaseReturnedOutboundItem = validatePurchaseReturnedOutboundItemExists(purchaseReturnedOutboundItem.getId());
        // 更新
        purchaseReturnedOutboundItem.setUpdater(null).setUpdateTime(null); // 解决更新情况下：updateTime 不更新
        purchaseReturnedOutboundItemMapper.updateById(purchaseReturnedOutboundItem);
    }

    @Override
    public void deletePurchaseReturnedOutboundItem(Long id) {
        // 校验存在
        PurchaseReturnedOutboundItemDO purchaseReturnedOutboundItem = validatePurchaseReturnedOutboundItemExists(id);
        // 删除
        purchaseReturnedOutboundItemMapper.deleteById(id);
    }

    @Override
    public PurchaseReturnedOutboundItemDO getPurchaseReturnedOutboundItem(Long id) {
        return purchaseReturnedOutboundItemMapper.selectById(id);
    }

    private PurchaseReturnedOutboundItemDO validatePurchaseReturnedOutboundItemExists(Long id) {
        PurchaseReturnedOutboundItemDO purchaseReturnedOutboundItem = purchaseReturnedOutboundItemMapper.selectById(id);
        if (purchaseReturnedOutboundItem == null) {
            throw exception(PURCHASERETURNEDOUTBOUNDITEM_NOT_EXISTS);
        }
        return purchaseReturnedOutboundItem;
    }

    private void deletePurchaseReturnedOutboundItemByReturnedOutboundId(Long returnedOutboundId) {
        purchaseReturnedOutboundItemMapper.deleteByReturnedOutboundId(returnedOutboundId);
    }


    // ==================== 文档操作方法实现 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void close(Long id) {
        PurchaseReturnedOutboundDO purchaseReturnedOutbound = validatePurchaseReturnedOutboundExists(id);
        validateBusinessStatus(purchaseReturnedOutbound, BusinessStatusEnum.NORMAL.getStatus());
        purchaseReturnedOutboundMapper.updateBusinessStatusById(BusinessStatusEnum.CLOSED.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelClose(Long id) {
        PurchaseReturnedOutboundDO purchaseReturnedOutbound = validatePurchaseReturnedOutboundExists(id);
        validateBusinessStatus(purchaseReturnedOutbound, BusinessStatusEnum.CLOSED.getStatus());
        purchaseReturnedOutboundMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspend(Long id) {
        PurchaseReturnedOutboundDO purchaseReturnedOutbound = validatePurchaseReturnedOutboundExists(id);
        validateBusinessStatus(purchaseReturnedOutbound, BusinessStatusEnum.NORMAL.getStatus());
        purchaseReturnedOutboundMapper.updateBusinessStatusById(BusinessStatusEnum.SUSPENDED.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSuspend(Long id) {
        PurchaseReturnedOutboundDO purchaseReturnedOutbound = validatePurchaseReturnedOutboundExists(id);
        validateBusinessStatus(purchaseReturnedOutbound, BusinessStatusEnum.SUSPENDED.getStatus());
        purchaseReturnedOutboundMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    @Override
    public void approve(Long id) {
        doApprove(id);
    }

    @Override
    public void cancelApprove(Long id) {
        doCancelApprove(id);
    }

    @Override
    protected void doApprove(Long id) {
        // 校验存在
        PurchaseReturnedOutboundDO purchaseReturnedOutbound = validatePurchaseReturnedOutboundExists(id);
        // 校验状态
        if (!Objects.equals(purchaseReturnedOutbound.getDocumentStatus(), DocumentStatusEnum.DRAFT.getStatus())) {
            throw exception(PURCHASERETURNEDOUTBOUND_APPROVE_FAIL_STATUS_NOT_DRAFT);
        }

        // 更新状态
        purchaseReturnedOutboundMapper.updateDocumentStatusById(id, DocumentStatusEnum.APPROVED.getStatus());

        // 生成库存明细
        List<PurchaseReturnedOutboundItemDO> items = purchaseReturnedOutboundItemMapper.selectListByPurchaseReturnedOutboundId(id);
        generateInventoryDetails(purchaseReturnedOutbound, items);
    }

    @Override
    protected void doCancelApprove(Long id) {
        // 校验存在
        PurchaseReturnedOutboundDO purchaseReturnedOutbound = validatePurchaseReturnedOutboundExists(id);
        // 校验状态
        if (!Objects.equals(purchaseReturnedOutbound.getDocumentStatus(), DocumentStatusEnum.APPROVED.getStatus())) {
            throw exception(PURCHASERETURNEDOUTBOUND_CANCEL_APPROVE_FAIL_STATUS_NOT_APPROVED);
        }

        // 更新状态
        purchaseReturnedOutboundMapper.updateDocumentStatusById(id, DocumentStatusEnum.DRAFT.getStatus());

        // 删除库存明细
        deleteInventoryDetails(purchaseReturnedOutbound);
    }

    private void generateInventoryDetails(PurchaseReturnedOutboundDO purchaseReturnedOutbound, List<PurchaseReturnedOutboundItemDO> items) {
        // TODO: 实现库存明细生成逻辑
        // 参考 PurchaseArrivalServiceImpl.generateInventoryDetails 方法
    }

    private void deleteInventoryDetails(PurchaseReturnedOutboundDO purchaseReturnedOutbound) {
        // TODO: 实现库存明细删除逻辑
        // 参考 PurchaseArrivalServiceImpl.deleteInventoryDetails 方法
    }

    @Override
    public PageResult<ItemWithPurchaseReturnedOutboundVO> pageItem(SearchItemPageDTO searchDTO) {
        PageResult<ItemWithPurchaseReturnedOutbound> pageResult = purchaseReturnedOutboundItemMapper.selectItemPage(searchDTO);
        return purchaseReturnedOutboundItemConvert.convertPage(pageResult);
    }

    @PostConstruct
    public void init() {
        // TODO: 初始化逻辑，如设置单据编号前缀等
        // 参考 PurchaseArrivalServiceImpl.init() 方法
    }



    private void validatePurchaseReturnedOutboundEditable(PurchaseReturnedOutboundDO purchaseReturnedOutbound) {
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(purchaseReturnedOutbound.getDocumentStatus()) &&
                !DocumentStatusEnum.ROLLBACK.getStatus().equals(purchaseReturnedOutbound.getDocumentStatus())) {
            throw exception(PURCHASERETURNEDOUTBOUND_NOT_EDITABLE);
        }
    }

    private void validatePurchaseReturnedOutboundApprovable(PurchaseReturnedOutboundDO purchaseReturnedOutbound) {
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(purchaseReturnedOutbound.getDocumentStatus()) &&
                !DocumentStatusEnum.ROLLBACK.getStatus().equals(purchaseReturnedOutbound.getDocumentStatus())) {
            throw exception(PURCHASERETURNEDOUTBOUND_NOT_APPROVABLE);
        }
    }

    private void validatePurchaseReturnedOutboundCancelApprovable(PurchaseReturnedOutboundDO purchaseReturnedOutbound) {
        if (!DocumentStatusEnum.APPROVE.getStatus().equals(purchaseReturnedOutbound.getDocumentStatus())) {
            throw exception(PURCHASERETURNEDOUTBOUND_NOT_CANCEL_APPROVABLE);
        }
    }

    private void validateBusinessStatus(PurchaseReturnedOutboundDO purchaseReturnedOutbound, Integer status) {
        if (!Objects.equals(purchaseReturnedOutbound.getBusinessStatus(), status)) {
            throw exception(PURCHASERETURNEDOUTBOUND_BUSINESS_STATUS_NOT_SUPPORT_CHANGE, purchaseReturnedOutbound.getCode());
        }
    }

    private void commonValidate(PurchaseReturnedOutboundDO purchaseReturnedOutbound, List<PurchaseReturnedOutboundItemDO> items) {
        // TODO: 实现业务逻辑验证
        // 参考 PurchaseArrivalServiceImpl.commonValidate() 方法
        // 1. 验证业务员和部门的隶属关系
        // 2. 验证仓库
        // 3. 验证数量
        // 4. 验证唯一码等
    }

    private void fillDeptName(PurchaseReturnedOutboundDO purchaseReturnedOutbound) {
        // TODO: 实现部门名称填充逻辑
        // 参考 PurchaseArrivalServiceImpl.fillDeptName() 方法
    }

    private void doCreatePurchaseReturnedOutboundItems(Long purchaseReturnedOutboundId, List<PurchaseReturnedOutboundItemDO> items) {
        // TODO: 实现明细创建逻辑
        // 参考 PurchaseArrivalServiceImpl.createPurchaseArrivalItems() 方法
        items.forEach(item -> {
            item.setPurchaseReturnedOutboundId(purchaseReturnedOutboundId);
        });
        purchaseReturnedOutboundItemMapper.insertBatch(items);
    }

    private void doUpdatePurchaseReturnedOutboundItemList(Long purchaseReturnedOutboundId, List<PurchaseReturnedOutboundItemDO> items) {
        // TODO: 实现明细更新逻辑
        // 参考 PurchaseArrivalServiceImpl.updatePurchaseArrivalItemList() 方法
        items.forEach(item -> {
            item.setPurchaseReturnedOutboundId(purchaseReturnedOutboundId);
        });
        purchaseReturnedOutboundItemMapper.updateBatch(items);
    }

}