package cn.iocoder.yudao.module.erp.dal.dataobject.purchase.arrival;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.DocumentDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 采购到货单 DO
 *
 * <AUTHOR>
 */
@TableName("erp_purchase_arrival")
@KeySequence("erp_purchase_arrival_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseArrivalDO extends DocumentDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 到货日期
     */
    private LocalDate arrivalDate;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 物料类型
     */
    private String materialType;

    /**
     * 业务部门ID
     */
    private Long businessDepartmentId;

    /**
     * 业务部门名称
     */
    private String businessDepartmentName;

    /**
     * 业务员ID
     */
    private Long businessUserId;

    /**
     * 业务员姓名
     */
    private String businessUserName;

    /**
     * 入库仓库ID
     */
    private Long warehouseId;

    /**
     * 入库仓库名称
     */
    private String warehouseName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 未入库库数量：当前单据下所有Item未入库数量的汇总
     */
    private BigDecimal unconfirmedInboundQuantity;

}
