package cn.iocoder.yudao.module.erp.service.outbound.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.BusinessTypeEnum;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.*;
import cn.iocoder.yudao.module.erp.convert.inventory.InventoryBillConvert;
import cn.iocoder.yudao.module.erp.convert.inventory.InventoryBillItemConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryBillDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryBillItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.view.ItemWithInventoryBill;
import cn.iocoder.yudao.module.erp.service.inventory.common.AbstractInventoryService;
import cn.iocoder.yudao.module.erp.service.supplier.SupplierService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 采购退货出库单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PurchaseReturnedOutboundServiceImpl extends AbstractInventoryService<CreatePurchaseReturnedOutboundDTO, PurchaseReturnedOutboundVO>
        implements PurchaseReturnedOutboundService {


    @Resource
    private InventoryBillItemConvert itemConvert;

    @Resource
    private InventoryBillConvert billConvert;
    
    @Resource
    private InventoryBillItemConvert inventoryBillItemConvert;

    @Resource
    private SupplierService supplierService;

    @Override
    protected InventoryBillDO getBillDO(CreatePurchaseReturnedOutboundDTO createDTO) {
        return billConvert.fromPurchaseReturnedOutboundDO(createDTO);
    }

    @Override
    protected List<InventoryBillItemDO> getBillItemDO(CreatePurchaseReturnedOutboundDTO createDTO) {
        List<PurchaseReturnedOutboundItemDTO> items = createDTO.getItems();
        return itemConvert.fromPurchaseReturnedOutboundItemDO(items);
    }

    @Override
    protected BusinessTypeEnum getBusinessTypeEnum() {
        return BusinessTypeEnum.PURCHASE_RETURNED_OUTBOUND;
    }

    @Override
    protected void commonValidate(InventoryBillDO inventoryBill, List<InventoryBillItemDO> items) {
        super.commonValidate(inventoryBill, items);
    }

    protected void fillName(InventoryBillDO inventoryBill) {
        super.fillName(inventoryBill);
        if (inventoryBill.getSupplierId() != null) {
            inventoryBill.setSupplierName(supplierService.get(inventoryBill.getSupplierId()).getName());
        }
    }

    @Override
    protected PurchaseReturnedOutboundVO buildVO(InventoryBillDO billDO, List<InventoryBillItemDO> items) {
        PurchaseReturnedOutboundVO result = billConvert.toPurchaseReturnedOutboundVO(billDO);
        result.setItems(inventoryBillItemConvert.toPurchaseReturnedOutboundItemVO(items));
        return result;
    }

    @Override
    public PageResult<SimplePurchaseReturnedOutboundVO> page(SearchPurchaseReturnedOutboundDTO searchDTO) {
        PageResult<InventoryBillDO> page = super.page(billConvert.fromPurchaseReturnedOutboundSearchDTO(searchDTO));
        List<InventoryBillDO> list = page.getList();
        List<SimplePurchaseReturnedOutboundVO> listVO = billConvert.fromInventoryBillDO(list);
        return new PageResult<>(listVO, page.getTotal());
    }


    @Override
    public PageResult<ItemWithPurchaseReturnedOutboundVO> pageItem(SearchItemPageDTO searchDTO) {
        PageResult<ItemWithInventoryBill> page = super.pageItem(billConvert.fromSpecItemSearch(searchDTO));
        List<ItemWithInventoryBill> list = page.getList();
        return new PageResult<>(billConvert.fromItemWithInventoryBill(list), page.getTotal());
    }
}