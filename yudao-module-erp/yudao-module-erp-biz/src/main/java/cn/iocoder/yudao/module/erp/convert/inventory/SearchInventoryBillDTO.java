package cn.iocoder.yudao.module.erp.convert.inventory;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 出入库清单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchInventoryBillDTO extends PageParam {

    @Schema(description = "单据号")
    private String code;

    @Schema(description = "in入库 out出库")
    private String direction;

    @Schema(description = "单据类型", example = "1")
    private String documentType;

    @Schema(description = "业务类型", example = "2")
    private String businessType;

    @Schema(description = "0编制中 1审核通过 2回退", example = "2")
    private Integer documentStatus;

    @Schema(description = "业务状态：0关闭 1正常 2挂起", example = "1")
    private Integer businessStatus;

    @Schema(description = "单据日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] documentDate;


    @Schema(description = "仓库ID", example = "23679")
    private Long warehouseId;

    @Schema(description = "仓库名称", example = "张三")
    private String warehouseName;

    @Schema(description = "供应商ID", example = "31190")
    private Long supplierId;

    @Schema(description = "供应商名称", example = "李四")
    private String supplierName;

    @Schema(description = "客户ID", example = "28181")
    private Long customerId;

    @Schema(description = "客户名称", example = "王五")
    private String customerName;

    @Schema(description = "业务部门ID", example = "3240")
    private Long businessDepartmentId;

    @Schema(description = "业务部门名称", example = "张三")
    private String businessDepartmentName;

    @Schema(description = "业务员ID", example = "16529")
    private Long businessUserId;

    @Schema(description = "业务员姓名", example = "芋艿")
    private String businessUserName;

    @Schema(description = "来源单据类型", example = "2")
    private String sourceType;

    @Schema(description = "来源单据ID", example = "27528")
    private Long sourceId;

    @Schema(description = "来源单据号")
    private String sourceCode;

    @Schema(description = "物料类型:采购单的物料类型（业务字典）", example = "1")
    private String materialType;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;


    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;


}