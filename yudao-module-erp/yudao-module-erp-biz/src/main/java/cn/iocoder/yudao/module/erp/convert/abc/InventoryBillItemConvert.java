package cn.iocoder.yudao.module.erp.convert.abc;

import cn.iocoder.yudao.module.erp.controller.admin.abc.vo.*;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.InventoryBillItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.view.ItemWithInventoryBill;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface  InventoryBillItemConvert {

    InventoryBillItemDO toDO(InventoryBillItemDTO inventorybillitemDTO);

    InventoryBillItemVO toVO(InventoryBillItemDO inventorybillitemDO);

    List<InventoryBillItemVO> toVO(List<InventoryBillItemDO> inventorybillitemDOList);

    List<InventoryBillItemDO> toDO(List<InventoryBillItemDTO> inventorybillitemDTOList);

    /**
     * 转换分页结果
     */
    default PageResult<ItemWithInventoryBillVO> convertPage(PageResult<ItemWithInventoryBill> pageResult) {
        return BeanUtils.toBean(pageResult, ItemWithInventoryBillVO.class);
    }
}