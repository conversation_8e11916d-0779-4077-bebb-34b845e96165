package cn.iocoder.yudao.module.erp.dal.dataobject.warehouse;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 仓库基本信息 DO
 *
 * <AUTHOR>
 */
@TableName("erp_warehouse")
@KeySequence("erp_warehouse_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 仓库编号
     */
    private String code;
    /**
     * 仓库名称
     */
    private String name;
    /**
     * 库位管理
     */
    private Boolean storagePositionControl;


    /**
     * 状态（停用/启用）
     */
    private Integer status;

}