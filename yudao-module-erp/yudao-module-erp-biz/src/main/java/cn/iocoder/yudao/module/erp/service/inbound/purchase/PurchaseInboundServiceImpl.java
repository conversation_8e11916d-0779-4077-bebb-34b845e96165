package cn.iocoder.yudao.module.erp.service.inbound.purchase;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.*;
import cn.iocoder.yudao.module.erp.controller.admin.inbound.purchase.vo.*;
import cn.iocoder.yudao.module.erp.convert.inbound.purchase.PurchaseInboundConvert;
import cn.iocoder.yudao.module.erp.convert.inbound.purchase.PurchaseInboundItemConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.PurchaseInboundDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.PurchaseInboundItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.PurchaseInboundQuantity;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.view.PurchaseInboundItemWithInboundDO;
import cn.iocoder.yudao.module.erp.dal.mysql.inbound.purchase.PurchaseInboundItemMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.inbound.purchase.PurchaseInboundMapper;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataRefer;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataService;
import cn.iocoder.yudao.module.erp.service.common.AbstractDocumentAuditingService;
import cn.iocoder.yudao.module.erp.service.common.DocumentRelationHelper;
import cn.iocoder.yudao.module.erp.service.common.DocumentServiceUtil;
import cn.iocoder.yudao.module.erp.service.common.RedissonLockUtil;
import cn.iocoder.yudao.module.erp.service.inventory.InventoryDetailHelper;
import cn.iocoder.yudao.module.erp.service.inventory.InventoryDetailService;
import cn.iocoder.yudao.module.erp.service.purchase.arrival.PurchaseArrivalService;
import cn.iocoder.yudao.module.erp.service.warehouse.WarehouseRefer;
import cn.iocoder.yudao.module.erp.service.warehouse.WarehouseService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.*;

/**
 * 采购入库单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PurchaseInboundServiceImpl extends AbstractDocumentAuditingService<PurchaseInboundItemDO> implements PurchaseInboundService, MaterialMasterDataRefer, WarehouseRefer {

    @Resource
    private PurchaseInboundMapper purchaseInboundMapper;
    @Resource
    private PurchaseInboundConvert purchaseInboundConvert;

    @Resource
    private PurchaseInboundItemConvert itemConvert;
    @Resource
    private PurchaseInboundItemMapper purchaseInboundItemMapper;

    @Resource
    @Lazy
    private DocumentServiceUtil documentServiceUtil;

    @Resource
    private RedissonLockUtil redissonLockUtil;

    @Resource
    @Lazy
    private AdminUserApi adminUserApi;

    @Resource
    @Lazy
    private MaterialMasterDataService materialMasterDataService;

    @Resource
    @Lazy
    private WarehouseService warehouseService;

    @Resource
    @Lazy
    private InventoryDetailService inventoryDetailService;

    @Resource
    @Lazy
    private InventoryDetailHelper inventoryHelper;

    @Resource
    @Lazy
    private PurchaseArrivalService purchaseArrivalService;

    @Resource
    @Lazy
    private MaterialMasterDataService materialService;

    @Resource
    @Lazy
    private DeptApi deptApi;
    @Autowired
    private DocumentRelationHelper documentRelationHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(CreatePurchaseInboundDTO createDTO) {
        // 验证业务逻辑
        PurchaseInboundDO inbound = purchaseInboundConvert.toDO(createDTO);
        List<PurchaseInboundItemDO> items = itemConvert.toDO(createDTO.getItems());

        commonValidate(inbound, items);
        // 插入主表
        documentServiceUtil.fillDocumentInfoWhenCreate(inbound, BusinessTypeEnum.PURCHASE_INBOUND);
        fillDeptName(inbound);
        purchaseInboundMapper.insert(inbound);

        // 插入明细表
        doCreatePurchaseInboundItemList(inbound.getId(), itemConvert.toDO(createDTO.getItems()));

        return inbound.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Long id, UpdatePurchaseInboundDTO updateDTO) {
        // 校验存在
        PurchaseInboundDO purchaseInboundDO = validateInboundExists(id);
        validateCanEdit(purchaseInboundDO);

        PurchaseInboundDO updateObj = purchaseInboundConvert.toDO(updateDTO);
        List<PurchaseInboundItemDO> items = itemConvert.toDO(updateDTO.getItems());
        // 验证业务逻辑
        commonValidate(updateObj, items);

        // 更新主表
        updateObj.setId(id);
        fillDeptName(updateObj);
        purchaseInboundMapper.updateById(updateObj);

        // 更新明细表 - 智能处理增删改
        updatePurchaseInboundItemSmart(id, items);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        // 校验存在
        PurchaseInboundDO purchaseInboundDO = validateInboundExists(id);
        validateCanEdit(purchaseInboundDO);

        // 删除主表
        purchaseInboundMapper.deleteById(id);

        // 删除明细表
        purchaseInboundItemMapper.deleteByInboundId(id);
    }

    @Override
    public PurchaseInboundVO get(Long id) {
        PurchaseInboundDO purchaseInboundDO = validateInboundExists(id);
        PurchaseInboundVO inboundVO = purchaseInboundConvert.toVO(purchaseInboundDO);

        List<PurchaseInboundItemDO> itemDOList = purchaseInboundItemMapper.selectListByInboundId(id);
        inboundVO.setItems(itemConvert.toVO(itemDOList));

        return inboundVO;
    }

    @Override
    public PageResult<SimplePurchaseInboundVO> page(SearchPurchaseInboundDTO searchDTO) {
        PageResult<PurchaseInboundDO> page = purchaseInboundMapper.selectPage(searchDTO);
        return purchaseInboundConvert.toSimpleVOPage(page);
    }

    @Override
    public PageResult<ItemWithPurchaseInboundVO> pageItem(SearchItemPageDTO searchDTO) {
        PageResult<PurchaseInboundItemWithInboundDO> page = purchaseInboundItemMapper.selectItemPage(searchDTO);
        return purchaseInboundConvert.toItemWithPurchaseInboundVOPage(page);
    }

    // ==================== 业务状态操作 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void close(Long id) {
        PurchaseInboundDO purchaseInboundDO = validateInboundExists(id);
        validateBusinessStatus(purchaseInboundDO, BusinessStatusEnum.NORMAL.getStatus());
        purchaseInboundMapper.updateBusinessStatusById(BusinessStatusEnum.CLOSED.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelClose(Long id) {
        PurchaseInboundDO purchaseInboundDO = validateInboundExists(id);
        validateBusinessStatus(purchaseInboundDO, BusinessStatusEnum.CLOSED.getStatus());
        purchaseInboundMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspend(Long id) {
        PurchaseInboundDO purchaseInboundDO = validateInboundExists(id);
        validateBusinessStatus(purchaseInboundDO, BusinessStatusEnum.NORMAL.getStatus());
        purchaseInboundMapper.updateBusinessStatusById(BusinessStatusEnum.SUSPENDED.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSuspend(Long id) {
        PurchaseInboundDO purchaseInboundDO = validateInboundExists(id);
        validateBusinessStatus(purchaseInboundDO, BusinessStatusEnum.SUSPENDED.getStatus());
        purchaseInboundMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    @Override
    protected void doApprove(Long id, List<PurchaseInboundItemDO> list) {
        initReturnedQuantity(id);
        // 通过inventoryHelper处理库存
        PurchaseInboundDO inbound = purchaseInboundMapper.selectById(id);
        inventoryHelper.processPurchaseInbound(inbound.getWarehouseId(), DocumentTypeEnum.INBOUND, id, BusinessTypeEnum.PURCHASE_INBOUND, list);
        //更新到货单的已入库数量
        List<PurchaseInboundQuantity> inboundQuantityList = list.stream()
                .filter(item -> item.getPurchaseArrivalItemId() != null)
                .map(item -> new PurchaseInboundQuantity(item.getId(), item.getPurchaseArrivalItemId(), item.getInboundQuantity()))
                .toList();
        if (!inboundQuantityList.isEmpty()) {
            documentRelationHelper.updateInboundQuantityWhenPurchaseInboundApprove(inboundQuantityList);
        }
    }

    private void initReturnedQuantity(Long id) {
        purchaseInboundItemMapper.initReturnedQuantity(id);
    }

    @Override
    protected void doCancelApprove(Long id, List<PurchaseInboundItemDO> list) {
        List<Long> businessItemIds = list.stream()
                .map(PurchaseInboundItemDO::getId)
                .collect(Collectors.toList());
        // 通过inventoryHelper处理库存反审核
        inventoryHelper.tryCancelApproveByDocumentAndBusinesses(DocumentTypeEnum.INBOUND, id, BusinessTypeEnum.PURCHASE_INBOUND, businessItemIds);
        //更新到货单的已入库数量
        List<PurchaseInboundQuantity> inboundQuantityList = list.stream()
                .filter(item -> item.getPurchaseArrivalItemId() != null)
                .map(item -> new PurchaseInboundQuantity(item.getId(), item.getPurchaseArrivalItemId(), item.getInboundQuantity()))
                .toList();
        if (!inboundQuantityList.isEmpty()) {
            documentRelationHelper.updateInboundQuantityWhenPurchaseInboundCancelApprove(inboundQuantityList);
        }
    }

    @Override
    protected void validateBeforeApprove(Long id) {
        PurchaseInboundDO purchaseInboundDO = validateInboundExists(id);
        validateDocumentStatus(purchaseInboundDO, DocumentStatusEnum.approveExpectStatus());
        commonValidate(id);
    }

    @Override
    protected void validateBeforeCancelApprove(Long id) {
        PurchaseInboundDO purchaseInboundDO = validateInboundExists(id);
        validateDocumentStatus(purchaseInboundDO, DocumentStatusEnum.cancelApproveExpectStatus());
    }

    @Override
    protected List<PurchaseInboundItemDO> getDocumentItemsWhenAuditing(Long id) {
        return purchaseInboundItemMapper.selectListByInboundId(id);
    }

    @Override
    protected List<Long> getNeedLockedIds(List<PurchaseInboundItemDO> list) {
        return list.stream().map(PurchaseInboundItemDO::getPurchaseArrivalItemId).collect(Collectors.toList());
    }

    @Override
    protected void updateDocumentApproveStatus(Long id, Integer status) {
        purchaseInboundMapper.updateApproveStatusById(status, id);
    }

    @Override
    @PostConstruct
    protected void initLockTypeEnum() {
        this.documentLockType = LockTypeEnum.PURCHASE_INBOUND;
        this.itemLockType = LockTypeEnum.PURCHASE_INBOUND_ITEM;
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void approve(Long id) {
//        RLock documentLock = redissonLockUtil.lock(LockTypeEnum.PURCHASE_INBOUND, id);
//        if (documentLock == null) {
//            throw exception(PURCHASE_INBOUND_LOCK_FAILED, id);
//        }
//        try {
//
//
//            PurchaseInboundDO inbound = purchaseInboundMapper.selectById(id);
//            List<PurchaseInboundItemDO> items = purchaseInboundItemMapper.selectListByInboundId(id);
//
//
//            // 更新单据状态
//            purchaseInboundMapper.updateApproveStatusById(DocumentStatusEnum.APPROVE.getStatus(), id);
//        } finally {
//            redissonLockUtil.safeUnlock(documentLock);
//        }
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void cancelApprove(Long id) {
//        RLock documentLock = redissonLockUtil.lock(LockTypeEnum.PURCHASE_INBOUND, id);
//        if (documentLock == null) {
//            throw exception(PURCHASE_INBOUND_LOCK_FAILED, id);
//        }
//        try {
//
//
//            // 更新单据状态
//            purchaseInboundMapper.updateApproveStatusById(DocumentStatusEnum.ROLLBACK.getStatus(), id);
//        } finally {
//            redissonLockUtil.safeUnlock(documentLock);
//        }
//    }

    // ==================== 业务查询 ====================


    @Override
    public Boolean isInboundByPurchaseArrivalId(Long purchaseArrivalId) {
        List<PurchaseInboundItemDO> list = purchaseInboundItemMapper.selectItemListByPurchaseArrivalId(purchaseArrivalId);
        return CollUtil.isNotEmpty(list);
    }

    // ==================== 引用验证 ====================

    @Override
    public void validateWhenMaterialDelete(Long materialId) {
        if (purchaseInboundItemMapper.countByMaterialId(materialId) > 0) {
            throw exception(PURCHASE_INBOUND_ITEMS_REFER_MATERIAL_EXITS);
        }
    }


    @Override
    public void validateWhenWarehouseDelete(Long warehouseId) {
        if (purchaseInboundMapper.countByWarehouseId(warehouseId) > 0) {
            throw exception(PURCHASE_INBOUND_REFER_WAREHOUSE_EXITS);
        }
    }

    @Override
    public void updateReturnedQuantityWhenPurchaseReturnedApprove(Long purchaseInboundItemId, BigDecimal quantity) {
        purchaseInboundItemMapper.updateReturnedQuantityWhenPurchaseReturnedApprove(purchaseInboundItemId, quantity);
    }

    @Override
    public void validateReturnedQuantity(Long purchaseInboundItemId, BigDecimal returnedQuantity) {
        PurchaseInboundItemDO purchaseInboundItemDO = purchaseInboundItemMapper.selectById(purchaseInboundItemId);
        if (returnedQuantity.compareTo(purchaseInboundItemDO.getReturnableQuantity()) > 0) {
            throw exception(PURCHASE_INBOUND_ITEM_RETURNABLE_QUANTITY_NOT_ENOUGH, purchaseInboundItemDO.getUnreturnedQuantity(), returnedQuantity);
        }
    }

    @Override
    public void updateReturnedQuantityWhenPurchaseReturnedCancelApprove(Long purchaseInboundItemId, BigDecimal quantity) {
        purchaseInboundItemMapper.updateReturnedQuantityWhenPurchaseReturnedCancelApprove(purchaseInboundItemId, quantity);
    }

    // ==================== 私有验证方法 ====================
    private void commonValidate(Long id) {
        PurchaseInboundDO purchaseInboundDO = validateInboundExists(id);
        List<PurchaseInboundItemDO> purchaseInboundItemDOList = purchaseInboundItemMapper.selectListByInboundId(id);
        commonValidate(purchaseInboundDO, purchaseInboundItemDOList);
    }


    private void commonValidate(PurchaseInboundDO purchaseInboundDO, List<PurchaseInboundItemDO> purchaseInboundItemDOList) {
        // 验证业务部门和用户关系
        adminUserApi.validateUserAndDept(purchaseInboundDO.getBusinessUserId(), purchaseInboundDO.getBusinessDepartmentId()).checkError();
        //验证仓库
        warehouseService.validateWarehouseEnableAndInStockOperationType(purchaseInboundDO.getWarehouseId(), StockOperationInTypeEnum.PURCHASE_INBOUND);
        //获取所有不为空的库位信息
        List<Long> warehousePositionIds = purchaseInboundItemDOList.stream()
                .map(PurchaseInboundItemDO::getWarehousePositionId)
                .filter(java.util.Objects::nonNull)
                .toList();
        if (CollUtil.isNotEmpty(warehousePositionIds)) {
            warehouseService.validatePositionEnableAndBelongWarehouse(purchaseInboundDO.getWarehouseId(), warehousePositionIds);
        }
        //验证物料是否存在
        if (CollUtil.isNotEmpty(purchaseInboundItemDOList)) {
            List<Long> materialIds = purchaseInboundItemDOList.stream()
                    .map(PurchaseInboundItemDO::getMaterialId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(materialIds)) {
                materialMasterDataService.validateExist(materialIds);
            }
        }

        validateDocumentApprovedAndQuantity(purchaseInboundItemDOList);

        validateUniqueCode(purchaseInboundItemDOList);
    }

    private PurchaseInboundDO validateInboundExists(Long id) {
        PurchaseInboundDO purchaseInboundDO = purchaseInboundMapper.selectById(id);
        if (Objects.isNull(purchaseInboundDO)) {
            throw exception(PURCHASE_INBOUND_NOT_EXISTS);
        }
        return purchaseInboundDO;
    }

    private void doUpdatePurchaseInboundItem(Long inboundId, List<PurchaseInboundItemDO> toUpdate) {
        toUpdate.forEach(
                item -> item.setInboundId(inboundId)
        );
        purchaseInboundItemMapper.updateBatch(toUpdate);
    }

    private void validateCanEdit(PurchaseInboundDO purchaseInboundDO) {
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(purchaseInboundDO.getDocumentStatus()) &&
                !DocumentStatusEnum.ROLLBACK.getStatus().equals(purchaseInboundDO.getDocumentStatus())) {
            throw exception(PURCHASE_INBOUND_CANNOT_EDIT);
        }
    }

    private void validateBusinessStatus(PurchaseInboundDO purchaseInboundDO, Integer status) {
        if (!Objects.equals(purchaseInboundDO.getBusinessStatus(), status)) {
            throw exception(PURCHASE_INBOUND_BUSINESS_STATUS_NOT_SUPPORT_CHANGE, purchaseInboundDO.getCode());
        }
    }

    // 新增：支持Integer[]参数的方法（与其他inbound实现保持一致）
    private void validateDocumentStatus(PurchaseInboundDO purchaseInboundDO, Integer[] expectedStatuses) {
        if (!ArrayUtil.contains(expectedStatuses, purchaseInboundDO.getDocumentStatus())) {
            throw exception(PURCHASE_INBOUND_DOCUMENT_STATUS_NOT_SUPPORT_CHANGE, purchaseInboundDO.getCode());
        }
    }

    private void fillDeptName(PurchaseInboundDO inbound) {
        if (inbound.getBusinessDepartmentId() != null) {
            DeptRespDTO dept = deptApi.getDept(inbound.getBusinessDepartmentId()).getCheckedData();
            if (dept != null) {
                inbound.setBusinessDepartmentName(dept.getName());
            }
        }
    }

    private void validateUniqueCode(List<PurchaseInboundItemDO> purchaseInboundItemDOList) {
        // 1. 收集本次提交的所有非空唯一码，并验证不重复
        Set<String> submittedUniqueCodes = new HashSet<>();

        // 2. 分组验证唯一码在本次提交中不重复
        purchaseInboundItemDOList.stream()
                .filter(item -> StrUtil.isNotBlank(item.getUniqueCode()))
                .forEach(item -> {
                    // 验证本次提交中唯一码不重复
                    if (!submittedUniqueCodes.add(item.getUniqueCode())) {
                        throw exception(PURCHASE_INBOUND_ITEM_UNIQUE_CODE_DUPLICATE, item.getUniqueCode());
                    }

                    // 获取物料信息
                    Boolean uniqueCodeManaged = materialService.isUniqueCodeManaged(item.getMaterialId());
                    // 根据物料唯一码管理设置进行验证
                    if (Objects.equals(Boolean.TRUE, uniqueCodeManaged)) {
                        //采购到货中，审核通过的记录中，唯一码不允许重复
                        if (purchaseInboundItemMapper.existsApprovedByUniqueCode(item.getUniqueCode())) {
                            throw exception(PURCHASE_INBOUND_ITEM_UNIQUE_CODE_DUPLICATE, item.getUniqueCode());
                        }
                        // 验证唯一码在库存中不重复
                        if (inventoryDetailService.existsByUniqueCode(item.getUniqueCode())) {
                            throw exception(PURCHASE_INBOUND_ITEM_UNIQUE_CODE_EXISTS_IN_INVENTORY, item.getMaterialName(), item.getUniqueCode());
                        }
                    } else {
                        // 物料未开启唯一码管理，唯一码必须为null
                        throw exception(PURCHASE_INBOUND_ITEM_UNIQUE_CODE_SHOULD_BE_NULL, item.getMaterialName());
                    }
                });

        // 3. 验证开启唯一码管理的物料没有填写唯一码
        purchaseInboundItemDOList.stream()
                .filter(item -> StrUtil.isBlank(item.getUniqueCode()))
                .forEach(item -> {
                    Boolean uniqueCodeManaged = materialService.isUniqueCodeManaged(item.getMaterialId());
                    if (Objects.equals(uniqueCodeManaged, Boolean.TRUE)) {
                        throw exception(PURCHASE_INBOUND_ITEM_UNIQUE_CODE_REQUIRED, item.getMaterialName());
                    }
                });

    }

    private void validateDocumentApprovedAndQuantity(List<PurchaseInboundItemDO> purchaseInboundItemDOList) {
        //验证提交过来的到货ItemId唯一
        Set<Long> purchaseArrivalItemSet = new HashSet<>();
        purchaseInboundItemDOList.forEach(item -> {
            if (!purchaseArrivalItemSet.add(item.getPurchaseArrivalItemId())) {
                throw exception(PURCHASE_INBOUND_ITEM_PURCHASE_ARRIVAL_ITEM_ID_DUPLICATE, item.getPurchaseArrivalItemId());
            }
        });

        //收集所有的到货单明细id，验证所有的到货单明细都是审核通过的
        Set<Long> purchaseArrivalItemIds = purchaseInboundItemDOList.stream()
                .map(PurchaseInboundItemDO::getPurchaseArrivalItemId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(purchaseArrivalItemIds)) {
            purchaseArrivalService.validateExistsAndApprove(purchaseArrivalItemIds);
        }

        //验证入库数量不能超过到货单的未入库数量，收集每个purchaseArrivalItemId的入库数量，然后和purchaseArrivalItemDO比较
        purchaseInboundItemDOList.forEach(
                item -> {
                    purchaseArrivalService.validateInboundQuantity(item.getPurchaseArrivalItemId(), item.getInboundQuantity());
                }
        );
    }

    private void doCreatePurchaseInboundItemList(Long inboundId, List<PurchaseInboundItemDO> itemList) {
        if (CollUtil.isEmpty(itemList)) {
            return;
        }
        itemList.forEach(item -> {
            item.setInboundId(inboundId);
            purchaseInboundItemMapper.insert(item);
        });
    }


    /**
     * 智能更新采购入库单明细 - 区分新增、修改、删除
     */
    private void updatePurchaseInboundItemSmart(Long inboundId, List<PurchaseInboundItemDO> newItemList) {
        // 获取现有明细
        List<PurchaseInboundItemDO> existingItems = purchaseInboundItemMapper.selectListByInboundId(inboundId);
        // 分类处理
        List<PurchaseInboundItemDO> toInsert = new ArrayList<>();
        List<PurchaseInboundItemDO> toUpdate = new ArrayList<>();
        Set<Long> updateItemIds = new HashSet<>();

        for (PurchaseInboundItemDO itemDTO : newItemList) {
            if (itemDTO.getId() == null) {
                // 新增
                itemDTO.setInboundId(inboundId);
                toInsert.add(itemDTO);
            } else {
                // 修改
                updateItemIds.add(itemDTO.getId());
                itemDTO.setInboundId(inboundId);
                toUpdate.add(itemDTO);
            }
        }

        // 找出要删除的明细
        List<Long> toDelete = existingItems.stream()
                .map(PurchaseInboundItemDO::getId)
                .filter(id -> !updateItemIds.contains(id))
                .collect(Collectors.toList());

        // 执行操作
        if (CollUtil.isNotEmpty(toInsert)) {
            doCreatePurchaseInboundItemList(inboundId, toInsert);
        }
        if (CollUtil.isNotEmpty(toUpdate)) {
            doUpdatePurchaseInboundItem(inboundId, toUpdate);
        }
        if (CollUtil.isNotEmpty(toDelete)) {
            purchaseInboundItemMapper.deleteByIds(toDelete);
        }
    }


}
