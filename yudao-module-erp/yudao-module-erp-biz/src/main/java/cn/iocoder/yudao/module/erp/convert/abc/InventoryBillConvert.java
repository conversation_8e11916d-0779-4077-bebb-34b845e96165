package cn.iocoder.yudao.module.erp.convert.abc;



import cn.iocoder.yudao.module.erp.controller.admin.abc.vo.*;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.InventoryBillDO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface  InventoryBillConvert {

    InventoryBillDO toDO(CreateInventoryBillDTO creatDTO);

    InventoryBillDO toDO(UpdateInventoryBillDTO updateDTO);

    InventoryBillVO toVO(InventoryBillDO inventoryBillDO);

    List<InventoryBillVO> toVO(List<InventoryBillDO> inventoryBillDOList);

    SimpleInventoryBillVO toSimpleVO(InventoryBillDO inventoryBillDO);

    List<SimpleInventoryBillVO> toSimpleVO(List<InventoryBillDO> inventoryBillDOList);
}