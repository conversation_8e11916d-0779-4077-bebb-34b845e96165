package cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.view;

import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.common.InboundDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.PurchaseInboundItemDO;
import lombok.Data;

/**
 * 采购入库单明细与入库单的视图对象
 *
 * <AUTHOR>
 */
@Data
public class PurchaseInboundItemWithInboundDO extends PurchaseInboundItemDO {

    /**
     * 入库单
     */
    private InboundDO inbound;

}
