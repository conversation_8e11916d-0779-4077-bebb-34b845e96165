package cn.iocoder.yudao.module.erp.dal.mysql.purchase.materialconfig;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.materialconfig.PurchaseMaterialConfigDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 采购物料平均周期配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseMaterialConfigMapper extends BaseMapperX<PurchaseMaterialConfigDO> {


    default PurchaseMaterialConfigDO selectByMaterialId(Long materialId) {
        return selectOne(PurchaseMaterialConfigDO::getMaterialId, materialId);
    }


}
