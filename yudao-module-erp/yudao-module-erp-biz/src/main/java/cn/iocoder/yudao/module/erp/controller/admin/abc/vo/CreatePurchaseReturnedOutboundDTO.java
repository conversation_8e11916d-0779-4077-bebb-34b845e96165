package cn.iocoder.yudao.module.erp.controller.admin.abc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 采购退货出库单新增 DTO")
@Data
public class CreatePurchaseReturnedOutboundDTO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15613")
    private Long id;

    @Schema(description = "单据号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单据号不能为空")
    private String code;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @Schema(description = "0编制中 1审核通过 2回退", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "0编制中 1审核通过 2回退不能为空")
    private Integer documentStatus;

    @Schema(description = "业务状态：0关闭 1正常 2挂起", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业务状态：0关闭 1正常 2挂起不能为空")
    private Integer businessStatus;

    @Schema(description = "出库日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "出库日期不能为空")
    private LocalDateTime outboundDate;

    @Schema(description = "仓库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31837")
    @NotNull(message = "仓库ID不能为空")
    private Long warehouseId;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "仓库名称不能为空")
    private String warehouseName;

    @Schema(description = "供应商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24917")
    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;

    @Schema(description = "供应商名称", example = "芋艿")
    private String supplierName;

    @Schema(description = "业务部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13559")
    @NotNull(message = "业务部门ID不能为空")
    private Long businessDepartmentId;

    @Schema(description = "业务部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "业务部门名称不能为空")
    private String businessDepartmentName;

    @Schema(description = "业务员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28466")
    @NotNull(message = "业务员ID不能为空")
    private Long businessUserId;

    @Schema(description = "业务员姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "业务员姓名不能为空")
    private String businessUserName;

    @Schema(description = "物料类型:采购单的物料类型（业务字典）", example = "2")
    private String materialType;

    @Schema(description = "退货通知单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27355")
    @NotNull(message = "退货通知单ID不能为空")
    private Long returnedId;

    @Schema(description = "退货通知单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "退货通知单号不能为空")
    private String returnedCode;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;


    @Schema(description = "采购退货出库单明细列表")
    private List<PurchaseReturnedOutboundItemDTO> items;
}