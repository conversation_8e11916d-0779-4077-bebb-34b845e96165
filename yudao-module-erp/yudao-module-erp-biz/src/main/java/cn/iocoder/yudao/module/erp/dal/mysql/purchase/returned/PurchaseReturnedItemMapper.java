package cn.iocoder.yudao.module.erp.dal.mysql.purchase.returned;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.returned.vo.SearchItemPageDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.returned.PurchaseReturnedDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.returned.PurchaseReturnedItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.returned.view.ItemWithPurchaseReturned;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.List;

/**
 * 采购退货通知单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseReturnedItemMapper extends BaseMapperX<PurchaseReturnedItemDO> {

    default PageResult<PurchaseReturnedItemDO> selectPage(PageParam reqVO, Long purchaseReturnedId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PurchaseReturnedItemDO>()
                .eq(PurchaseReturnedItemDO::getPurchaseReturnedId, purchaseReturnedId)
                .orderByDesc(PurchaseReturnedItemDO::getId));
    }

    default int deleteByPurchaseReturnedId(Long purchaseReturnedId) {
        return delete(PurchaseReturnedItemDO::getPurchaseReturnedId, purchaseReturnedId);
    }

    /**
     * 根据主表ID查询明细列表
     *
     * @param purchaseReturnedId 主表ID
     * @return 明细列表
     */
    default List<PurchaseReturnedItemDO> selectListByPurchaseReturnedId(Long purchaseReturnedId) {
        return selectList(PurchaseReturnedItemDO::getPurchaseReturnedId, purchaseReturnedId);
    }

    /**
     * 分页查询明细与主表组合数据
     *
     * @param searchDTO 查询条件
     * @return 分页结果
     */
    default PageResult<ItemWithPurchaseReturned> selectItemPage(SearchItemPageDTO searchDTO) {
        MPJLambdaWrapper<PurchaseReturnedItemDO> wrapper = new MPJLambdaWrapperX<PurchaseReturnedItemDO>()
                .selectAll(PurchaseReturnedItemDO.class)
                .likeIfPresent(PurchaseReturnedItemDO::getMaterialCode, searchDTO.getMaterialCode())
                .likeIfPresent(PurchaseReturnedItemDO::getMaterialName, searchDTO.getMaterialName())
                .likeIfPresent(PurchaseReturnedItemDO::getBatchNo, searchDTO.getBatchNo())
                .likeIfPresent(PurchaseReturnedItemDO::getUniqueCode, searchDTO.getUniqueCode())

                .selectAssociation(PurchaseReturnedDO.class, ItemWithPurchaseReturned::getPurchaseReturned)
                .leftJoin(PurchaseReturnedDO.class, PurchaseReturnedDO::getId, PurchaseReturnedItemDO::getPurchaseReturnedId)
                .likeIfExists(PurchaseReturnedDO::getCode, searchDTO.getCode())
                .eqIfExists(PurchaseReturnedDO::getDocumentStatus, searchDTO.getDocumentStatus())

                .orderByDesc(PurchaseReturnedDO::getCreateTime)
                .orderByDesc(PurchaseReturnedDO::getId);

        if (searchDTO.getCreateTime() != null && searchDTO.getCreateTime().length > 0) {
            LocalDate start = searchDTO.getCreateTime()[0];
            LocalDate end = searchDTO.getCreateTime()[1];
            if (start != null && end != null) {
                wrapper.between(PurchaseReturnedDO::getCreateTime, start.atStartOfDay(), end.plusDays(1).atStartOfDay());
            } else if (start != null) {
                wrapper.ge(PurchaseReturnedDO::getCreateTime, start.atStartOfDay());
            } else if (end != null) {
                wrapper.le(PurchaseReturnedDO::getCreateTime, end.plusDays(1).atStartOfDay());
            }
        }

        return selectJoinPage(searchDTO, ItemWithPurchaseReturned.class, wrapper);
    }

    default void initOutboundQuantity(Long id) {
        update(new LambdaUpdateWrapper<PurchaseReturnedItemDO>()
                .setSql("confirmed_outbound_quantity = 0")
                .setSql("unconfirmed_outbound_quantity = returned_quantity")
                .eq(PurchaseReturnedItemDO::getPurchaseReturnedId, id));

    }
}
