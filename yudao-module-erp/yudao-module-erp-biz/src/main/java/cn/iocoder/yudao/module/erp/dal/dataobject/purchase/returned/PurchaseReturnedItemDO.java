package cn.iocoder.yudao.module.erp.dal.dataobject.purchase.returned;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 采购退货通知单明细 DO
 *
 * <AUTHOR>
 */
@TableName("erp_purchase_returned_item")
@KeySequence("erp_purchase_returned_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseReturnedItemDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 退货通知单ID
     */
    private Long purchaseReturnedId;
    /**
     * 来源到货单ID
     */
    private Long purchaseArrivalId;
    /**
     * 来源入库单ID(inbound_id)
     */
    private Long purchaseInboundId;
    /**
     * 来源入库单明细ID
     */
    private Long purchaseInboundItemId;
    /**
     * 物料ID
     */
    private Long materialId;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料规格型号
     */
    private String materialSpec;
    /**
     * 物料主计量单位
     */
    private String materialUom;
    /**
     * 唯一码（启用唯一码管理时必填）
     */
    private String uniqueCode;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 仓库ID
     */
    private Long warehouseId;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 库位ID
     */
    private Long warehousePositionId;
    /**
     * 库位名称
     */
    private String warehousePositionName;
    /**
     * 退货数量(本次用户输入的退货数量)
     */
    private BigDecimal returnedQuantity;
    /**
     * 已出库数量:本次退货当中已经出库的数量
     */
    private BigDecimal confirmedOutboundQuantity;
    /**
     * 未出库数量：已出库数量+未出库数量=returned_quantity
     */
    private BigDecimal unconfirmedOutboundQuantity;
    /**
     * 备注
     */
    private String remark;

}