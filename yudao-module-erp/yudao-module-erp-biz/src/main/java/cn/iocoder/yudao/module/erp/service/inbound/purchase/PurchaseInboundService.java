package cn.iocoder.yudao.module.erp.service.inbound.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.inbound.purchase.vo.*;
import jakarta.validation.Valid;

import java.math.BigDecimal;

/**
 * 采购入库单 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseInboundService {

    /**
     * 创建采购入库单
     *
     * @param createDTO 创建信息
     * @return Id
     */
    Long create(@Valid CreatePurchaseInboundDTO createDTO);

    /**
     * 更新采购入库单
     *
     * @param id        采购入库单ID
     * @param updateDTO 更新信息
     */
    void update(Long id, @Valid UpdatePurchaseInboundDTO updateDTO);

    /**
     * 删除采购入库单
     *
     * @param id 采购入库单ID
     */
    void delete(Long id);

    /**
     * 获得采购入库单
     *
     * @param id 采购入库单ID
     * @return 采购入库单
     */
    PurchaseInboundVO get(Long id);

    /**
     * 获得采购入库单分页
     *
     * @param searchDTO 分页查询
     * @return 采购入库单分页
     */
    PageResult<SimplePurchaseInboundVO> page(SearchPurchaseInboundDTO searchDTO);

    /**
     * 获得采购入库单明细分页
     *
     * @param searchDTO 分页查询
     * @return 采购入库单明细分页
     */
    PageResult<ItemWithPurchaseInboundVO> pageItem(SearchItemPageDTO searchDTO);

    // ==================== 业务状态操作 ====================

    /**
     * 关闭采购入库单
     *
     * @param id 采购入库单ID
     */
    void close(Long id);

    /**
     * 取消关闭采购入库单
     *
     * @param id 采购入库单ID
     */
    void cancelClose(Long id);

    /**
     * 挂起采购入库单
     *
     * @param id 采购入库单ID
     */
    void suspend(Long id);

    /**
     * 取消挂起采购入库单
     *
     * @param id 采购入库单ID
     */
    void cancelSuspend(Long id);

    /**
     * 审核采购入库单
     *
     * @param id 采购入库单ID
     */
    void approve(Long id);

    /**
     * 取消审核采购入库单
     *
     * @param id 采购入库单ID
     */
    void cancelApprove(Long id);

    // ==================== 业务查询 ====================


    /**
     * 根据采购到货单ID判断是否已入库
     *
     * @param purchaseArrivalId 采购到货单ID
     * @return 是否已入库
     */
    Boolean isInboundByPurchaseArrivalId(Long purchaseArrivalId);

    /**
     * 采购退货单审核时更新采购入库单明细的已退货数量
     *
     * @param purchaseInboundItemId 采购入库单明细ID
     * @param quantity              退货数量
     */
    void updateReturnedQuantityWhenPurchaseReturnedApprove(Long purchaseInboundItemId, BigDecimal quantity);

    /**
     * 验证退货数量小于 采购入库单的可退货数量
     *
     * @param purchaseInboundItemId
     * @param returnedQuantity
     */
    void validateReturnedQuantity(Long purchaseInboundItemId, BigDecimal returnedQuantity);

    void updateReturnedQuantityWhenPurchaseReturnedCancelApprove(Long purchaseInboundItemId, BigDecimal quantity);
}
