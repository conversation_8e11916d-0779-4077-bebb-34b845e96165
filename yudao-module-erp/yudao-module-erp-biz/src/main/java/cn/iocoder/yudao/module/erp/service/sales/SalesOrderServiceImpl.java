package cn.iocoder.yudao.module.erp.service.sales;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.BusinessStatusEnum;
import cn.iocoder.yudao.module.erp.constant.BusinessTypeEnum;
import cn.iocoder.yudao.module.erp.constant.DocumentStatusEnum;
import cn.iocoder.yudao.module.erp.constant.LockTypeEnum;
import cn.iocoder.yudao.module.erp.controller.admin.sales.vo.*;
import cn.iocoder.yudao.module.erp.convert.sales.SalesOrderConvert;
import cn.iocoder.yudao.module.erp.convert.sales.SalesOrderLineConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.sales.SalesOrderDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.sales.SalesOrderLineDO;
import cn.iocoder.yudao.module.erp.dal.mysql.sales.SalesOrderLineMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.sales.SalesOrderMapper;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataRefer;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataService;
import cn.iocoder.yudao.module.erp.service.common.AdminUserUtil;
import cn.iocoder.yudao.module.erp.service.common.DocumentRelationHelper;
import cn.iocoder.yudao.module.erp.service.common.RedissonLockUtil;
import cn.iocoder.yudao.module.erp.service.common.SerialNumberService;
import cn.iocoder.yudao.module.erp.service.customer.CustomerRefer;
import cn.iocoder.yudao.module.erp.service.customer.CustomerService;
import cn.iocoder.yudao.module.erp.service.packaging.PackagingTypeService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.*;

/**
 * 销售订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SalesOrderServiceImpl implements SalesOrderService, MaterialMasterDataRefer, CustomerRefer {
    private final String DEFAULT_CUSTOMER_ORDER_CODE = "0";

    @Resource
    private SalesOrderMapper salesOrderMapper;
    @Resource
    private SalesOrderLineMapper salesOrderLineMapper;

    @Resource
    private SalesOrderConvert salesOrderConvert;
    @Resource
    private SalesOrderLineConvert salesOrderLineConvert;

    @Resource
    @Lazy
    private CustomerService customerService;

    @Resource
    @Lazy
    private MaterialMasterDataService materialMasterDataService;

    @Resource
    private SerialNumberService serialNumberService;

    @Resource
    @Lazy
    private AdminUserApi adminUserApi;

    @Resource
    @Lazy
    private AdminUserUtil adminUserUtil;

    @Resource
    @Lazy
    private PackagingTypeService packagingTypeService;
    @Resource
    @Lazy
    private DocumentRelationHelper documentRelationHelper;

    @Resource
    @Lazy
    private List<CustomerOrderCodeRefer> customerOrderCodeRefers;

    @Resource
    private RedissonLockUtil redissonLockUtil;

    @Override
    @Transactional
    public Long createSalesOrder(CreateSalesOrderDTO createDTO) {
        //通用验证
        commonValidate(createDTO);


        SalesOrderDO salesOrder = salesOrderConvert.toDO(createDTO);
        salesOrder.setBusinessType(BusinessTypeEnum.SALES_ORDER.getType());
        salesOrder.setDocumentStatus(DocumentStatusEnum.DRAFT.getStatus());
        salesOrder.setBusinessStatus(BusinessStatusEnum.NORMAL.getStatus());
        salesOrder.setCode(serialNumberService.generateOrderNumber(BusinessTypeEnum.SALES_ORDER.getPrefix()));
        salesOrderMapper.insert(salesOrder);
        Long salesOrderId = salesOrder.getId();

        //销售订单行
        List<SalesOrderLineDTO> salesOrderLineList = createDTO.getSalesOrderLineList();
        addSalesOrderLine(salesOrderId, salesOrderLineList);

        // 返回
        return salesOrder.getId();
    }

    private void commonValidate(BaseSalesOderDTO createDTO) {
        validateCustomerOrderCode(createDTO.getCustomerOrderStatus(), createDTO.getCustomerOrderCode());

        validateDeliveryDate(createDTO.getDeliveryDate(), createDTO.getOrderDate());
        //校验客户档案信息不能是停用的
        customerService.validateEnable(createDTO.getCustomerInfo().getId());
        //验证业务员businessUserId和businessDepartment存在隶属关系
        adminUserApi.validateUserAndDept(createDTO.getBusinessUserId(), createDTO.getBusinessDepartment()).checkError();

        //收集salesOrderLineList的materialId
        if (CollUtil.isNotEmpty(createDTO.getSalesOrderLineList())) {
            validateMaterialExistAndEnable(createDTO.getSalesOrderLineList());
        }
        //搜集salesOrderLineList的packagingSpecId
        if (CollUtil.isNotEmpty(createDTO.getSalesOrderLineList())) {
            List<Long> packagingSpecIds = createDTO.getSalesOrderLineList().stream().map(SalesOrderLineDTO::getPackagingSpecId).toList();
            packagingTypeService.validateSpecAndTypeEnable(packagingSpecIds);
        }

    }

    private void validateCustomerOrderCode(Boolean customerOrderStatus, String customerOrderCode) {

        if (!customerOrderStatus && !Objects.equals(customerOrderCode, DEFAULT_CUSTOMER_ORDER_CODE)) {
            throw exception(SALES_ORDER_CUSTOMER_ORDER_CODE_ERROR);
        }
    }

    private void validateDeliveryDate(LocalDateTime deliveryDate, LocalDateTime orderDate) {
        if (Objects.nonNull(deliveryDate) && Objects.nonNull(orderDate) && deliveryDate.isBefore(orderDate)) {
            throw exception(SALES_ORDER_DELIVERY_DATE_ERROR);
        }
    }

    private void validateMaterialExistAndEnable(List<SalesOrderLineDTO> salesOrderLineList) {
        List<Long> materialIds = salesOrderLineList.stream().map(SalesOrderLineDTO::getMaterialId).toList();
        materialMasterDataService.validateExistAndEnableByIds(materialIds);
    }

    @Override
    @Transactional
    public void updateSalesOrder(Long id, UpdateSalesOrderDTO updateDTO) {
        // 校验存在
        validateSalesOrderExists(id);
        // 校验单据状态，只有编制中和退回的才能编辑
        validateSalesOrderCanEdit(id);

        commonValidate(updateDTO);

        List<SalesOrderLineDTO> salesOrderLineList = updateDTO.getSalesOrderLineList();
        //从salesOrderLineList中找到没有Id的记录，作为新增的记录
        List<SalesOrderLineDTO> newSalesOrderLineList = salesOrderLineList.stream().filter(item -> item.getId() == null).toList();
        //从salesOrderLineList中找到有Id的记录,作为更新的记录
        List<SalesOrderLineDTO> updateSalesOrderLineList = salesOrderLineList.stream().filter(item -> item.getId() != null).toList();

        if (CollUtil.isNotEmpty(newSalesOrderLineList)) {
            validateMaterialExistAndEnable(newSalesOrderLineList);
        }
        // 更新
        SalesOrderDO salesOrder = salesOrderConvert.toDO(updateDTO);
        salesOrder.setId(id);
        salesOrderMapper.updateById(salesOrder);

        //根据salesOrderId查询所有现存的订单行记录。
        List<SalesOrderLineDO> existSalesOrderLineList = salesOrderLineMapper.selectListBySalesOrderId(id);
        //从existSalesOrderLineList中找出需要删除的记录：SalesOrderLineDO的id不在updateSalesOrderLineList的id集合中
        Set<Long> updateIds = updateSalesOrderLineList.stream()
                .map(SalesOrderLineDTO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<SalesOrderLineDO> deleteSalesOrderLineList = existSalesOrderLineList.stream()
                .filter(item -> !updateIds.contains(item.getId()))
                .toList();
        if (CollUtil.isNotEmpty(deleteSalesOrderLineList)) {
            salesOrderLineMapper.deleteByIds(deleteSalesOrderLineList.stream().map(SalesOrderLineDO::getId).toList());
        }

        if (CollUtil.isNotEmpty(newSalesOrderLineList)) {
            addSalesOrderLine(id, newSalesOrderLineList);
        }

        if (CollUtil.isNotEmpty(updateSalesOrderLineList)) {
            List<SalesOrderLineDO> updateSalesOrderLineDOS = salesOrderLineConvert.toDO(updateSalesOrderLineList);
            salesOrderLineMapper.updateBatch(updateSalesOrderLineDOS);
        }
    }

    private void validateSalesOrderCanEdit(Long id) {
        SalesOrderDO salesOrderDO = salesOrderMapper.selectById(id);
        if (DocumentStatusEnum.DRAFT.getStatus().equals(salesOrderDO.getDocumentStatus())) {
            return;
        }
        if (DocumentStatusEnum.ROLLBACK.getStatus().equals(salesOrderDO.getDocumentStatus())) {
            return;
        }
        throw exception(SALES_ORDER_NOT_EDITABLE);

    }

    private void addSalesOrderLine(Long salesOrderId, List<SalesOrderLineDTO> newSalesOrderLineList) {
        if (CollUtil.isNotEmpty(newSalesOrderLineList)) {
            List<SalesOrderLineDO> newSalesOrderLineDOS = salesOrderLineConvert.toDO(newSalesOrderLineList);
            //给每个销售订单行设置销售订单id
            newSalesOrderLineDOS.forEach(item -> {
                item.setSalesOrderId(salesOrderId);
            });
            salesOrderLineMapper.insertBatch(newSalesOrderLineDOS);
        }
    }

    @Override
    @Transactional
    public void deleteSalesOrder(Long id) {
        // 校验存在
        validateSalesOrderExists(id);
        validateSalesOrderCanEdit(id);
        // 删除
        salesOrderMapper.deleteById(id);
        salesOrderLineMapper.deleteBySalesOrderId(id);
    }

    private void validateSalesOrderExists(Long id) {
        if (salesOrderMapper.selectById(id) == null) {
            throw exception(SALES_ORDER_NOT_EXISTS, id);
        }
    }

    @Override
    public SalesOrderVO getSalesOrder(Long id) {
        validateSalesOrderExists(id);

        SalesOrderDO salesOrderDO = salesOrderMapper.selectById(id);
        SalesOrderVO vo = salesOrderConvert.toVO(salesOrderDO);
        List<SalesOrderLineDO> salesOrderLineDOList = salesOrderLineMapper.selectListBySalesOrderId(id);
        vo.setSalesOrderLineList(salesOrderLineConvert.toVO(salesOrderLineDOList));

        return vo;
    }

    @Override
    public PageResult<SimpleSalesOrderVO> pageSalesOrder(SearchSalesOrderPageDTO pageReqVO) {
        PageResult<SalesOrderDO> salesOrderDOPageResult = salesOrderMapper.selectPage(pageReqVO);
        List<SalesOrderDO> list = salesOrderDOPageResult.getList();
        if (CollUtil.isEmpty(list)) {
            return PageResult.empty();
        }
        Map<Long, AdminUserRespDTO> adminUserMap = new HashMap<>();
        //找到销售订单的businessUserId
        List<Long> businessUserIds = list.stream().map(SalesOrderDO::getBusinessUserId).toList();
        if (CollUtil.isNotEmpty(businessUserIds)) {
            adminUserMap = adminUserUtil.listUserMap(businessUserIds);
        }
        //调用
        List<SimpleSalesOrderVO> simpleSalesOrderVOList = salesOrderConvert.toSimpleVO(list);
        for (SimpleSalesOrderVO simpleSalesOrderVO : simpleSalesOrderVOList) {
            simpleSalesOrderVO.setBusinessUserName(adminUserMap.get(simpleSalesOrderVO.getBusinessUserId()) == null ? null : adminUserMap.get(simpleSalesOrderVO.getBusinessUserId()).getNickname());
        }

        return new PageResult<>(simpleSalesOrderVOList, salesOrderDOPageResult.getTotal());
    }

    @Override
    public void closeSalesOrder(Long id) {
        validateSalesOrderExists(id);
        salesOrderMapper.updateBusinessStatusById(BusinessStatusEnum.CLOSED.getStatus(), id);
    }

    @Override
    public void cancelCloseSalesOrder(Long id) {
        validateSalesOrderExists(id);
        validateSalesBusinessStatus(id, BusinessStatusEnum.CLOSED.getStatus());
        salesOrderMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    private void validateSalesBusinessStatus(Long id, Integer status) {
        SalesOrderDO salesOrderDO = salesOrderMapper.selectById(id);
        if (!Objects.equals(salesOrderDO.getBusinessStatus(), status)) {
            throw exception(SALES_ORDER_NOT_BUSINESS_STATUS_CHANGE);
        }
    }

    @Override
    public void suspendSalesOrder(Long id) {
        validateSalesOrderExists(id);
        salesOrderMapper.updateBusinessStatusById(BusinessStatusEnum.SUSPENDED.getStatus(), id);
    }

    @Override
    public void cancelSuspendSalesOrder(Long id) {
        validateSalesOrderExists(id);
        validateSalesBusinessStatus(id, BusinessStatusEnum.SUSPENDED.getStatus());
        salesOrderMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveSalesOrder(Long id) {
        RLock documentLock = redissonLockUtil.lock(LockTypeEnum.SALES_ORDER, id);
        try {
            validateSalesOrderExists(id);
            validateSalesOrderCanEdit(id);
            initShippingQuantity(id);
            salesOrderMapper.updateApproveStatusById(DocumentStatusEnum.APPROVE.getStatus(), id);
        } finally {
            redissonLockUtil.safeUnlock(documentLock);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelApproveSalesOrder(Long id) {
        RLock documentLock = redissonLockUtil.lock(LockTypeEnum.SALES_ORDER, id);
        try {
            validateSalesOrderExists(id);
            //校验当前记录的状态是审核通过
            validateSalesOrderCanCancelApprove(id);

            documentRelationHelper.validateSalesOrderCanCancelApprove(id);

            salesOrderMapper.updateApproveStatusById(DocumentStatusEnum.ROLLBACK.getStatus(), id);
        } finally {
            redissonLockUtil.safeUnlock(documentLock);
        }
    }

    private void validateSalesOrderCanCancelApprove(Long id) {
        SalesOrderDO salesOrderDO = salesOrderMapper.selectById(id);
        if (!Objects.equals(DocumentStatusEnum.APPROVE.getStatus(), salesOrderDO.getDocumentStatus())) {
            throw exception(SALES_ORDER_NOT_CANCEL_APPROVE);
        }
    }

    @Override
    public PageResult<SalesOrderLineWithOrderVO> pageSalesOrderLine(SearchSalesOrderLinePageDTO searchSalesOrderLine) {
        PageResult<SalesOrderLineWithOrderDTO> salesOrderLineWithOrderDTOPageResult = salesOrderLineMapper.selectAllLineWithOrderPage(searchSalesOrderLine);
        if (salesOrderLineWithOrderDTOPageResult != null) {
            List<SalesOrderLineWithOrderDTO> list = salesOrderLineWithOrderDTOPageResult.getList();
            List<SalesOrderLineWithOrderVO> salesOrderLineWithOrderVO = salesOrderLineConvert.toSalesOrderLineWithOrderVO(list);
            //收集salesOrderLineWithOrderVO的.salesOrder.businessUserId
            List<Long> businessUserIds = salesOrderLineWithOrderVO.stream().map(SalesOrderLineWithOrderVO::getSalesOrder).map(SimpleSalesOrderVO::getBusinessUserId).toList();
            Map<Long, AdminUserRespDTO> adminUserMap = adminUserUtil.listUserMap(businessUserIds);
            for (SalesOrderLineWithOrderVO item : salesOrderLineWithOrderVO) {
                item.getSalesOrder().setBusinessUserName(adminUserMap.get(item.getSalesOrder().getBusinessUserId()) == null ? null : adminUserMap.get(item.getSalesOrder().getBusinessUserId()).getNickname());
            }

            return new PageResult<>(salesOrderLineWithOrderVO, salesOrderLineWithOrderDTOPageResult.getTotal());
        }
        return PageResult.empty();
    }

    @Override
    public List<SalesOrderLineWithOrderVO> listSalesOrderLine(List<Long> salesOrderLineIds) {
        List<SalesOrderLineWithOrderDTO> lineWithOrderDTOList = salesOrderLineMapper.listSalesOrderLine(salesOrderLineIds);
        return salesOrderLineConvert.toSalesOrderLineWithOrderVO(lineWithOrderDTOList);
    }

    @Override
    public void validateSalesOrderCanShipping(SalesOrderLineWithOrderVO salesOrderLineWithOrderVO, BigDecimal shippingQuantity) {
        if (!Objects.equals(DocumentStatusEnum.APPROVE.getStatus(), salesOrderLineWithOrderVO.getSalesOrder().getDocumentStatus())) {
            throw exception(SALES_ORDER_NOT_APPROVE, salesOrderLineWithOrderVO.getSalesOrderId());
        }
        if (shippingQuantity.compareTo(salesOrderLineWithOrderVO.getUnshippedNum()) > 0) {
            throw exception(SALES_ORDER_LINE_SHIPPING_QUANTITY_ERROR, shippingQuantity);
        }

    }

    @Override
    public void updateSalesOrderLineShippingQuantity(Long salesOrderLineId, BigDecimal shippingQuantity) {
        salesOrderLineMapper.updateSalesOrderLineShippingQuantity(salesOrderLineId, shippingQuantity);
    }

    @Override
    public void resetShippingQuantity(Long salesOrderLineId, BigDecimal shippingQuantity) {
        salesOrderLineMapper.resetShippingQuantity(salesOrderLineId, shippingQuantity);
    }

    @Override
    public void validateEnable(Long salesOrderId) {
        SalesOrderDO salesOrderDO = salesOrderMapper.selectById(salesOrderId);
        if (salesOrderDO == null) {
            throw exception(SALES_ORDER_NOT_EXISTS, salesOrderId);
        }
        if (!DocumentStatusEnum.APPROVE.getStatus().equals(salesOrderDO.getDocumentStatus())) {
            throw exception(SALES_ORDER_NOT_APPROVE, salesOrderId);
        }
        if (!BusinessStatusEnum.NORMAL.getStatus().equals(salesOrderDO.getBusinessStatus())) {
            throw exception(SALES_ORDER_NOT_NORMAL, salesOrderId);
        }
    }

    @Override
    public void initShippingQuantity(Long salesOrderId) {
        salesOrderLineMapper.initShippingQuantityBySalesOrderId(salesOrderId);
    }

    @Override
    public SalesOrderLineVO selectItemBySalesOrderItemId(Long salesOrderLineId) {
        return salesOrderLineConvert.toVO(salesOrderLineMapper.selectById(salesOrderLineId));
    }

    @Override
    public void updateShippedQuantityWhenShippingApprove(Long salesOrderItemId, BigDecimal quantity) {
        salesOrderLineMapper.updateShippedQuantityWhenShippingApprove(salesOrderItemId, quantity);
    }

    @Override
    public void updateShippedQuantityWhenShippingCancelApprove(Long salesOrderItemId, BigDecimal quantity) {
        salesOrderLineMapper.updateShippedQuantityWhenShippingCancelApprove(salesOrderItemId, quantity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setCustomerOrderCode(Long salesOrderId, String customerOrderCode) {
        // 校验销售订单存在
        validateSalesOrderExists(salesOrderId);

        // 校验客户订单是否已经设置过
        validateCustomerOrderCodeSet(salesOrderId);

        // 更新销售订单的客户订单号
        SalesOrderDO updateObj = new SalesOrderDO();
        updateObj.setId(salesOrderId);
        updateObj.setCustomerOrderCode(customerOrderCode);
        updateObj.setCustomerOrderStatus(true);
        salesOrderMapper.updateById(updateObj);

        // 通知所有依赖单据更新客户订单号
        for (CustomerOrderCodeRefer customerOrderCodeRefer : customerOrderCodeRefers) {
            customerOrderCodeRefer.updateCustomerOrderCode(salesOrderId, customerOrderCode);
        }
    }

    private void validateCustomerOrderCodeSet(Long salesOrderId) {
        // 验证客户订单号是否已经被设置
        SalesOrderDO salesOrderDO = salesOrderMapper.selectById(salesOrderId);
        if (salesOrderDO.getCustomerOrderStatus()) {
            throw exception(SALES_ORDER_CUSTOMER_ORDER_CODE_SET, salesOrderDO.getCustomerOrderCode());
        }
    }

    @Override
    public void validateWhenMaterialDelete(Long materialId) {
        if (salesOrderLineMapper.countByMaterialId(materialId) > 0) {
            throw exception(SALES_ORDER_LINE_REFER_MATERIAL_EXITS);
        }
    }

    @Override
    public void validateWhenCustomerDelete(Long customerId) {
        if (salesOrderMapper.countByCustomerId(customerId) > 0) {
            throw exception(SALES_ORDER_REFER_CUSTOMER_EXITS);
        }
    }
}