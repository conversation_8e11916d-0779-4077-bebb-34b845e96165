package cn.iocoder.yudao.module.erp.controller.admin.customer;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.customer.vo.*;
import cn.iocoder.yudao.module.erp.service.customer.CustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 客户基本信息")
@RestController
@RequestMapping("/erp/customer")
@Validated
public class CustomerController {

    @Resource
    private CustomerService customerService;
    

    @Operation(summary = "校验客户", description = "创建客户前的校验", responses = {
            @ApiResponse(responseCode = "1_030_624_003", description = "商业伙伴中存在与该公司名称相似的公司，是否从商业伙伴中导入"),
            @ApiResponse(responseCode = "1_030_624_001", description = "该公司已在商业伙伴中存在，请从商业伙伴中导入"),
            @ApiResponse(responseCode = "1_030_624_002", description = "该公司已在商业伙伴中存在，请从商业伙伴中导入")
    })
    @PreAuthorize("@ss.hasPermission('erp:customer:create')")
    @PostMapping("/validate")
    public CommonResult<Boolean> validateBeforeCreate(@Valid @RequestBody CreateCustomerDTO createDTO) {
        return success(customerService.validateBeforeCreate(createDTO));
    }

    @PostMapping
    @Operation(summary = "创建客户基本信息")
    @PreAuthorize("@ss.hasPermission('erp:customer:create')")
    public CommonResult<Long> create(@Valid @RequestBody CreateCustomerDTO createCustomerDTO) {
        return success(customerService.create(createCustomerDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新客户基本信息")
    @PreAuthorize("@ss.hasPermission('erp:customer:update')")
    public CommonResult<Boolean> update(@PathVariable("id") Long id, @Valid @RequestBody UpdateCustomerDTO updateCustomerDTO) {
        customerService.update(id, updateCustomerDTO);
        return success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除客户基本信息")
    @PreAuthorize("@ss.hasPermission('erp:customer:delete')")
    public CommonResult<Boolean> delete(@PathVariable("id") Long id) {
        customerService.delete(id);
        return success(true);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获得客户基本信息")
    @PreAuthorize("@ss.hasPermission('erp:customer:query')")
    public CommonResult<CustomerVO> get(@PathVariable("id") Long id) {
        return success(customerService.get(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获得客户基本信息分页")
    @PreAuthorize("@ss.hasPermission('erp:customer:query')")
    public CommonResult<PageResult<CustomerVO>> page(@Valid SearchCustomerDTO searchCustomerDTO) {
        return success(customerService.page(searchCustomerDTO));
    }


    @GetMapping("/simple-list")
    @Operation(summary = "获取简化版客户基本信息列表")
//    @PreAuthorize("@ss.hasAnyPermissions('erp:customer:query','erp:sales-order:create','erp:sales-order:update','erp:picking-list:create','erp:picking-list:update','erp:shipping:create','erp:shipping:update','erp:produce-inbound:create','erp:produce-inbound:update')")
    public CommonResult<List<SimpleCustomerVO>> simpleList() {
        return success(customerService.simpleList());
    }

    //停用
    @PutMapping("/{id}/disable")
    @Operation(summary = "停用客户")
    @PreAuthorize("@ss.hasPermission('erp:customer:status')")
    public CommonResult<Boolean> disable(@PathVariable("id") Long id) {
        customerService.disable(id);
        return success(true);
    }

    //启用
    @PutMapping("/{id}/enable")
    @Operation(summary = "启用客户")
    @PreAuthorize("@ss.hasPermission('erp:customer:status')")
    public CommonResult<Boolean> enable(@PathVariable("id") Long id) {
        customerService.enable(id);
        return success(true);
    }


    @PostMapping("/{id}/receive-address")
    @Operation(summary = "新建客户收货地址")
    @PreAuthorize("@ss.hasPermission('erp:customer:receive-address:create')")
    public CommonResult<CustomerReceiveAddressVO> createReceiveAddress(@PathVariable("id") Long id,
                                                                       @Valid @RequestBody CreateCustomerReceiveAddressDTO createCustomerReceiveAddressDTO) {
        return success(customerService.createReceiveAddress(id, createCustomerReceiveAddressDTO));
    }

    @PutMapping("/{id}/receive-address/{receiveAddressId}")
    @Operation(summary = "更新客户收货地址")
    @PreAuthorize("@ss.hasPermission('erp:customer:receive-address:update')")
    public CommonResult<CustomerReceiveAddressVO> updateReceiveAddress(@PathVariable("id") Long id,
                                                                       @PathVariable("receiveAddressId") Long receiveAddressId,
                                                                       @Valid @RequestBody UpdateCustomerReceiveAddressDTO updateCustomerReceiveAddressDTO) {
        return success(customerService.updateReceiveAddress(id, receiveAddressId, updateCustomerReceiveAddressDTO));
    }

    @DeleteMapping("/{id}/receive-address/{receiveAddressId}")
    @Operation(summary = "删除客户收货地址")
    @PreAuthorize("@ss.hasPermission('erp:customer:receive-address:delete')")
    public CommonResult<Boolean> deleteReceiveAddress(@PathVariable("id") Long id,
                                                      @PathVariable("receiveAddressId") Long receiveAddressId) {
        customerService.deleteReceiveAddress(id, receiveAddressId);
        return success(true);
    }

    @GetMapping("/{id}/receive-address/{receiveAddressId}")
    @Operation(summary = "获得客户收货地址")
    @PreAuthorize("@ss.hasPermission('erp:customer:receive-address:query')")
    public CommonResult<CustomerReceiveAddressVO> getReceiveAddress(@PathVariable("id") Long id,
                                                                    @PathVariable("receiveAddressId") Long receiveAddressId) {
        return success(customerService.getReceiveAddress(id, receiveAddressId));
    }

    @GetMapping("/{id}/receive-address")
    @Operation(summary = "获得客户收货地址列表")
    @PreAuthorize("@ss.hasAnyPermissions('erp:customer:receive-address:query','erp:shipping:create','erp:shipping:update')")
    public CommonResult<List<CustomerReceiveAddressVO>> listReceiveAddress(@PathVariable("id") Long id) {
        return success(customerService.listReceiveAddress(id));
    }

    @PostMapping("{id}/invoice-info")
    @Operation(summary = "新建客户发票信息")
    @PreAuthorize("@ss.hasPermission('erp:customer:invoice-info:create')")
    public CommonResult<CustomerInvoiceInfoVO> createInvoiceInfo(@PathVariable("id") Long id,
                                                                 @Valid @RequestBody CreateCustomerInvoiceInfoDTO createCustomerInvoiceInfoDTO) {
        return success(customerService.createInvoiceInfo(id, createCustomerInvoiceInfoDTO));
    }

    @PutMapping("{id}/invoice-info/{invoiceInfoId}")
    @Operation(summary = "更新客户发票信息")
    @PreAuthorize("@ss.hasPermission('erp:customer:invoice-info:update')")
    public CommonResult<CustomerInvoiceInfoVO> updateInvoiceInfo(@PathVariable("id") Long id,
                                                                 @PathVariable("invoiceInfoId") Long invoiceInfoId,
                                                                 @Valid @RequestBody UpdateCustomerInvoiceInfoDTO updateCustomerInvoiceInfoDTO) {
        return success(customerService.updateInvoiceInfo(id, invoiceInfoId, updateCustomerInvoiceInfoDTO));
    }

    @DeleteMapping("{id}/invoice-info/{invoiceInfoId}")
    @Operation(summary = "删除客户发票信息")
    @PreAuthorize("@ss.hasPermission('erp:customer:invoice-info:delete')")
    public CommonResult<Boolean> deleteInvoiceInfo(@PathVariable("id") Long id,
                                                   @PathVariable("invoiceInfoId") Long invoiceInfoId) {
        customerService.deleteInvoiceInfo(id, invoiceInfoId);
        return success(true);
    }

    @GetMapping("{id}/invoice-info/{invoiceInfoId}")
    @Operation(summary = "获得客户发票信息")
    @PreAuthorize("@ss.hasPermission('erp:customer:invoice-info:query')")
    public CommonResult<CustomerInvoiceInfoVO> getInvoiceInfo(@PathVariable("id") Long id,
                                                              @PathVariable("invoiceInfoId") Long invoiceInfoId) {
        return success(customerService.getInvoiceInfo(id, invoiceInfoId));
    }

    @GetMapping("{id}/invoice-info")
    @Operation(summary = "获得客户发票信息列表")
    @PreAuthorize("@ss.hasPermission('erp:customer:invoice-info:query')")
    public CommonResult<List<CustomerInvoiceInfoVO>> listInvoiceInfo(@PathVariable("id") Long id) {
        return success(customerService.listInvoiceInfo(id));
    }

    @GetMapping("/{id}/simple-receive-address")
    @Operation(summary = "获得客户收货地址列表")
    @PreAuthorize("@ss.hasAnyPermissions('erp:shipping:create','erp:shipping:update')")
    public CommonResult<List<CustomerReceiveAddressVO>> simpleListReceiveAddress(@PathVariable("id") Long id) {
        return success(customerService.listReceiveAddress(id));
    }
}