package cn.iocoder.yudao.module.erp.controller.admin.customer.vo;

import cn.iocoder.yudao.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Schema(description = "管理后台 - 客户基本信息更新 DTO")
@Data
public class UpdateCustomerDTO extends CreateCustomerDTO {

    /**
     * 客户类别
     */
    @Schema(description = "客户类别", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Size(max = 50, message = "客户类别长度不能超过 50 个字")
    private String category;

    /**
     * 地区
     */
    @Schema(description = "地区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long area;

    /**
     * 详细地址
     */
    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Size(max = 50, message = "详细地址长度不能超过 50 个字")
    private String address;
    /**
     * 联系人
     */
    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Size(max = 50, message = "联系人长度不能超过 50 个字")
    private String contact;
    /**
     * 联系电话
     */
    @Schema(description = "联系电话", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Mobile(message = "手机号码格式不正确")
    private String contactPhone;

    /**
     * 业务联系人
     */
    @Schema(description = "业务联系人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Size(max = 50, message = "业务联系人长度不能超过 50 个字")
    private String businessContact;

    /**
     * 业务联系电话
     */
    @Schema(description = "业务联系电话", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Mobile(message = "手机号码格式不正确")
    private String businessContactPhone;
    /**
     * 业务联系人邮箱
     */
    @Schema(description = "业务联系人邮箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Email(message = "邮箱格式不正确")
    private String businessContactEmail;
    /**
     * 财务联系人
     */
    @Schema(description = "财务联系人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Size(max = 50, message = "财务联系人长度不能超过 50 个字")
    private String financeContact;
    /**
     * 财务联系人电话
     */
    @Schema(description = "财务联系人电话", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Mobile(message = "手机号码格式不正确")
    private String financeContactPhone;
    /**
     * 财务联系人邮箱
     */
    @Schema(description = "财务联系人邮箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Email(message = "邮箱格式不正确")
    private String financeContactEmail;

    @Schema(description = "客户类型(客户渠道)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String channel;
}
