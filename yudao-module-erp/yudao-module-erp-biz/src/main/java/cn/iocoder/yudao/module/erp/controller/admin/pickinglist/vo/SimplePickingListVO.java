package cn.iocoder.yudao.module.erp.controller.admin.pickinglist.vo;

import cn.iocoder.yudao.module.erp.controller.admin.common.Attachment;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 备货通知单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SimplePickingListVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "14418")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "备货单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("备货单号")
    private String code;

    @Schema(description = "交货日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交货日期")
    private LocalDateTime deliveryDate;

    @Schema(description = "`业务员`   ", requiredMode = Schema.RequiredMode.REQUIRED, example = "15877")
    @ExcelProperty("`业务员`   ")
    private Long businessUserId;

    @Schema(description = "业务员", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String businessUserName;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("业务类型")
    private String businessType;

    @Schema(description = "业务状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("业务状态")
    private Integer businessStatus;

    @Schema(description = "单据状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("单据状态")
    private Integer documentStatus;

    @Schema(description = "客户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3504")
    @ExcelProperty("客户id")
    private Long customerId;

    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("客户名称")
    private String customerName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "质量要求")
    private String qualityRule;

    @Schema(description = "包装要求")
    private String packagingRule;

    @Schema(description = "运输要求")
    private String transportRule;

    @Schema(description = "交付资料")
    private String deliverables;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "附件")
    private List<Attachment> attachment;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;


}