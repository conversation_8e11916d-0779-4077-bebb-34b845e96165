package cn.iocoder.yudao.module.erp.dal.mysql.packaging;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.iocoder.yudao.module.erp.controller.admin.packaging.vo.SearchPackagingSpecDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.packaging.PackagingSpecDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.packaging.PackagingTypeDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.packaging.view.SpecWithPackagingType;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface PackagingSpecMapper extends BaseMapperX<PackagingSpecDO> {
    default PageResult<PackagingSpecDO> selectPage(Long packagingTypeId, SearchPackagingSpecDTO searchPackagingSpecDTO) {
        return selectPage(searchPackagingSpecDTO, new LambdaQueryWrapperX<PackagingSpecDO>()
                .eq(PackagingSpecDO::getPackagingTypeId, packagingTypeId)
                .likeIfPresent(PackagingSpecDO::getName, searchPackagingSpecDTO.getName())
                .likeIfPresent(PackagingSpecDO::getCode, searchPackagingSpecDTO.getCode())
                .eqIfPresent(PackagingSpecDO::getStatus, searchPackagingSpecDTO.getStatus())
                .orderByAsc(PackagingSpecDO::getSort)
                .orderByDesc(PackagingSpecDO::getCreateTime));
    }

    default PackagingSpecDO selectByName(String name) {
        return selectOne(PackagingSpecDO::getName, name);
    }

    default PackagingSpecDO selectByCode(String code) {
        return selectOne(PackagingSpecDO::getCode, code);
    }

    default List<PackagingSpecDO> selectListByPackagingTypeIdsAndStatus(List<Long> packagingTypeIds, Integer status) {
        return selectList(new LambdaQueryWrapperX<PackagingSpecDO>()
                .in(PackagingSpecDO::getPackagingTypeId, packagingTypeIds)
                .eqIfPresent(PackagingSpecDO::getStatus, status)
                .orderByAsc(PackagingSpecDO::getSort)
                .orderByDesc(PackagingSpecDO::getCreateTime));
    }

    default List<SpecWithPackagingType> listSpecWithPackagingType(List<Long> packagingSpecIds) {
        //查询PackagingSpecDO left  join PackagingType
        MPJLambdaWrapper<PackagingSpecDO> wrapper = new MPJLambdaWrapperX<PackagingSpecDO>()
                .selectAll(PackagingSpecDO.class)
                .selectAssociation(PackagingTypeDO.class, SpecWithPackagingType::getPackagingType)
                .leftJoin(PackagingTypeDO.class, PackagingTypeDO::getId, PackagingSpecDO::getPackagingTypeId)
                .in(PackagingSpecDO::getId, packagingSpecIds)
                .orderByDesc(PackagingSpecDO::getId);

        return selectJoinList(SpecWithPackagingType.class, wrapper);


    }
}
