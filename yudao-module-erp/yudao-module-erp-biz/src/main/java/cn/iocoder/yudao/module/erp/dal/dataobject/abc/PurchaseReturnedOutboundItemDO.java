package cn.iocoder.yudao.module.erp.dal.dataobject.abc;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 采购退货出库单明细 DO
 *
 * <AUTHOR>
 */
@TableName("erp_purchase_returned_outbound_item")
@KeySequence("erp_purchase_returned_outbound_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseReturnedOutboundItemDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 出入库单ID
     */
    private Long returnedOutboundId;
    /**
     * 物料ID
     */
    private Long materialId;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 规格型号
     */
    private String materialSpec;
    /**
     * 计量单位
     */
    private String materialUom;
    /**
     * 出库数量
     */
    private BigDecimal outQuantity;
    /**
     * 批次号
     */
    private String batchNumber;
    /**
     * 唯一码
     */
    private String uniqueCode;
    /**
     * 库位ID
     */
    private Long warehousePositionId;
    /**
     * 库位名称
     */
    private String warehousePositionName;
    /**
     * 备注
     */
    private String remark;

}