package cn.iocoder.yudao.module.erp.controller.admin.purchase.request.vo;

import cn.iocoder.yudao.module.erp.controller.admin.common.Attachment;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 更新采购申请单 Request VO")
@Data
public class UpdatePurchaseRequestDTO {

    @Schema(description = "申请日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "申请日期不能为空")
    private LocalDateTime requestDate;

    @Schema(description = "申请部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "申请部门ID不能为空")
    private Long requestDeptId;

    @Schema(description = "申请人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "申请人ID不能为空")
    private Long requestUserId;

    @Schema(description = "单据类型", example = "purchase_request")
    private String documentType;

    @Schema(description = "物料类型", example = "general")
    private String materialType;

    @Schema(description = "备注", example = "紧急采购申请")
    private String remark;

    @Schema(description = "附件URL列表")
    private List<Attachment> attachments;

    @Schema(description = "采购申请单明细列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "采购申请单明细不能为空")
    @Valid
    private List<PurchaseRequestItemDTO> items;

}
