package cn.iocoder.yudao.module.erp.controller.admin.warehouse.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SearchWarehouseListDTO {

    /**
     * 库存操作类型
     */
    @Schema(description = "库存操作类型,in 入库；out 出库")
    private String stockOperationType;

    /**
     * 入库类型
     */
    @Schema(description = "入库类型")
    private String stockOperationInType;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;
}
