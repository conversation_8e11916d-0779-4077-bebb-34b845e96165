package cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 采购入库单明细 DO
 *
 * <AUTHOR>
 */
@TableName("erp_purchase_inbound_item")
@KeySequence("erp_purchase_inbound_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseInboundItemDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 入库单ID
     */
    private Long inboundId;

    // ==================== 物料信息 ====================

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料规格型号
     */
    private String materialSpec;

    /**
     * 物料主计量单位
     */
    private String materialUom;

    // ==================== 数量信息 ====================

    /**
     * 入库数量
     */
    private BigDecimal inboundQuantity;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 唯一码
     */
    private String uniqueCode;

    // ==================== 库位信息 ====================

    /**
     * 库位ID
     */
    private Long warehousePositionId;

    /**
     * 库位名称
     */
    private String warehousePositionName;

    // ==================== 来源单据信息（采购入库专用） ====================

    /**
     * 采购到货单ID
     */
    private Long purchaseArrivalId;

    /**
     * 采购到货单号
     */
    private String purchaseArrivalCode;

    /**
     * 采购到货单明细ID
     */
    private Long purchaseArrivalItemId;

    // ==================== 通用字段 ====================

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 供应商Id
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 未退数量
     */
    private BigDecimal unreturnedQuantity;

    /**
     * 已退货数量
     */
    private BigDecimal returnedQuantity;

    /**
     * 可退货数量
     */
    private BigDecimal returnableQuantity;

}
