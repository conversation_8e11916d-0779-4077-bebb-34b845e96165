package cn.iocoder.yudao.module.erp.controller.admin.purchase.returned;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.returned.vo.*;
import cn.iocoder.yudao.module.erp.service.purchase.returned.PurchaseReturnedService;
import db.migration.PermissionName;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 采购退货通知单")
@RestController
@RequestMapping("/erp/purchase-returned")
@Validated
public class PurchaseReturnedController {

    @Resource
    private PurchaseReturnedService purchaseReturnedService;

    @PostMapping
    @Operation(summary = "创建采购退货通知单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned:create')")
    @PermissionName(name = "创建采购退货通知单", sort = 1)
    public CommonResult<Long> create(@Valid @RequestBody CreatePurchaseReturnedDTO createDTO) {
        return success(purchaseReturnedService.create(createDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新采购退货通知单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned:update')")
    @PermissionName(name = "更新采购退货通知单", sort = 2)
    public CommonResult<Boolean> update(@PathVariable("id") Long id, @Valid @RequestBody UpdatePurchaseReturnedDTO updateDTO) {
        purchaseReturnedService.update(id, updateDTO);
        return success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除采购退货通知单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned:delete')")
    @PermissionName(name = "删除采购退货通知单", sort = 3)
    public CommonResult<Boolean> delete(@PathVariable("id") Long id) {
        purchaseReturnedService.delete(id);
        return success(true);
    }

    @GetMapping("/{id}")
    @Operation(summary = "查看采购退货通知单详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned:view')")
    @PermissionName(name = "查看采购退货通知单详情", sort = 4)
    public CommonResult<PurchaseReturnedVO> get(@PathVariable("id") Long id) {
        return success(purchaseReturnedService.get(id));
    }

    @GetMapping("/page")
    @Operation(summary = "查询采购退货通知单分页")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned:query')")
    @PermissionName(name = "查询采购退货通知单-单据模式", sort = 8)
    public CommonResult<PageResult<SimplePurchaseReturnedVO>> page(@Valid SearchPurchaseReturnedDTO searchDTO) {
        PageResult<SimplePurchaseReturnedVO> pageResult = purchaseReturnedService.page(searchDTO);
        return success(pageResult);
    }

    // ==================== 文档操作接口 ====================

    @PutMapping("/close/{id}")
    @Operation(summary = "关闭采购退货通知单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned:close')")
    @PermissionName(name = "关闭/取消关闭采购退货通知单", sort = 6)
    public CommonResult<Boolean> close(@PathVariable("id") Long id) {
        purchaseReturnedService.close(id);
        return success(true);
    }

    @PutMapping("/cancel-close/{id}")
    @Operation(summary = "取消关闭采购退货通知单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned:close')")
    public CommonResult<Boolean> cancelClose(@PathVariable("id") Long id) {
        purchaseReturnedService.cancelClose(id);
        return success(true);
    }

    @PutMapping("/suspend/{id}")
    @Operation(summary = "挂起采购退货通知单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned:suspend')")
    @PermissionName(name = "挂起/取消挂起采购退货通知单", sort = 7)
    public CommonResult<Boolean> suspend(@PathVariable("id") Long id) {
        purchaseReturnedService.suspend(id);
        return success(true);
    }

    @PutMapping("/cancel-suspend/{id}")
    @Operation(summary = "取消挂起采购退货通知单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned:suspend')")
    public CommonResult<Boolean> cancelSuspend(@PathVariable("id") Long id) {
        purchaseReturnedService.cancelSuspend(id);
        return success(true);
    }

    @PutMapping("/approve/{id}")
    @Operation(summary = "审核采购退货通知单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned:approve')")
    @PermissionName(name = "审核/取消审核采购退货通知单", sort = 5)
    public CommonResult<Boolean> approve(@PathVariable("id") Long id) {
        purchaseReturnedService.approve(id);
        return success(true);
    }

    @PutMapping("/cancel-approve/{id}")
    @Operation(summary = "取消审核采购退货通知单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned:approve')")
    public CommonResult<Boolean> cancelApprove(@PathVariable("id") Long id) {
        purchaseReturnedService.cancelApprove(id);
        return success(true);
    }

    @GetMapping("/item/page")
    @Operation(summary = "查询采购退货通知单明细分页")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned:item:query')")
    @PermissionName(name = "查询采购退货通知单-明细模式", sort = 8)
    public CommonResult<PageResult<ItemWithPurchaseReturnedVO>> pageItem(@Valid SearchItemPageDTO searchDTO) {
        PageResult<ItemWithPurchaseReturnedVO> pageResult = purchaseReturnedService.pageItem(searchDTO);
        return success(pageResult);
    }


}