package cn.iocoder.yudao.module.erp.controller.admin.purchase.order.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 采购订单 VO")
@Data
@ExcelIgnoreUnannotated
public class PurchaseOrderItemVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14526")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "采购订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32635")
    @ExcelProperty("采购订单ID")
    private Long purchaseOrderId;

    @Schema(description = "采购申请单ID", example = "26373")
    @ExcelProperty("采购申请单ID")
    private Long purchaseRequestId;

    @Schema(description = "采购申请单明细ID", example = "26374")
    @ExcelProperty("采购申请单明细ID")
    private Long purchaseRequestItemId;

    @Schema(description = "采购申请单编号")
    @ExcelProperty("采购申请单编号")
    private String purchaseRequestCode;

    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21604")
    @ExcelProperty("物料ID")
    private Long materialId;

    @Schema(description = "物料编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物料编码")
    private String materialCode;

    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("物料名称")
    private String materialName;

    @Schema(description = "物料规格型号")
    @ExcelProperty("物料规格型号")
    private String materialSpec;

    @Schema(description = "物料主计量单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物料主计量单位")
    private String materialUom;

    @Schema(description = "采购数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购数量")
    private BigDecimal purchaseQuantity;

    @Schema(description = "含税单价", requiredMode = Schema.RequiredMode.REQUIRED, example = "10636")
    @ExcelProperty("含税单价")
    private BigDecimal taxIncludedPrice;

    @Schema(description = "税额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("税额")
    private BigDecimal taxAmount;

    @Schema(description = "税价合计", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("税价合计")
    private BigDecimal taxIncludedTotal;

    @Schema(description = "税率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("税率")
    private BigDecimal taxRate;

    @Schema(description = "需求日期")
    @ExcelProperty("需求日期")
    private LocalDateTime demandDate;

    @Schema(description = "计划到货日期")
    @ExcelProperty("计划到货日期")
    private LocalDateTime plannedArrivalDate;

    @Schema(description = "已到货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("已到货数量")
    private BigDecimal arrivedQuantity;

    @Schema(description = "未到货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("未到货数量")
    private BigDecimal unarrivedQuantity;

}