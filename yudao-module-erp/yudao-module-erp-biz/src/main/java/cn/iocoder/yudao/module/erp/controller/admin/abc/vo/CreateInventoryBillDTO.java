package cn.iocoder.yudao.module.erp.controller.admin.abc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 出入库清单新增 DTO")
@Data
public class CreateInventoryBillDTO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20645")
    private Long id;

    @Schema(description = "单据号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单据号不能为空")
    private String code;

    @Schema(description = "in入库 out出库", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "in入库 out出库不能为空")
    private String direction;

    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "单据类型不能为空")
    private String documentType;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @Schema(description = "0编制中 1审核通过 2回退", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "0编制中 1审核通过 2回退不能为空")
    private Integer documentStatus;

    @Schema(description = "业务状态：0关闭 1正常 2挂起", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业务状态：0关闭 1正常 2挂起不能为空")
    private Integer businessStatus;

    @Schema(description = "单据日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单据日期不能为空")
    private LocalDateTime documentDate;

    @Schema(description = "仓库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23679")
    @NotNull(message = "仓库ID不能为空")
    private Long warehouseId;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "仓库名称不能为空")
    private String warehouseName;

    @Schema(description = "供应商ID", example = "31190")
    private Long supplierId;

    @Schema(description = "供应商名称", example = "李四")
    private String supplierName;

    @Schema(description = "客户ID", example = "28181")
    private Long customerId;

    @Schema(description = "客户名称", example = "王五")
    private String customerName;

    @Schema(description = "业务部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3240")
    @NotNull(message = "业务部门ID不能为空")
    private Long businessDepartmentId;

    @Schema(description = "业务部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "业务部门名称不能为空")
    private String businessDepartmentName;

    @Schema(description = "业务员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16529")
    @NotNull(message = "业务员ID不能为空")
    private Long businessUserId;

    @Schema(description = "业务员姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "业务员姓名不能为空")
    private String businessUserName;

    @Schema(description = "来源单据类型", example = "2")
    private String sourceType;

    @Schema(description = "来源单据ID", example = "27528")
    private Long sourceId;

    @Schema(description = "来源单据号")
    private String sourceCode;

    @Schema(description = "物料类型:采购单的物料类型（业务字典）", example = "1")
    private String materialType;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;


    @Schema(description = "出入库清单明细列表")
    private List<InventoryBillItemDTO> items;
}