package cn.iocoder.yudao.module.erp.service.purchase.request;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.BusinessStatusEnum;
import cn.iocoder.yudao.module.erp.constant.BusinessTypeEnum;
import cn.iocoder.yudao.module.erp.constant.DocumentStatusEnum;
import cn.iocoder.yudao.module.erp.constant.LockTypeEnum;
import cn.iocoder.yudao.module.erp.controller.admin.base.vo.material.MaterialMasterDataVO;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.request.vo.*;
import cn.iocoder.yudao.module.erp.convert.purchase.request.PurchaseRequestConvert;
import cn.iocoder.yudao.module.erp.convert.purchase.request.PurchaseRequestItemConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.request.PurchaseRequestDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.request.PurchaseRequestItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.request.view.ItemWithPurchaseRequest;
import cn.iocoder.yudao.module.erp.dal.mysql.purchase.request.PurchaseRequestItemMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.purchase.request.PurchaseRequestMapper;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataService;
import cn.iocoder.yudao.module.erp.service.common.AbstractDocumentAuditingService;
import cn.iocoder.yudao.module.erp.service.common.DocumentServiceUtil;
import cn.iocoder.yudao.module.erp.service.purchase.materialconfig.PurchaseMaterialConfigService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.*;

/**
 * 采购申请单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PurchaseRequestServiceImpl extends AbstractDocumentAuditingService<PurchaseRequestItemDO> implements PurchaseRequestService {

    @Resource
    private PurchaseRequestMapper purchaseRequestMapper;
    @Resource
    private PurchaseRequestItemMapper purchaseRequestItemMapper;
    @Resource
    private PurchaseRequestConvert purchaseRequestConvert;
    @Resource
    private PurchaseRequestItemConvert purchaseRequestItemConvert;

    @Resource
    private DocumentServiceUtil documentServiceUtil;

    @Resource
    @Lazy
    private MaterialMasterDataService materialMasterDataService;

    @Resource
    @Lazy
    private PurchaseMaterialConfigService purchaseMaterialConfigService;

    @Resource
    @Lazy
    private DeptApi deptApi;

    @Resource
    @Lazy
    private AdminUserApi adminUserApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(CreatePurchaseRequestDTO createDTO) {
        // 校验基础数据
        commonValidate(purchaseRequestConvert.toDO(createDTO), purchaseRequestItemConvert.toDO(createDTO.getItems()));

        // 插入采购申请单
        PurchaseRequestDO purchaseRequest = purchaseRequestConvert.toDO(createDTO);
        documentServiceUtil.fillDocumentInfoWhenCreate(purchaseRequest, BusinessTypeEnum.PURCHASE_REQUEST);

        // 填充部门和用户名称
        fillDeptAndUserNames(purchaseRequest);

        purchaseRequestMapper.insert(purchaseRequest);

        // 插入采购申请单明细
        List<PurchaseRequestItemDO> items = purchaseRequestItemConvert.toDO(createDTO.getItems());
        items.forEach(item -> {
            item.setPurchaseRequestId(purchaseRequest.getId());
            // 填充物料信息
            fillMaterialInfo(item);
        });
        purchaseRequestItemMapper.insertBatch(items);

        return purchaseRequest.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Long id, UpdatePurchaseRequestDTO updateDTO) {
        // 校验采购申请单存在
        validatePurchaseRequestExists(id);
        // 校验采购申请单可编辑
        validatePurchaseRequestEditable(id);
        // 校验基础数据
        commonValidate(purchaseRequestConvert.toDO(updateDTO), purchaseRequestItemConvert.toDO(updateDTO.getItems()));

        // 更新采购申请单
        PurchaseRequestDO updateObj = purchaseRequestConvert.toDO(updateDTO);
        updateObj.setId(id);

        // 填充部门和用户名称
        fillDeptAndUserNames(updateObj);

        purchaseRequestMapper.updateById(updateObj);

        saveItems(id, purchaseRequestItemConvert.toDO(updateDTO.getItems()));
    }

    private void saveItems(Long purchaseRequestId, List<PurchaseRequestItemDO> purchaseRequestItemDOList) {
        List<PurchaseRequestItemDO> existItems = purchaseRequestItemMapper.selectListByPurchaseRequestId(purchaseRequestId);
        // Split into new and updated items
        Map<Boolean, List<PurchaseRequestItemDO>> partitionedItems = purchaseRequestItemDOList.stream()
                .collect(Collectors.partitioningBy(i -> i.getId() == null));
        List<PurchaseRequestItemDO> newItems = partitionedItems.get(true);
        List<PurchaseRequestItemDO> updateItems = partitionedItems.get(false);
        // Handle deletions
        Set<Long> updatedItemIds = updateItems.stream()
                .map(PurchaseRequestItemDO::getId)
                .collect(Collectors.toSet());
        List<Long> deleteItemIds = existItems.stream()
                .map(PurchaseRequestItemDO::getId)
                .filter(id -> !updatedItemIds.contains(id))
                .toList();

        if (CollUtil.isNotEmpty(deleteItemIds)) {
            purchaseRequestItemMapper.deleteByIds(deleteItemIds);
        }
        if (CollUtil.isNotEmpty(newItems)) {
            createItems(purchaseRequestId, newItems);
        }
        if (CollUtil.isNotEmpty(updateItems)) {
            updateItems.forEach(this::fillMaterialInfo);
            purchaseRequestItemMapper.updateBatch(updateItems);
        }
    }

    private void createItems(Long purchaseRequestId, List<PurchaseRequestItemDO> newItems) {
        newItems.forEach(item -> {
            item.setPurchaseRequestId(purchaseRequestId);
            fillMaterialInfo(item);
        });
        purchaseRequestItemMapper.insertBatch(newItems);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        // 校验采购申请单存在
        validatePurchaseRequestExists(id);
        // 校验采购申请单可删除
        validatePurchaseRequestDeletable(id);

        // 删除采购申请单
        purchaseRequestMapper.deleteById(id);
        // 删除采购申请单明细
        purchaseRequestItemMapper.deleteByPurchaseRequestId(id);
    }

    @Override
    public PurchaseRequestVO get(Long id) {
        PurchaseRequestDO purchaseRequest = purchaseRequestMapper.selectById(id);
        if (Objects.isNull(purchaseRequest)) {
            throw exception(PURCHASE_REQUEST_NOT_EXISTS, id);
        }
        PurchaseRequestVO purchaseRequestVO = purchaseRequestConvert.toVO(purchaseRequest);
        List<PurchaseRequestItemVO> items = purchaseRequestItemConvert.toVO(
                purchaseRequestItemMapper.selectListByPurchaseRequestId(id)
        );
        purchaseRequestVO.setItems(items);

        return purchaseRequestVO;
    }

    @Override
    public PageResult<SimplePurchaseRequestVO> page(SearchPurchaseRequestDTO searchDTO) {
        PageResult<PurchaseRequestDO> pageResult = purchaseRequestMapper.selectPage(searchDTO);
        return new PageResult<>(purchaseRequestConvert.toSimpleVO(pageResult.getList()), pageResult.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void close(Long id) {
        // 校验采购申请单存在
        validatePurchaseRequestExists(id);

        // 更新业务状态为关闭
        purchaseRequestMapper.updateBusinessStatusById(BusinessStatusEnum.CLOSED.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelClose(Long id) {
        // 校验采购申请单存在
        validatePurchaseRequestExists(id);
        // 校验采购申请单是关闭状态
        validatePurchaseRequestBusinessStatus(id, BusinessStatusEnum.CLOSED.getStatus());

        // 更新业务状态为正常
        purchaseRequestMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspend(Long id) {
        // 校验采购申请单存在
        validatePurchaseRequestExists(id);

        // 更新业务状态为挂起
        purchaseRequestMapper.updateBusinessStatusById(BusinessStatusEnum.SUSPENDED.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSuspend(Long id) {
        // 校验采购申请单存在
        validatePurchaseRequestExists(id);
        // 校验采购申请单是挂起状态
        validatePurchaseRequestBusinessStatus(id, BusinessStatusEnum.SUSPENDED.getStatus());

        // 更新业务状态为正常
        purchaseRequestMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }


    @Override
    public PageResult<ItemWithPurchaseRequestVO> pageItem(SearchItemPageDTO searchDTO) {
        PageResult<ItemWithPurchaseRequest> page = purchaseRequestItemMapper.selectItemPage(searchDTO);
        return new PageResult<>(purchaseRequestItemConvert.toItemWithPurchaseRequestVO(page.getList()), page.getTotal());
    }

    @Override
    protected void doApprove(Long id, List<PurchaseRequestItemDO> purchaseRequestItemDOList) {
        // 初始化采购数量
        initPurchaseQuantity(id);
    }

    @Override
    protected void doCancelApprove(Long id, List<PurchaseRequestItemDO> purchaseRequestItemDOList) {
    }

    @Override
    protected void validateBeforeApprove(Long id) {
        commonValidate(id);
        validatePurchaseRequestApprovable(id);
    }

    @Override
    protected void validateBeforeCancelApprove(Long id) {
        commonValidate(id);
        validatePurchaseRequestCancelApprovable(id);
    }

    @Override
    protected List<PurchaseRequestItemDO> getDocumentItemsWhenAuditing(Long id) {
        List<PurchaseRequestItemDO> items = purchaseRequestItemMapper.selectListByPurchaseRequestId(id);
        // 校验明细不能为空
        if (CollUtil.isEmpty(items)) {
            throw exception(PURCHASE_REQUEST_ITEMS_EMPTY);
        }
        return items;
    }

    @Override
    protected List<Long> getNeedLockedIds(List<PurchaseRequestItemDO> purchaseRequestItemDOList) {
        return purchaseRequestItemDOList.stream().map(PurchaseRequestItemDO::getId).collect(Collectors.toList());
    }

    @Override
    protected void updateDocumentApproveStatus(Long id, Integer status) {
        purchaseRequestMapper.updateApproveStatusById(status, id);
    }

    @Override
    @PostConstruct
    protected void initLockTypeEnum() {
        this.documentLockType = LockTypeEnum.PURCHASE_REQUEST;
        this.itemLockType = LockTypeEnum.PURCHASE_REQUEST_ITEM;
    }

    @Override
    public void initPurchaseQuantity(Long purchaseRequestId) {
        purchaseRequestItemMapper.initPurchaseQuantity(purchaseRequestId);
    }

    @Override
    public void validatePurchaseQuantity(Long requestItemId, BigDecimal totalPurchaseQuantity) {
        PurchaseRequestItemDO purchaseRequestItem = validatePurchaseRequestItemExists(requestItemId);
        if (totalPurchaseQuantity.compareTo(purchaseRequestItem.getUnfulfilledQuantity()) > 0) {
            throw exception(PURCHASE_ORDER_ITEM_QUANTITY_EXCEED_REQUEST, totalPurchaseQuantity, purchaseRequestItem.getUnfulfilledQuantity());
        }
    }

    @Override
    public void updatePurchaseQuantityWhenPurchaseApprove(Long requestItemId, BigDecimal purchaseQuantity) {
        purchaseRequestItemMapper.updatePurchaseQuantityWhenPurchaseApprove(requestItemId, purchaseQuantity);
    }

    @Override
    public void updatePurchaseQuantityWhenPurchaseCancelApprove(Long requestItemId, BigDecimal purchaseQuantity) {
        purchaseRequestItemMapper.updatePurchaseQuantityWhenPurchaseCancelApprove(requestItemId, purchaseQuantity);
    }

    @Override
    public void validateExistsAndApprove(Set<Long> purchaseRequestIds) {
        List<PurchaseRequestDO> purchaseRequestDOS = purchaseRequestMapper.selectByIds(purchaseRequestIds);
        Set<Long> existIds = purchaseRequestDOS.stream()
                .map(PurchaseRequestDO::getId)
                .collect(Collectors.toSet());
        List<Long> notExistIds = purchaseRequestIds.stream()
                .filter(id -> !existIds.contains(id))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(notExistIds)) {
            throw exception(PURCHASE_REQUEST_NOT_EXISTS, notExistIds);
        }
        for (PurchaseRequestDO purchaseRequestDO : purchaseRequestDOS) {
            if (!DocumentStatusEnum.APPROVE.getStatus().equals(purchaseRequestDO.getDocumentStatus())) {
                throw exception(PURCHASE_REQUEST_NOT_APPROVE, purchaseRequestDO.getCode());
            }
        }

    }

    private PurchaseRequestItemDO validatePurchaseRequestItemExists(Long requestItemId) {
        PurchaseRequestItemDO purchaseRequestItem = purchaseRequestItemMapper.selectById(requestItemId);
        if (purchaseRequestItem == null) {
            throw exception(PURCHASE_REQUEST_ITEM_NOT_EXISTS, requestItemId);
        }
        return purchaseRequestItem;
    }
    // ==================== 校验方法 ====================

    private PurchaseRequestDO validatePurchaseRequestExists(Long id) {
        PurchaseRequestDO purchaseRequest = purchaseRequestMapper.selectById(id);
        if (purchaseRequest == null) {
            throw exception(PURCHASE_REQUEST_NOT_EXISTS, id);
        }
        return purchaseRequest;
    }

    private void validatePurchaseRequestEditable(Long id) {
        PurchaseRequestDO purchaseRequest = purchaseRequestMapper.selectById(id);
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(purchaseRequest.getDocumentStatus()) &&
                !DocumentStatusEnum.ROLLBACK.getStatus().equals(purchaseRequest.getDocumentStatus())) {
            throw exception(PURCHASE_REQUEST_NOT_EDITABLE);
        }
    }

    private void validatePurchaseRequestDeletable(Long id) {
        PurchaseRequestDO purchaseRequest = purchaseRequestMapper.selectById(id);
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(purchaseRequest.getDocumentStatus()) &&
                !DocumentStatusEnum.ROLLBACK.getStatus().equals(purchaseRequest.getDocumentStatus())) {
            throw exception(PURCHASE_REQUEST_NOT_DELETABLE);
        }
    }

    private void validatePurchaseRequestApprovable(Long id) {
        PurchaseRequestDO purchaseRequest = purchaseRequestMapper.selectById(id);
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(purchaseRequest.getDocumentStatus()) &&
                !DocumentStatusEnum.ROLLBACK.getStatus().equals(purchaseRequest.getDocumentStatus())) {
            throw exception(PURCHASE_REQUEST_NOT_APPROVABLE);
        }
    }

    private void validatePurchaseRequestCancelApprovable(Long id) {
        PurchaseRequestDO purchaseRequest = purchaseRequestMapper.selectById(id);
        if (!DocumentStatusEnum.APPROVE.getStatus().equals(purchaseRequest.getDocumentStatus())) {
            throw exception(PURCHASE_REQUEST_NOT_CANCEL_APPROVABLE);
        }
    }

    private void validatePurchaseRequestBusinessStatus(Long id, Integer expectedStatus) {
        PurchaseRequestDO purchaseRequest = purchaseRequestMapper.selectById(id);
        if (!expectedStatus.equals(purchaseRequest.getBusinessStatus())) {
            throw exception(PURCHASE_REQUEST_BUSINESS_STATUS_NOT_MATCH);
        }
    }

    private void commonValidate(Long id) {
        PurchaseRequestDO purchaseRequestDO = validatePurchaseRequestExists(id);
        List<PurchaseRequestItemDO> purchaseRequestItemDOList = purchaseRequestItemMapper.selectListByPurchaseRequestId(id);
        commonValidate(purchaseRequestDO, purchaseRequestItemDOList);
    }

    private void commonValidate(PurchaseRequestDO purchaseRequestDO, List<PurchaseRequestItemDO> purchaseRequestItemDOList) {


        adminUserApi.validateUserAndDept(purchaseRequestDO.getRequestUserId(), purchaseRequestDO.getRequestDeptId()).checkError();

        // 校验明细中的物料
        documentServiceUtil.validateMaterialsFromItems(purchaseRequestItemDOList, PurchaseRequestItemDO::getMaterialId);
        
        // 校验明细数据完整性
        for (PurchaseRequestItemDO purchaseRequestItemDO : purchaseRequestItemDOList) {
            if (purchaseRequestItemDO.getMaterialId() == null || purchaseRequestItemDO.getRequestQuantity() == null ||
                    purchaseRequestItemDO.getRequestQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                throw exception(PURCHASE_REQUEST_ITEM_NO_QUANTITY, purchaseRequestItemDO.getId());
            }
            if (purchaseRequestItemDO.getRequiredDate() == null
                    || purchaseRequestItemDO.getRequiredDate().isBefore(purchaseRequestDO.getRequestDate())) {
                throw exception(PURCHASE_REQUEST_REQUIRED_DATE_INVALID);
            }

            if (purchaseRequestItemDO.getExpectedArrivalDate() != null
                    && purchaseRequestItemDO.getRequiredDate().isBefore(purchaseRequestItemDO.getExpectedArrivalDate())) {
                throw exception(PURCHASE_EXPECTED_ARRIVAL_DATE_INVALID);
            }

            this.purchaseMaterialConfigService.validateMaterialRequiredDate(
                    purchaseRequestItemDO.getMaterialId(),
                    purchaseRequestDO.getRequestDate(),
                    purchaseRequestItemDO.getRequiredDate()
            );
        }
    }

    // ==================== 辅助方法 ====================

    private void fillDeptAndUserNames(PurchaseRequestDO purchaseRequest) {
        // 填充部门名称
        if (purchaseRequest.getRequestDeptId() != null) {
            DeptRespDTO dept = deptApi.getDept(purchaseRequest.getRequestDeptId()).getCheckedData();
            if (dept != null) {
                purchaseRequest.setRequestDeptName(dept.getName());
            }
        }

        // 填充用户名称
        if (purchaseRequest.getRequestUserId() != null) {
            AdminUserRespDTO user = adminUserApi.getUser(purchaseRequest.getRequestUserId()).getCheckedData();
            if (user != null) {
                purchaseRequest.setRequestUserName(user.getNickname());
            }
        }
    }

    private void fillMaterialInfo(PurchaseRequestItemDO item) {
        if (item.getMaterialId() != null) {
            try {
                // 根据物料ID获取物料信息
                MaterialMasterDataVO material = materialMasterDataService.getMaterialMasterData(item.getMaterialId());
                if (material != null) {
                    item.setMaterialCode(material.getCode());
                    item.setMaterialName(material.getName());
                    item.setMaterialSpec(material.getSpecification());
                    item.setMaterialUom(material.getUnitOfMeasure());
                }
            } catch (Exception e) {
                log.warn("填充物料信息失败，物料ID: {}, 错误: {}", item.getMaterialId(), e.getMessage());
                // 填充失败时保持原有值，不抛出异常影响主流程
            }
        }
    }

}
