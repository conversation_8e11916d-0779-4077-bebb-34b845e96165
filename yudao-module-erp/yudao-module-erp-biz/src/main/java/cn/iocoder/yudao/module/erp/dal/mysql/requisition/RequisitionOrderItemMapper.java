package cn.iocoder.yudao.module.erp.dal.mysql.requisition;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.iocoder.yudao.module.erp.controller.admin.requisition.vo.SearchItemPageDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.requisition.RequisitionOrderDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.requisition.RequisitionOrderItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.requisition.view.ItemWithRequisitionOrder;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 领料单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RequisitionOrderItemMapper extends BaseMapperX<RequisitionOrderItemDO> {

    default List<RequisitionOrderItemDO> selectListByRequisitionOrderId(Long requisitionOrderId) {
        return selectList(RequisitionOrderItemDO::getRequisitionOrderId, requisitionOrderId);
    }

    default int deleteByRequisitionOrderId(Long requisitionOrderId) {
        return delete(RequisitionOrderItemDO::getRequisitionOrderId, requisitionOrderId);
    }

    default PageResult<ItemWithRequisitionOrder> selectItemPage(SearchItemPageDTO searchDTO) {
        MPJLambdaWrapper<RequisitionOrderItemDO> queryWrapper = new MPJLambdaWrapperX<RequisitionOrderItemDO>()
                .selectAll(RequisitionOrderItemDO.class)
                // 物料相关条件
                .likeIfPresent(RequisitionOrderItemDO::getMaterialCode, searchDTO.getMaterialCode())
                .likeIfPresent(RequisitionOrderItemDO::getMaterialName, searchDTO.getMaterialName())
                // 关联领料单表
                .selectAssociation(RequisitionOrderDO.class, ItemWithRequisitionOrder::getRequisitionOrder)
                .leftJoin(RequisitionOrderDO.class, RequisitionOrderDO::getId, RequisitionOrderItemDO::getRequisitionOrderId)
                .eqIfExists(RequisitionOrderDO::getId, searchDTO.getRequisitionOrderId());

        // 领料单表相关查询条件
        if (StrUtil.isNotEmpty(searchDTO.getCode())) {
            queryWrapper.like(RequisitionOrderDO::getCode, searchDTO.getCode());
        }

        if (Objects.nonNull(searchDTO.getBusinessUserId())) {
            queryWrapper.eq(RequisitionOrderDO::getBusinessUserId, searchDTO.getBusinessUserId());
        }
        if (Objects.nonNull(searchDTO.getDocumentStatus())) {
            queryWrapper.eq(RequisitionOrderDO::getDocumentStatus, searchDTO.getDocumentStatus());
        }

        if (Objects.nonNull(searchDTO.getWarehouseId())) {
            queryWrapper.eq(RequisitionOrderDO::getWarehouseId, searchDTO.getWarehouseId());
        }

        // 领料日期范围查询
        if (Objects.nonNull(searchDTO.getRequisitionDate()) && searchDTO.getRequisitionDate().length > 0) {
            LocalDate start = searchDTO.getRequisitionDate()[0];
            LocalDate end = searchDTO.getRequisitionDate().length > 1 ? searchDTO.getRequisitionDate()[1] : null;
            if (start != null && end != null) {
                queryWrapper.between(RequisitionOrderDO::getRequisitionDate, start, end);
            } else if (start != null) {
                queryWrapper.ge(RequisitionOrderDO::getRequisitionDate, start);
            } else if (end != null) {
                queryWrapper.le(RequisitionOrderDO::getRequisitionDate, end);
            }
        }

        // 排序
        queryWrapper.orderByDesc(RequisitionOrderItemDO::getId);

        return selectJoinPage(searchDTO, ItemWithRequisitionOrder.class, queryWrapper);
    }

    default Long countByMaterialId(Long materialId) {
        return selectCount(RequisitionOrderItemDO::getMaterialId, materialId);
    }

}
