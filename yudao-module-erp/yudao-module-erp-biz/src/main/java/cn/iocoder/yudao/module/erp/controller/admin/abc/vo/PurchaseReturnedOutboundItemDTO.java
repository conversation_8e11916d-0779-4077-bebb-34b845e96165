package cn.iocoder.yudao.module.erp.controller.admin.abc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 采购退货出库单明细 DTO")
@Data
public class PurchaseReturnedOutboundItemDTO {
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15640")
    private Long id;

    @Schema(description = "出入库单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32103")
    @NotNull(message = "出入库单ID不能为空")
    private Long returnedOutboundId;

    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24255")
    @NotNull(message = "物料ID不能为空")
    private Long materialId;

    @Schema(description = "物料编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物料编码不能为空")
    private String materialCode;

    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "物料名称不能为空")
    private String materialName;

    @Schema(description = "规格型号")
    private String materialSpec;

    @Schema(description = "计量单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "计量单位不能为空")
    private String materialUom;

    @Schema(description = "出库数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "出库数量不能为空")
    private BigDecimal outQuantity;

    @Schema(description = "批次号")
    private String batchNumber;

    @Schema(description = "唯一码")
    private String uniqueCode;

    @Schema(description = "库位ID", example = "1798")
    private Long warehousePositionId;

    @Schema(description = "库位名称", example = "赵六")
    private String warehousePositionName;

    @Schema(description = "备注", example = "随便")
    private String remark;


}