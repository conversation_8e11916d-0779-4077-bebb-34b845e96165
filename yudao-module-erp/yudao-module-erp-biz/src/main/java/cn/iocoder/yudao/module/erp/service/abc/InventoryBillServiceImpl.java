package cn.iocoder.yudao.module.erp.service.abc;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.BusinessStatusEnum;
import cn.iocoder.yudao.module.erp.constant.BusinessTypeEnum;
import cn.iocoder.yudao.module.erp.constant.DocumentStatusEnum;
import cn.iocoder.yudao.module.erp.controller.admin.abc.vo.*;
import cn.iocoder.yudao.module.erp.convert.inventory.InventoryBillConvert;
import cn.iocoder.yudao.module.erp.convert.inventory.InventoryBillItemConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryBillDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryBillItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.view.ItemWithInventoryBill;
import cn.iocoder.yudao.module.erp.dal.mysql.inventory.InventoryBillItemMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.inventory.InventoryBillMapper;
import cn.iocoder.yudao.module.erp.service.common.AbstractDocumentAuditingService;
import cn.iocoder.yudao.module.erp.service.common.DocumentServiceUtil;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 出入库清单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryBillServiceImpl extends AbstractDocumentAuditingService<InventoryBillItemDO>
        implements InventoryBillService {

    @Resource
    private InventoryBillMapper inventoryBillMapper;

    @Resource
    private InventoryBillItemMapper inventoryBillItemMapper;

    @Resource
    private InventoryBillItemConvert inventoryBillItemConvert;

    @Resource
    private DocumentServiceUtil documentServiceUtil;
    @Resource
    private InventoryBillConvert inventoryBillConvert;
    @Resource
    private InventoryBillItemMapper inventoryBillItemMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(CreateInventoryBillDTO createDTO) {
        // 1. 转换DO
        InventoryBillDO inventoryBill = inventoryBillConvert.toDO(createDTO);
        List<InventoryBillItemDO> items = inventoryBillItemConvert.toDO(createDTO.getItems());

        // 2. 验证业务逻辑
        commonValidate(inventoryBill, items);

        // 3. 插入主表
        documentServiceUtil.fillDocumentInfoWhenCreate(inventoryBill, BusinessTypeEnum.INVENTORYBILL);
        fillDeptName(inventoryBill);
        inventoryBillMapper.insert(inventoryBill);

        // 4. 插入明细表
        doCreateInventoryBillItems(inventoryBill.getId(), items);

        return inventoryBill.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Long id, UpdateInventoryBillDTO updateDTO) {
        // 1. 验证存在
        InventoryBillDO inventoryBill = validateInventoryBillExists(id);
        // 2. 验证可编辑
        validateInventoryBillEditable(inventoryBill);

        // 3. 转换DO
        InventoryBillDO updateObj = inventoryBillConvert.toDO(updateDTO);
        List<InventoryBillItemDO> updateItems = inventoryBillItemConvert.toDO(updateDTO.getItems());

        // 4. 验证业务逻辑
        commonValidate(updateObj, updateItems);

        // 5. 更新主表
        updateObj.setId(id);
        fillDeptName(updateObj);
        inventoryBillMapper.updateById(updateObj);

        // 6. 处理明细表 - 分为新增、更新、删除三类
        Map<Boolean, List<InventoryBillItemDO>> partitionedItems = updateItems.stream()
                .collect(Collectors.partitioningBy(item -> item.getId() == null));

        // 获取现有明细项
        List<InventoryBillItemDO> existItemList = inventoryBillItemMapper.selectListByInventoryBillId(id);

        // 处理新增项
        List<InventoryBillItemDO> newItemList = partitionedItems.get(true);
        if (CollUtil.isNotEmpty(newItemList)) {
            doCreateInventoryBillItems(id, newItemList);
        }

        // 处理更新项
        List<InventoryBillItemDO> updateItemList = partitionedItems.get(false);
        if (CollUtil.isNotEmpty(updateItemList)) {
            doUpdateInventoryBillItemList(id, updateItemList);
        }

        // 处理删除项：找出存在于旧列表但不在更新列表中的项
        Set<Long> updateItemIds = updateItemList.stream()
                .map(InventoryBillItemDO::getId)
                .collect(Collectors.toSet());
        Set<Long> deleteItemIds = existItemList.stream()
                .map(InventoryBillItemDO::getId)
                .filter(itemId -> !updateItemIds.contains(itemId))
                .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(deleteItemIds)) {
            inventoryBillItemMapper.deleteByIds(deleteItemIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        // 验证存在
        InventoryBillDO inventoryBill = validateInventoryBillExists(id);
        // 验证可删除
        validateInventoryBillEditable(inventoryBill);

        // 删除主表
        inventoryBillMapper.deleteById(id);
        // 删除明细表
        inventoryBillItemMapper.deleteByInventoryBillId(id);
    }

    private InventoryBillDO validateInventoryBillExists(Long id) {
        InventoryBillDO inventoryBill = inventoryBillMapper.selectById(id);
        if (inventoryBill == null) {
            throw exception(INVENTORYBILL_NOT_EXISTS);
        }
        return inventoryBill;
    }

    @Override
    public InventoryBillVO get(Long id) {
        InventoryBillDO inventoryBill = validateInventoryBillExists(id);
        List<InventoryBillItemDO> items = inventoryBillItemMapper.selectListByInventoryBillId(id);

        InventoryBillVO result = inventoryBillConvert.toVO(inventoryBill);
        result.setItems(inventoryBillItemConvert.toVO(items));
        return result;
    }

    @Override
    public PageResult<SimpleInventoryBillVO> page(SearchInventoryBillDTO searchDTO) {
        PageResult<InventoryBillDO> page = inventoryBillMapper.selectPage(searchDTO);
        List<InventoryBillDO> list = page.getList();
        List<SimpleInventoryBillVO> listVO = inventoryBillConvert.toSimpleVO(list);
        return new PageResult<>(listVO, page.getTotal());
    }

    // ==================== 子表（通用库存单据明细） ====================

    @Override
    public PageResult<InventoryBillItemDO> getInventoryBillItemPage(PageParam pageReqVO, Long billId) {
        return inventoryBillItemMapper.selectPage(pageReqVO, billId);
    }

    @Override
    public Long createInventoryBillItem(InventoryBillItemDO inventoryBillItem) {
        inventoryBillItemMapper.insert(inventoryBillItem);
        return inventoryBillItem.getId();
    }

    @Override
    public void updateInventoryBillItem(InventoryBillItemDO inventoryBillItem) {
        // 校验存在
        InventoryBillItemDO existingInventoryBillItem = validateInventoryBillItemExists(inventoryBillItem.getId());
        // 更新
        inventoryBillItem.setUpdater(null).setUpdateTime(null); // 解决更新情况下：updateTime 不更新
        inventoryBillItemMapper.updateById(inventoryBillItem);
    }

    @Override
    public void deleteInventoryBillItem(Long id) {
        // 校验存在
        InventoryBillItemDO inventoryBillItem = validateInventoryBillItemExists(id);
        // 删除
        inventoryBillItemMapper.deleteById(id);
    }

    @Override
    public InventoryBillItemDO getInventoryBillItem(Long id) {
        return inventoryBillItemMapper.selectById(id);
    }

    private InventoryBillItemDO validateInventoryBillItemExists(Long id) {
        InventoryBillItemDO inventoryBillItem = inventoryBillItemMapper.selectById(id);
        if (inventoryBillItem == null) {
            throw exception(INVENTORYBILLITEM_NOT_EXISTS);
        }
        return inventoryBillItem;
    }

    private void deleteInventoryBillItemByBillId(Long billId) {
        inventoryBillItemMapper.deleteByBillId(billId);
    }


    // ==================== 文档操作方法实现 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void close(Long id) {
        InventoryBillDO inventoryBill = validateInventoryBillExists(id);
        validateBusinessStatus(inventoryBill, BusinessStatusEnum.NORMAL.getStatus());
        inventoryBillMapper.updateBusinessStatusById(BusinessStatusEnum.CLOSED.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelClose(Long id) {
        InventoryBillDO inventoryBill = validateInventoryBillExists(id);
        validateBusinessStatus(inventoryBill, BusinessStatusEnum.CLOSED.getStatus());
        inventoryBillMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspend(Long id) {
        InventoryBillDO inventoryBill = validateInventoryBillExists(id);
        validateBusinessStatus(inventoryBill, BusinessStatusEnum.NORMAL.getStatus());
        inventoryBillMapper.updateBusinessStatusById(BusinessStatusEnum.SUSPENDED.getStatus(), id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSuspend(Long id) {
        InventoryBillDO inventoryBill = validateInventoryBillExists(id);
        validateBusinessStatus(inventoryBill, BusinessStatusEnum.SUSPENDED.getStatus());
        inventoryBillMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    @Override
    public void approve(Long id) {
        doApprove(id);
    }

    @Override
    public void cancelApprove(Long id) {
        doCancelApprove(id);
    }

    @Override
    protected void doApprove(Long id) {
        // 校验存在
        InventoryBillDO inventoryBill = validateInventoryBillExists(id);
        // 校验状态
        if (!Objects.equals(inventoryBill.getDocumentStatus(), DocumentStatusEnum.DRAFT.getStatus())) {
            throw exception(INVENTORYBILL_APPROVE_FAIL_STATUS_NOT_DRAFT);
        }

        // 更新状态
        inventoryBillMapper.updateDocumentStatusById(id, DocumentStatusEnum.APPROVED.getStatus());

        // 生成库存明细
        List<InventoryBillItemDO> items = inventoryBillItemMapper.selectListByInventoryBillId(id);
        generateInventoryDetails(inventoryBill, items);
    }

    @Override
    protected void doCancelApprove(Long id) {
        // 校验存在
        InventoryBillDO inventoryBill = validateInventoryBillExists(id);
        // 校验状态
        if (!Objects.equals(inventoryBill.getDocumentStatus(), DocumentStatusEnum.APPROVED.getStatus())) {
            throw exception(INVENTORYBILL_CANCEL_APPROVE_FAIL_STATUS_NOT_APPROVED);
        }

        // 更新状态
        inventoryBillMapper.updateDocumentStatusById(id, DocumentStatusEnum.DRAFT.getStatus());

        // 删除库存明细
        deleteInventoryDetails(inventoryBill);
    }

    private void generateInventoryDetails(InventoryBillDO inventoryBill, List<InventoryBillItemDO> items) {
        // TODO: 实现库存明细生成逻辑
        // 参考 PurchaseArrivalServiceImpl.generateInventoryDetails 方法
    }

    private void deleteInventoryDetails(InventoryBillDO inventoryBill) {
        // TODO: 实现库存明细删除逻辑
        // 参考 PurchaseArrivalServiceImpl.deleteInventoryDetails 方法
    }

    @Override
    public PageResult<ItemWithInventoryBillVO> pageItem(SearchItemPageDTO searchDTO) {
        PageResult<ItemWithInventoryBill> pageResult = inventoryBillItemMapper.selectItemPage(searchDTO);
        return inventoryBillItemConvert.convertPage(pageResult);
    }

    @PostConstruct
    public void init() {
        // TODO: 初始化逻辑，如设置单据编号前缀等
        // 参考 PurchaseArrivalServiceImpl.init() 方法
    }


    private void validateInventoryBillEditable(InventoryBillDO inventoryBill) {
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(inventoryBill.getDocumentStatus()) &&
                !DocumentStatusEnum.ROLLBACK.getStatus().equals(inventoryBill.getDocumentStatus())) {
            throw exception(INVENTORYBILL_NOT_EDITABLE);
        }
    }

    private void validateInventoryBillApprovable(InventoryBillDO inventoryBill) {
        if (!DocumentStatusEnum.DRAFT.getStatus().equals(inventoryBill.getDocumentStatus()) &&
                !DocumentStatusEnum.ROLLBACK.getStatus().equals(inventoryBill.getDocumentStatus())) {
            throw exception(INVENTORYBILL_NOT_APPROVABLE);
        }
    }

    private void validateInventoryBillCancelApprovable(InventoryBillDO inventoryBill) {
        if (!DocumentStatusEnum.APPROVE.getStatus().equals(inventoryBill.getDocumentStatus())) {
            throw exception(INVENTORYBILL_NOT_CANCEL_APPROVABLE);
        }
    }

    private void validateBusinessStatus(InventoryBillDO inventoryBill, Integer status) {
        if (!Objects.equals(inventoryBill.getBusinessStatus(), status)) {
            throw exception(INVENTORYBILL_BUSINESS_STATUS_NOT_SUPPORT_CHANGE, inventoryBill.getCode());
        }
    }

    private void commonValidate(InventoryBillDO inventoryBill, List<InventoryBillItemDO> items) {
        // TODO: 实现业务逻辑验证
        // 参考 PurchaseArrivalServiceImpl.commonValidate() 方法
        // 1. 验证业务员和部门的隶属关系
        // 2. 验证仓库
        // 3. 验证数量
        // 4. 验证唯一码等
    }

    private void fillDeptName(InventoryBillDO inventoryBill) {
        // TODO: 实现部门名称填充逻辑
        // 参考 PurchaseArrivalServiceImpl.fillDeptName() 方法
    }

    private void doCreateInventoryBillItems(Long inventoryBillId, List<InventoryBillItemDO> items) {
        // TODO: 实现明细创建逻辑
        // 参考 PurchaseArrivalServiceImpl.createPurchaseArrivalItems() 方法
        items.forEach(item -> {
            item.setInventoryBillId(inventoryBillId);
        });
        inventoryBillItemMapper.insertBatch(items);
    }

    private void doUpdateInventoryBillItemList(Long inventoryBillId, List<InventoryBillItemDO> items) {
        // TODO: 实现明细更新逻辑
        // 参考 PurchaseArrivalServiceImpl.updatePurchaseArrivalItemList() 方法
        items.forEach(item -> {
            item.setInventoryBillId(inventoryBillId);
        });
        inventoryBillItemMapper.updateBatch(items);
    }

}