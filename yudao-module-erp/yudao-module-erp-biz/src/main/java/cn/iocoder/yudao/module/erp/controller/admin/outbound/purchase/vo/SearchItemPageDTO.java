package cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo;

import cn.iocoder.yudao.module.erp.controller.admin.common.vo.ItemSearchDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 采购退货出库单明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchItemPageDTO extends ItemSearchDTO {

    @Schema(description = "采购退货出库单ID", example = "1024")
    private Long purchaseReturnedOutboundId;


    @Schema(description = "仓库ID", example = "1")
    private Long warehouseId;

    @Schema(description = "批次号", example = "B001")
    private String batchNo;

    @Schema(description = "唯一码", example = "U001")
    private String uniqueCode;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] createTime;

}
