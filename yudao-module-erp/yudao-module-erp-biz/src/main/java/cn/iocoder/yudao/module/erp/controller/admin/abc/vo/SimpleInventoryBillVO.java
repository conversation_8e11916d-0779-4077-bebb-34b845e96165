package cn.iocoder.yudao.module.erp.controller.admin.abc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 出入库清单 Simple Response VO")
@Data
@ExcelIgnoreUnannotated
public class SimpleInventoryBillVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20645")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "单据号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单据号")
    private String code;

    @Schema(description = "in入库 out出库", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("in入库 out出库")
    private String direction;

    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("单据类型")
    private String documentType;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("业务类型")
    private String businessType;

    @Schema(description = "0编制中 1审核通过 2回退", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("0编制中 1审核通过 2回退")
    private Integer documentStatus;

    @Schema(description = "业务状态：0关闭 1正常 2挂起", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("业务状态：0关闭 1正常 2挂起")
    private Integer businessStatus;

    @Schema(description = "单据日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单据日期")
    private LocalDateTime documentDate;

    @Schema(description = "仓库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23679")
    @ExcelProperty("仓库ID")
    private Long warehouseId;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("仓库名称")
    private String warehouseName;

    @Schema(description = "供应商ID", example = "31190")
    @ExcelProperty("供应商ID")
    private Long supplierId;

    @Schema(description = "供应商名称", example = "李四")
    @ExcelProperty("供应商名称")
    private String supplierName;

    @Schema(description = "客户ID", example = "28181")
    @ExcelProperty("客户ID")
    private Long customerId;

    @Schema(description = "客户名称", example = "王五")
    @ExcelProperty("客户名称")
    private String customerName;

    @Schema(description = "业务部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3240")
    @ExcelProperty("业务部门ID")
    private Long businessDepartmentId;

    @Schema(description = "业务部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("业务部门名称")
    private String businessDepartmentName;

    @Schema(description = "业务员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16529")
    @ExcelProperty("业务员ID")
    private Long businessUserId;

    @Schema(description = "业务员姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("业务员姓名")
    private String businessUserName;

    @Schema(description = "来源单据类型", example = "2")
    @ExcelProperty("来源单据类型")
    private String sourceType;

    @Schema(description = "来源单据ID", example = "27528")
    @ExcelProperty("来源单据ID")
    private Long sourceId;

    @Schema(description = "来源单据号")
    @ExcelProperty("来源单据号")
    private String sourceCode;

    @Schema(description = "物料类型:采购单的物料类型（业务字典）", example = "1")
    @ExcelProperty("物料类型:采购单的物料类型（业务字典）")
    private String materialType;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
