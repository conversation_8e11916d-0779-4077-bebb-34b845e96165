package cn.iocoder.yudao.module.erp.convert.inbound.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.inbound.purchase.vo.*;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.PurchaseInboundDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.view.PurchaseInboundItemWithInboundDO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PurchaseInboundConvert {

    PurchaseInboundDO toDO(CreatePurchaseInboundDTO createDTO);

    PurchaseInboundDO toDO(UpdatePurchaseInboundDTO updateDTO);

    PurchaseInboundVO toVO(PurchaseInboundDO inboundDO);

    List<PurchaseInboundVO> toVO(List<PurchaseInboundDO> inboundDOList);

    List<SimplePurchaseInboundVO> toSimpleVO(List<PurchaseInboundDO> list);

    SimplePurchaseInboundVO toSimpleVO(PurchaseInboundDO inboundDO);

    PageResult<SimplePurchaseInboundVO> toSimpleVOPage(PageResult<PurchaseInboundDO> page);

    ItemWithPurchaseInboundVO toItemWithPurchaseInboundVO(PurchaseInboundItemWithInboundDO itemWithInbound);

    List<ItemWithPurchaseInboundVO> toItemWithPurchaseInboundVO(List<PurchaseInboundItemWithInboundDO> itemWithInboundList);

    PageResult<ItemWithPurchaseInboundVO> toItemWithPurchaseInboundVOPage(PageResult<PurchaseInboundItemWithInboundDO> page);

}
