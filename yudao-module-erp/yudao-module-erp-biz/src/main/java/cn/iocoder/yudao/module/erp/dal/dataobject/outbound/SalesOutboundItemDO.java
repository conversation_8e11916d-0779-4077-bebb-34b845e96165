package cn.iocoder.yudao.module.erp.dal.dataobject.outbound;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销售出库单-出库明细 DO
 *
 * <AUTHOR>
 */
@TableName("erp_sales_outbound_item")
@KeySequence("erp_sales_outbound_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesOutboundItemDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 销售出库单ID
     */
    private Long salesOutboundId;
    /**
     * 关联发货明细ID
     */
    private Long salesOutboundShipmentId;

    /**
     * 销售订单ID
     */
    private Long salesOrderId;

    /**
     * 销售订单号
     */
    private String salesOrderCode;

    /**
     * 销售订单-客户订单号
     */
    private String salesCustomerOrderCode;

    /**
     * 物料id
     */
    private Long materialId;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料规格型号
     */
    private String materialSpec;
    /**
     * 物料主计量单位
     */
    private String materialUom;
    /**
     * 库存明细ID
     */
    private Long inventoryDetailId;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 包装类型id
     */
    private Long packagingTypeId;
    /**
     * 包装类型名称
     */
    private String packagingTypeName;
    /**
     * 包装规格id
     */
    private Long packagingSpecId;
    /**
     * 包装规格名称
     */
    private String packagingSpecName;
    /**
     * 挂牌车号
     */
    private String plateNumber;
    /**
     * 包装物所属公司id，从customer中获取
     */
    private Long packagingCompanyId;
    /**
     * 包装物所属公司名称
     */
    private String packagingCompanyName;
    /**
     * 充装日期
     */
    private LocalDateTime fillingDate;
    /**
     * 毛重
     */
    private BigDecimal grossWeight;
    /**
     * 空重
     */
    private BigDecimal tareWeight;
    /**
     * 充装人id
     */
    private Long fillingUserId;
    /**
     * 库存数量
     */
    private BigDecimal inventoryQuantity;
    /**
     * 发货数量-来源于erp_sales_outbound_shipment的shippingQuantity
     */
    private BigDecimal shippingQuantity;
    /**
     * 未出库数量
     */
    private BigDecimal unshippedQuantity;
    /**
     * 出库数量-本次出库数量
     */
    private BigDecimal outQuantity;
    /**
     * 仓库id
     */
    private Long warehouseId;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 仓库库位id
     */
    private Long warehousePositionId;
    /**
     * 仓库库位名称
     */
    private String warehousePositionName;
    /**
     * 备注
     */
    private String remark;

    /**
     * 包装物编码
     */
    private String packagingCode;

    /**
     * 发货单ID
     */
    private Long shippingId;

    /**
     * 发货通知单code
     */
    private String shippingCode;

    /**
     * 发货单明细ID
     */
    private Long shippingItemId;

    /**
     * 退货数量
     */
    private BigDecimal returnedQuantity;

    /**
     * 未退货数量
     */
    private BigDecimal unreturnedQuantity;

    /**
     * 包装归属
     */
    private String packagingOwner;

}