package cn.iocoder.yudao.module.erp.convert.abc;

import cn.iocoder.yudao.module.erp.controller.admin.abc.vo.*;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.PurchaseReturnedOutboundItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.view.ItemWithPurchaseReturnedOutbound;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface  PurchaseReturnedOutboundItemConvert {

    PurchaseReturnedOutboundItemDO toDO(PurchaseReturnedOutboundItemDTO purchasereturnedoutbounditemDTO);

    PurchaseReturnedOutboundItemVO toVO(PurchaseReturnedOutboundItemDO purchasereturnedoutbounditemDO);

    List<PurchaseReturnedOutboundItemVO> toVO(List<PurchaseReturnedOutboundItemDO> purchasereturnedoutbounditemDOList);

    List<PurchaseReturnedOutboundItemDO> toDO(List<PurchaseReturnedOutboundItemDTO> purchasereturnedoutbounditemDTOList);

    /**
     * 转换分页结果
     */
    default PageResult<ItemWithPurchaseReturnedOutboundVO> convertPage(PageResult<ItemWithPurchaseReturnedOutbound> pageResult) {
        return BeanUtils.toBean(pageResult, ItemWithPurchaseReturnedOutboundVO.class);
    }
}