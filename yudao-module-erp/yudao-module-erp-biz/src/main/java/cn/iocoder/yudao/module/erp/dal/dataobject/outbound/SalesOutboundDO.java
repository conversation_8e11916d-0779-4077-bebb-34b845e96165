package cn.iocoder.yudao.module.erp.dal.dataobject.outbound;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.DocumentDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

/**
 * 销售出库单 DO
 *
 * <AUTHOR>
 */
@TableName("erp_sales_outbound")
@KeySequence("erp_sales_outbound_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SalesOutboundDO extends DocumentDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 客户ID
     */
    private Long customerId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 出库日期
     */
    private LocalDate outboundDate;
    /**
     * 发货通知单
     */
    private Long shippingId;
    /**
     * 发货单号
     */
    private String shippingCode;
    /**
     * 业务部门
     */
    private Long businessDepartment;
    /**
     * 业务员
     */
    private Long businessUserId;

    /**
     * 备注
     */
    private String remark;

}