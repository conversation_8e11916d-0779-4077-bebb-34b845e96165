package cn.iocoder.yudao.module.erp.dal.mysql.abc;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.InventoryBillItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.InventoryBillDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.abc.view.ItemWithInventoryBill;
import cn.iocoder.yudao.module.erp.controller.admin.abc.vo.SearchItemPageDTO;
import cn.iocoder.yudao.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import java.time.LocalDate;
import org.apache.ibatis.annotations.Mapper;

/**
 * 通用库存单据明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryBillItemMapper extends BaseMapperX<InventoryBillItemDO> {

    default PageResult<InventoryBillItemDO> selectPage(PageParam reqVO, Long billId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryBillItemDO>()
            .eq(InventoryBillItemDO::getBillId, billId)
            .orderByDesc(InventoryBillItemDO::getId));
    }

    default int deleteByBillId(Long billId) {
        return delete(InventoryBillItemDO::getBillId, billId);
    }

    /**
     * 根据主表ID查询明细列表
     *
     * @param billId 主表ID
     * @return 明细列表
     */
    default List<InventoryBillItemDO> selectListByBillId(Long billId) {
        return selectList(InventoryBillItemDO::getBillId, billId);
    }

    /**
     * 分页查询明细与主表组合数据
     *
     * @param searchDTO 查询条件
     * @return 分页结果
     */
    default PageResult<ItemWithInventoryBill> selectItemPage(SearchItemPageDTO searchDTO) {
        MPJLambdaWrapper<InventoryBillItemDO> wrapper = new MPJLambdaWrapperX<InventoryBillItemDO>()
                .selectAll(InventoryBillItemDO.class)
                .likeIfPresent(InventoryBillItemDO::getMaterialCode, searchDTO.getMaterialCode())
                .likeIfPresent(InventoryBillItemDO::getMaterialName, searchDTO.getMaterialName())
                .likeIfPresent(InventoryBillItemDO::getBatchNo, searchDTO.getBatchNo())
                .likeIfPresent(InventoryBillItemDO::getUniqueCode, searchDTO.getUniqueCode())

                .selectAssociation(InventoryBillDO.class, ItemWithInventoryBill::getInventoryBill)
                .leftJoin(InventoryBillDO.class, InventoryBillDO::getId, InventoryBillItemDO::getBillId)
                .likeIfExists(InventoryBillDO::getCode, searchDTO.getCode())
                .eqIfExists(InventoryBillDO::getWarehouseId, searchDTO.getWarehouseId())
                .eqIfExists(InventoryBillDO::getDocumentStatus, searchDTO.getDocumentStatus())

                .orderByDesc(InventoryBillDO::getCreateTime)
                .orderByDesc(InventoryBillDO::getId);

        if (searchDTO.getCreateTime() != null && searchDTO.getCreateTime().length > 0) {
            LocalDate start = searchDTO.getCreateTime()[0];
            LocalDate end = searchDTO.getCreateTime()[1];
            if (start != null && end != null) {
                wrapper.between(InventoryBillDO::getCreateTime, start.atStartOfDay(), end.plusDays(1).atStartOfDay());
            } else if (start != null) {
                wrapper.ge(InventoryBillDO::getCreateTime, start.atStartOfDay());
            } else if (end != null) {
                wrapper.le(InventoryBillDO::getCreateTime, end.plusDays(1).atStartOfDay());
            }
        }

        return selectJoinPage(searchDTO, ItemWithInventoryBill.class, wrapper);
    }

}
