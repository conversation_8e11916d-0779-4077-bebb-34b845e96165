package cn.iocoder.yudao.module.erp.service.purchase.materialconfig;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.materialconfig.vo.*;
import cn.iocoder.yudao.module.erp.convert.purchase.materialconfig.PurchaseMaterialConfigConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.materialconfig.PurchaseMaterialConfigDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.materialconfig.view.PurchaseMaterialConfigWithMaterialVO;
import cn.iocoder.yudao.module.erp.dal.mysql.purchase.materialconfig.PurchaseMaterialConfigMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.purchase.materialconfig.PurchaseMaterialMapper;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.PURCHASE_MATERIAL_CONFIG_NOT_EXISTS;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.PURCHASE_REQUEST_REQUIRED_DATE_INVALID;

/**
 * 采购物料配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PurchaseMaterialConfigServiceImpl implements PurchaseMaterialConfigService {

    @Resource
    private PurchaseMaterialConfigMapper purchaseMaterialConfigMapper;

    @Resource
    private PurchaseMaterialConfigConvert purchaseMaterialConfigConvert;

    @Resource
    private PurchaseMaterialMapper purchaseMaterialMapper;

    @Resource
    private MaterialMasterDataService materialMasterDataService;

    @Override
    public Long save(Long materialId, SavePurchaseMaterialConfigDTO saveDTO) {
        // 校验物料存在且启用
        validateMaterialExistsAndEnabled(materialId);

        // 查找是否已存在配置
        PurchaseMaterialConfigDO existConfig = purchaseMaterialConfigMapper.selectByMaterialId(materialId);

        if (existConfig == null) {
            // 创建新配置
            PurchaseMaterialConfigDO newConfig = purchaseMaterialConfigConvert.toDO(saveDTO);
            newConfig.setMaterialId(materialId);
            purchaseMaterialConfigMapper.insert(newConfig);
            return newConfig.getId();
        } else {
            // 更新现有配置
            PurchaseMaterialConfigDO updateObj = purchaseMaterialConfigConvert.toDO(saveDTO);
            updateObj.setId(existConfig.getId());
            updateObj.setMaterialId(materialId);
            purchaseMaterialConfigMapper.updateById(updateObj);
            return existConfig.getId();
        }
    }

    @Override
    public void deleteByMaterialId(Long materialId) {
        // 校验物料存在且启用
        validateMaterialExistsAndEnabled(materialId);

        // 查找配置
        PurchaseMaterialConfigDO config = purchaseMaterialConfigMapper.selectByMaterialId(materialId);
        if (config == null) {
            throw exception(PURCHASE_MATERIAL_CONFIG_NOT_EXISTS);
        }

        // 删除
        purchaseMaterialConfigMapper.deleteById(config.getId());
    }

    @Override
    public PurchaseMaterialConfigVO getByMaterialId(Long materialId) {
        PurchaseMaterialConfigDO config = purchaseMaterialConfigMapper.selectByMaterialId(materialId);
        if (config == null) {
            throw exception(PURCHASE_MATERIAL_CONFIG_NOT_EXISTS);
        }
        return purchaseMaterialConfigConvert.toVO(config);
    }


    @Override
    public PageResult<SimplePurchaseMaterialConfigVO> page(SearchPurchaseMaterialConfigDTO searchDTO) {
        PageResult<PurchaseMaterialConfigWithMaterialVO> pageResult =
                purchaseMaterialMapper.selectPage(searchDTO);
        return new PageResult<>(
                purchaseMaterialConfigConvert.toSimpleVO(pageResult.getList()),
                pageResult.getTotal()
        );
    }

    @Override
    public void validateMaterialRequiredDate(Long materialId, LocalDate requestDate, LocalDate requiredDate) {
        PurchaseMaterialConfigDO config = purchaseMaterialConfigMapper.selectByMaterialId(materialId);

        if (config != null && requiredDate.isBefore(requestDate.plusDays(config.getAverageCycleValue()))) {
            throw exception(PURCHASE_REQUEST_REQUIRED_DATE_INVALID);
        }
    }

    @Override
    public List<SimplePurchaseMaterialConfigVO> list(SearchSimplePurchaseMaterialConfigDTO searchDTO) {
        List<PurchaseMaterialConfigWithMaterialVO> list = purchaseMaterialMapper.selectList(searchDTO);
        return purchaseMaterialConfigConvert.toSimpleVO(list);
    }

    /**
     * 校验物料存在且启用
     */
    private void validateMaterialExistsAndEnabled(Long materialId) {
        materialMasterDataService.validateExistAndEnableByIds(Collections.singletonList(materialId));
    }
}
