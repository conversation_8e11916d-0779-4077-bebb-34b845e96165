package cn.iocoder.yudao.module.erp.controller.admin.requisition.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 领料单明细分页 Request DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchItemPageDTO extends PageParam {

    @Schema(description = "领料单ID", example = "1024")
    private Long requisitionOrderId;

    @Schema(description = "单据编号", example = "LL202507001")
    private String code;

    @Schema(description = "仓库ID", example = "1")
    private Long warehouseId;

    @Schema(description = "单据状态", example = "1")
    private Integer documentStatus;

    @Schema(description = "业务员ID", example = "1")
    private Long businessUserId;

    @Schema(description = "物料编码", example = "M001")
    private String materialCode;

    @Schema(description = "物料名称", example = "钢材")
    private String materialName;

    @Schema(description = "领料日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] requisitionDate;

}
