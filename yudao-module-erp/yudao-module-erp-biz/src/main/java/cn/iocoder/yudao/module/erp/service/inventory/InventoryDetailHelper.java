package cn.iocoder.yudao.module.erp.service.inventory;


import cn.iocoder.yudao.module.erp.constant.BusinessTypeEnum;
import cn.iocoder.yudao.module.erp.constant.DocumentTypeEnum;
import cn.iocoder.yudao.module.erp.constant.LockTypeEnum;
import cn.iocoder.yudao.module.erp.controller.admin.inventory.vo.CreateInventoryDetailDTO;
import cn.iocoder.yudao.module.erp.convert.inventory.InventoryDetailConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.produce.ProduceInboundLineDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.PurchaseInboundItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.returned.ReturnedInboundItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryChange;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryDetailDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.outbound.SalesOutboundItemDO;
import cn.iocoder.yudao.module.erp.service.common.RedissonLockUtil;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.HAS_OUTBOUND_NOT_SUPPORT_CANCEL_APPROVE;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.ITEM_LOCK_FAILED;

@Component
public class InventoryDetailHelper {

    @Resource
    private InventoryDetailConvert convert;

    @Resource
    private InventoryDetailService inventoryDetailService;

    @Resource
    @Lazy
    private RedissonLockUtil redissonLockUtil;

    public void processProduceInbound(Long warehouseId, DocumentTypeEnum documentTypeEnum, Long id, BusinessTypeEnum businessTypeEnum, List<ProduceInboundLineDO> lineDOList) {
        List<CreateInventoryDetailDTO> createInventoryDetailDTOS = convert.fromProduceInbound(lineDOList);
        batchCreateInventoryDetail(warehouseId, documentTypeEnum, id, businessTypeEnum, createInventoryDetailDTOS);
    }

    private void batchCreateInventoryDetail(Long warehouseId, DocumentTypeEnum documentTypeEnum, Long id, BusinessTypeEnum businessTypeEnum, List<CreateInventoryDetailDTO> createInventoryDetailDTOS) {
        for (CreateInventoryDetailDTO createInventoryDetailDTO : createInventoryDetailDTOS) {
            createInventoryDetailDTO.setWarehouseId(warehouseId);
            createInventoryDetailDTO.setDocumentType(documentTypeEnum.getType());
            createInventoryDetailDTO.setDocumentId(id);
            createInventoryDetailDTO.setBusinessType(businessTypeEnum.getType());
            createInventoryDetailDTO.setBusinessItemId(createInventoryDetailDTO.getBusinessItemId());
        }
        inventoryDetailService.batchCreate(createInventoryDetailDTOS);
    }

    public void processReturnedInbound(Long warehouseId, DocumentTypeEnum documentTypeEnum, Long id, BusinessTypeEnum businessTypeEnum, List<ReturnedInboundItemDO> items) {
        List<CreateInventoryDetailDTO> createInventoryDetailDTOS = convert.fromReturnedInbound(items);
        batchCreateInventoryDetail(warehouseId, documentTypeEnum, id, businessTypeEnum, createInventoryDetailDTOS);
    }

    public void processPurchaseInbound(Long warehouseId, DocumentTypeEnum documentTypeEnum, Long id, BusinessTypeEnum businessTypeEnum, List<PurchaseInboundItemDO> inboundItemDOList) {
        List<CreateInventoryDetailDTO> createInventoryDetailDTOS = convert.fromPurchaseInbound(inboundItemDOList);
        batchCreateInventoryDetail(warehouseId, documentTypeEnum, id, businessTypeEnum, createInventoryDetailDTOS);
    }


    public void processCancelApproveByDocumentAndBusinesses(DocumentTypeEnum documentTypeEnum, Long documentId, BusinessTypeEnum businessTypeEnum, List<Long> businessItemIds) {
        inventoryDetailService.deleteByDocumentAndBusinessTypeAndId(documentTypeEnum.getType(), documentId, businessTypeEnum.getType(), businessItemIds);
    }

    /**
     * 尝试对入库的记录做取消审核：会针对入库记录加锁
     * 会先查询入库单据产生的入库记录，如果入库记录没有被出库，则执行反审核操作：删除库存记录
     *
     * @param documentTypeEnum
     * @param documentId
     * @param businessTypeEnum
     * @param businessItemIds
     */
    public void tryCancelApproveByDocumentAndBusinesses(DocumentTypeEnum documentTypeEnum, Long documentId, BusinessTypeEnum businessTypeEnum, List<Long> businessItemIds) {
        List<InventoryDetailDO> existInventoryDetailList = inventoryDetailService.selectByDocumentTypeAndBusinessTypeAndId(documentTypeEnum.getType(), documentId, businessTypeEnum.getType(), businessItemIds);
        List<Long> inventoryIds = existInventoryDetailList.stream().map(InventoryDetailDO::getId).toList();
        boolean lockAcquired = redissonLockUtil.lock(LockTypeEnum.INVENTORY_DETAIL, inventoryIds);
        if (!lockAcquired) {
            throw exception(ITEM_LOCK_FAILED);
        }
        try {
            //验证inventoryIds 没有被出库
            if (inventoryDetailService.hasOutbound(inventoryIds)) {
                throw exception(HAS_OUTBOUND_NOT_SUPPORT_CANCEL_APPROVE);
            }
            processCancelApproveByDocumentAndBusinesses(documentTypeEnum, documentId, businessTypeEnum, businessItemIds);
        } finally {
            redissonLockUtil.unlock(LockTypeEnum.INVENTORY_DETAIL, inventoryIds);
        }
    }

    public void processOutbound(Long warehouseId, DocumentTypeEnum documentTypeEnum, Long id, BusinessTypeEnum businessTypeEnum, List<SalesOutboundItemDO> salesOutboundItemDOS) {
        List<CreateInventoryDetailDTO> createInventoryDetailDTOS = convert.fromSalesOutbound(salesOutboundItemDOS);
        batchCreateInventoryDetail(warehouseId, documentTypeEnum, id, businessTypeEnum, createInventoryDetailDTOS);
        //扣减库存
        List<InventoryChange> inventoryChangeList = new ArrayList<>(createInventoryDetailDTOS.size());
        createInventoryDetailDTOS.forEach(item -> {
            inventoryChangeList.add(new InventoryChange(item.getFromInventoryId(), item.getOutQuantity()));
        });
        inventoryDetailService.tryBatchDecreaseAvailableQuantity(inventoryChangeList);
    }

    public void processCancelOutbound(DocumentTypeEnum documentTypeEnum, Long documentId, BusinessTypeEnum businessTypeEnum, List<SalesOutboundItemDO> salesOutboundItemDOS) {
        List<InventoryChange> inventoryChangeList = new ArrayList<>(salesOutboundItemDOS.size());
        salesOutboundItemDOS.forEach(item -> {
            inventoryChangeList.add(new InventoryChange(item.getInventoryDetailId(), item.getOutQuantity()));
        });
        inventoryDetailService.batchIncreaseAvailableQuantity(inventoryChangeList);
        List<Long> ids = salesOutboundItemDOS.stream().map(SalesOutboundItemDO::getId).toList();
        inventoryDetailService.deleteByDocumentAndBusinessTypeAndId(documentTypeEnum.getType(), documentId, businessTypeEnum.getType(), ids);
    }


    public void processCancelReturnedInbound(DocumentTypeEnum documentTypeEnum, Long documentId, BusinessTypeEnum businessTypeEnum, List<Long> businessItemIds) {
        inventoryDetailService.deleteByDocumentAndBusinessTypeAndId(documentTypeEnum.getType(), documentId, businessTypeEnum.getType(), businessItemIds);
    }
}
