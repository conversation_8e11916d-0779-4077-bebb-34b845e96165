package cn.iocoder.yudao.module.erp.dal.dataobject.base.organization;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 组织：是客户，供应商等的公共信息 DO
 *
 * <AUTHOR>
 */
@TableName("erp_organization")
@KeySequence("erp_organization_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 客户简称
     */
    private String shortName;
    /**
     * 地区
     */
    private Long area;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 联系人
     */
    private String contact;
    /**
     * 联系电话
     */
    private String contactPhone;
    /**
     * 财务联系人
     */
    private String financeContact;
    /**
     * 财务联系人电话
     */
    private String financeContactPhone;
    /**
     * 财务联系人邮箱
     */
    private String financeContactEmail;

    /**
     * 国别
     */
    private String countryType;

}