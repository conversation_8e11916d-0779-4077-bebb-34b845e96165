package cn.iocoder.yudao.module.erp.controller.admin.returned.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 退货通知单新增 DTO")
@Data
public class CreateReturnedOrderDTO {

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @Schema(description = "业务状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业务状态不能为空")
    private Integer businessStatus;

    @Schema(description = "单据状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "单据状态不能为空")
    private Integer documentStatus;

    @Schema(description = "退货日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "退货日期不能为空")
    private LocalDateTime returnedDate;

    @Schema(description = "仓库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12305")
    @NotNull(message = "仓库ID不能为空")
    private Long warehouseId;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "仓库名称不能为空")
    private String warehouseName;

    @Schema(description = "客户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15234")
    @NotNull(message = "客户ID不能为空")
    private Long customerId;

    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "客户名称不能为空")
    private String customerName;

    @Schema(description = "业务部门", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务部门不能为空")
    private Long businessDepartment;

    @Schema(description = "业务员", requiredMode = Schema.RequiredMode.REQUIRED, example = "2191")
    @NotNull(message = "业务员不能为空")
    private Long businessUserId;

    @Schema(description = "退货原因", example = "不喜欢")
    private String returnedReason;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "退货通知单-明细列表")
    @Size(min = 1, message = "退货通知单-明细列表不能为空")
    @Valid
    private List<ReturnedOrderItemDTO> items;

}