package cn.iocoder.yudao.module.erp.dal.dataobject.inventory.view;

import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryBillDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryBillItemDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 出入库清单明细与主表组合 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ItemWithInventoryBill extends InventoryBillItemDO {

    /**
     * 出入库清单信息
     */
    private InventoryBillDO inventoryBill;

}
