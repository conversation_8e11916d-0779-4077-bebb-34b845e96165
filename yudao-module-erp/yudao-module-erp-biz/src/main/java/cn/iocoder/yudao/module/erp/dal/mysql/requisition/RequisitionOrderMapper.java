package cn.iocoder.yudao.module.erp.dal.mysql.requisition;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.erp.controller.admin.requisition.vo.SearchRequisitionOrderDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.requisition.RequisitionOrderDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

/**
 * 领料单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RequisitionOrderMapper extends BaseMapperX<RequisitionOrderDO> {

    default PageResult<RequisitionOrderDO> selectPage(SearchRequisitionOrderDTO searchDTO) {
        return selectPage(searchDTO, new LambdaQueryWrapperX<RequisitionOrderDO>()
                .likeIfPresent(RequisitionOrderDO::getCode, searchDTO.getCode())
                .likeIfPresent(RequisitionOrderDO::getPurpose, searchDTO.getPurpose())
                .eqIfPresent(RequisitionOrderDO::getBusinessDepartmentId, searchDTO.getBusinessDepartmentId())
                .eqIfPresent(RequisitionOrderDO::getBusinessUserId, searchDTO.getBusinessUserId())
                .eqIfPresent(RequisitionOrderDO::getWarehouseId, searchDTO.getWarehouseId())
                .likeIfPresent(RequisitionOrderDO::getWarehouseName, searchDTO.getWarehouseName())
                .eqIfPresent(RequisitionOrderDO::getDocumentStatus, searchDTO.getDocumentStatus())
                .eqIfPresent(RequisitionOrderDO::getBusinessStatus, searchDTO.getBusinessStatus())
                .betweenIfPresent(RequisitionOrderDO::getRequisitionDate, searchDTO.getRequisitionDate())
                .betweenIfPresent(RequisitionOrderDO::getCreateTime, searchDTO.getCreateTime())
                .orderByDesc(RequisitionOrderDO::getId));
    }

    default void updateBusinessStatusById(Integer status, Long id) {
        RequisitionOrderDO updateObj = new RequisitionOrderDO();
        updateObj.setId(id);
        updateObj.setBusinessStatus(status);
        updateById(updateObj);
    }

    default void updateDocumentApproveStatusById(Long id, Integer status) {
        RequisitionOrderDO updateObj = new RequisitionOrderDO();
        updateObj.setId(id);
        updateObj.setDocumentStatus(status);
        updateObj.setAuditTime(LocalDateTime.now());
        updateById(updateObj);
    }

    default Long countByWarehouseId(Long warehouseId) {
        return selectCount(RequisitionOrderDO::getWarehouseId, warehouseId);
    }

}
