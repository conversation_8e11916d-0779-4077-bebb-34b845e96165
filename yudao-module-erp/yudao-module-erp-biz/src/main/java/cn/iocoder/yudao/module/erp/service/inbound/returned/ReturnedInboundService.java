package cn.iocoder.yudao.module.erp.service.inbound.returned;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.inbound.returned.vo.*;
import jakarta.validation.Valid;

/**
 * 销售退货入库 Service 接口
 *
 * <AUTHOR>
 */
public interface ReturnedInboundService {

    /**
     * 创建销售退货入库
     *
     * @param createDTO 创建信息
     * @return Id
     */
    Long create(@Valid CreateReturnedInboundDTO createDTO);

    /**
     * 更新销售退货入库
     *
     * @param updateDTO 更新信息
     */
    void update(Long id, @Valid UpdateReturnedInboundDTO updateDTO);

    /**
     * 删除销售退货入库
     *
     * @param id Id
     */
    void delete(Long id);

    /**
     * 获得销售退货入库
     *
     * @param id Id
     * @return 销售退货入库
     */
    ReturnedInboundVO get(Long id);

    /**
     * 获得销售退货入库分页
     *
     * @param searchDTO 分页查询
     * @return 销售退货入库分页
     */
    PageResult<SimpleReturnedInboundVO> page(SearchReturnedInboundDTO searchDTO);

    PageResult<ItemWithReturnedInboundVO> pageItem(SearchItemPageDTO searchDTO);

    void cancelSuspend(Long id);

    void suspend(Long id);

    void cancelApprove(Long id);

    void approve(Long id);

    void cancelClose(Long id);

    void close(Long id);

    Boolean isInboundByReturnedOrderId(Long id);
}