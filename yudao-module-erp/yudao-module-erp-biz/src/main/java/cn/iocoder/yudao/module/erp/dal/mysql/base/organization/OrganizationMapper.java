package cn.iocoder.yudao.module.erp.dal.mysql.base.organization;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.erp.controller.admin.base.organization.vo.SearchOrganizationDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.base.organization.OrganizationDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 组织：是客户，供应商等的公共信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OrganizationMapper extends BaseMapperX<OrganizationDO> {

    default PageResult<OrganizationDO> selectPage(SearchOrganizationDTO searchDTO) {
        return selectPage(searchDTO, new LambdaQueryWrapperX<OrganizationDO>()
                .likeIfPresent(OrganizationDO::getName, searchDTO.getName())
                .likeIfPresent(OrganizationDO::getShortName, searchDTO.getShortName())
                .eqIfPresent(OrganizationDO::getArea, searchDTO.getArea())
                .eqIfPresent(OrganizationDO::getAddress, searchDTO.getAddress())
                .eqIfPresent(OrganizationDO::getContact, searchDTO.getContact())
                .eqIfPresent(OrganizationDO::getContactPhone, searchDTO.getContactPhone())
                .eqIfPresent(OrganizationDO::getFinanceContact, searchDTO.getFinanceContact())
                .eqIfPresent(OrganizationDO::getFinanceContactPhone, searchDTO.getFinanceContactPhone())
                .eqIfPresent(OrganizationDO::getFinanceContactEmail, searchDTO.getFinanceContactEmail())
                .betweenIfPresent(OrganizationDO::getCreateTime, searchDTO.getCreateTime())
                .orderByDesc(OrganizationDO::getId));
    }


    default long selectCountByNameLike(String name) {
        return selectCount(new LambdaQueryWrapperX<OrganizationDO>()
                .like(OrganizationDO::getName, name));
    }

    default OrganizationDO selectByName(String name) {
        return selectOne(OrganizationDO::getName, name);
    }

    default OrganizationDO selectByShortName(String shortName) {
        return selectOne(OrganizationDO::getShortName, shortName);
    }
}