package cn.iocoder.yudao.module.erp.service.purchase.materialconfig;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.materialconfig.vo.ImportResultVO;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.materialconfig.vo.PurchaseMaterialConfigImportExcelVO;
import cn.iocoder.yudao.module.erp.controller.admin.purchase.materialconfig.vo.SavePurchaseMaterialConfigDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.base.MaterialMasterDataDO;
import cn.iocoder.yudao.module.erp.dal.mysql.base.MaterialMasterDataMapper;
import cn.iocoder.yudao.module.erp.framework.excel.ExcelErrorAnnotationUtils;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataService;
import cn.iocoder.yudao.module.erp.service.common.AbstractExcelImportService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 采购物料周期配置导入服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PurchaseMaterialConfigImportService extends AbstractExcelImportService<PurchaseMaterialConfigImportExcelVO> {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(PurchaseMaterialConfigImportService.class);

    // Excel列索引常量（基于PurchaseMaterialConfigImportExcelVO的@ExcelProperty注解顺序）
    private static final int COLUMN_MATERIAL_CODE = 0;  // 物料编码
    private static final int COLUMN_MATERIAL_NAME = 1;  // 物料名称
    private static final int COLUMN_AVERAGE_CYCLE = 2;  // 平均采购周期/天

    @Resource
    private PurchaseMaterialConfigService purchaseMaterialConfigService;

    @Resource
    private MaterialMasterDataService materialMasterDataService;

    @Resource
    private MaterialMasterDataMapper materialMasterDataMapper;

    @Resource
    private ExcelErrorAnnotationUtils excelErrorAnnotationUtils;

    @Override
    protected List<PurchaseMaterialConfigImportExcelVO> parseExcel(MultipartFile file) {
        try {
            // 先清理Excel格式，然后再解析
            MultipartFile cleanedFile = cleanExcelFormat(file);
            return ExcelUtils.read(cleanedFile, PurchaseMaterialConfigImportExcelVO.class);
        } catch (Exception e) {
            log.error("解析Excel文件失败", e);
            throw new RuntimeException("Excel文件解析失败，请检查文件格式", e);
        }
    }

    /**
     * 清理Excel文件的格式（移除边框、批注等格式化信息）
     *
     * @param originalFile 原始Excel文件
     * @return 清理后的Excel文件
     * @throws Exception 处理异常
     */
    private MultipartFile cleanExcelFormat(MultipartFile originalFile) throws Exception {
        log.info("开始清理Excel格式，文件名: {}", originalFile.getOriginalFilename());

        Workbook workbook = null;
        try {
            // 根据文件扩展名创建对应的Workbook
            String fileName = originalFile.getOriginalFilename();
            if (fileName != null && fileName.toLowerCase().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(originalFile.getInputStream());
            } else {
                workbook = new HSSFWorkbook(originalFile.getInputStream());
            }

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 清理所有单元格的格式
            cleanSheetFormat(sheet, workbook);

            // 将清理后的工book转换为MultipartFile
            java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream();
            workbook.write(outputStream);
            byte[] cleanedBytes = outputStream.toByteArray();

            log.info("Excel格式清理完成，原始大小: {} bytes, 清理后大小: {} bytes",
                    originalFile.getSize(), cleanedBytes.length);

            return createMultipartFile(originalFile, cleanedBytes);

        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    /**
     * 清理工作表的格式
     *
     * @param sheet    工作表
     * @param workbook 工作簿
     */
    private void cleanSheetFormat(Sheet sheet, Workbook workbook) {
        log.debug("开始清理工作表格式，工作表名: {}", sheet.getSheetName());

        // 创建默认的单元格样式
        CellStyle defaultStyle = workbook.createCellStyle();
        defaultStyle.setBorderTop(BorderStyle.NONE);
        defaultStyle.setBorderBottom(BorderStyle.NONE);
        defaultStyle.setBorderLeft(BorderStyle.NONE);
        defaultStyle.setBorderRight(BorderStyle.NONE);

        int cleanedCellCount = 0;
        int cleanedCommentCount = 0;

        // 遍历所有行
        for (Row row : sheet) {
            if (row == null) continue;

            // 遍历行中的所有单元格
            for (Cell cell : row) {
                if (cell == null) continue;

                // 清理单元格样式（移除边框、背景色等）
                cell.setCellStyle(defaultStyle);
                cleanedCellCount++;

                // 清理单元格批注
                Comment comment = cell.getCellComment();
                if (comment != null) {
                    cell.removeCellComment();
                    cleanedCommentCount++;
                }
            }
        }

        log.debug("工作表格式清理完成，清理了 {} 个单元格样式，{} 个批注",
                cleanedCellCount, cleanedCommentCount);
    }

    /**
     * 创建MultipartFile对象
     *
     * @param originalFile 原始文件
     * @param bytes        文件字节数组
     * @return MultipartFile对象
     */
    private MultipartFile createMultipartFile(MultipartFile originalFile, byte[] bytes) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return originalFile.getName();
            }

            @Override
            public String getOriginalFilename() {
                return originalFile.getOriginalFilename();
            }

            @Override
            public String getContentType() {
                return originalFile.getContentType();
            }

            @Override
            public boolean isEmpty() {
                return bytes.length == 0;
            }

            @Override
            public long getSize() {
                return bytes.length;
            }

            @Override
            public byte[] getBytes() {
                return bytes;
            }

            @Override
            public java.io.InputStream getInputStream() {
                return new ByteArrayInputStream(bytes);
            }

            @Override
            public void transferTo(java.io.File dest) throws java.io.IOException, IllegalStateException {
                try (java.io.FileOutputStream fos = new java.io.FileOutputStream(dest)) {
                    fos.write(bytes);
                }
            }
        };
    }

    @Override
    protected Map<Integer, List<String>> validateData(List<PurchaseMaterialConfigImportExcelVO> dataList) {
        // 使用新的单元格级别验证方法
        Map<Integer, Map<Integer, List<String>>> cellErrors = validateDataWithCellErrors(dataList);

        // 为了保持与父类接口的兼容性，将单元格错误转换为行错误
        Map<Integer, List<String>> rowErrors = new HashMap<>();
        cellErrors.forEach((rowIndex, columnErrors) -> {
            List<String> allErrors = new ArrayList<>();
            columnErrors.values().forEach(allErrors::addAll);
            if (!allErrors.isEmpty()) {
                rowErrors.put(rowIndex, allErrors);
            }
        });

        return rowErrors;
    }

    /**
     * 数据校验（单元格级别）
     *
     * @param dataList 数据列表
     * @return 校验错误信息 Map<行号, Map<列号, 错误信息列表>>
     */
    protected Map<Integer, Map<Integer, List<String>>> validateDataWithCellErrors(List<PurchaseMaterialConfigImportExcelVO> dataList) {
        Map<Integer, Map<Integer, List<String>>> errors = new HashMap<>();

        if (CollUtil.isEmpty(dataList)) {
            return errors;
        }

        // 收集所有物料编码进行批量查询
        List<String> materialCodes = dataList.stream()
                .map(PurchaseMaterialConfigImportExcelVO::getMaterialCode)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        Map<String, MaterialMasterDataDO> materialMap = getMaterialMapByCode(materialCodes);

        // 检查Excel内部重复的物料编码
        Set<String> duplicateCheck = new HashSet<>();
        Set<String> duplicateCodes = new HashSet<>();
        for (PurchaseMaterialConfigImportExcelVO item : dataList) {
            if (StrUtil.isNotBlank(item.getMaterialCode())) {
                if (!duplicateCheck.add(item.getMaterialCode())) {
                    duplicateCodes.add(item.getMaterialCode());
                }
            }
        }

        for (int i = 0; i < dataList.size(); i++) {
            int rowIndex = i + 2; // Excel行号从2开始（第1行是标题）
            PurchaseMaterialConfigImportExcelVO item = dataList.get(i);
            Map<Integer, List<String>> rowCellErrors = new HashMap<>();

            // 1. 基础字段校验（单元格级别）
            validateBasicFieldsWithCellErrors(item, rowCellErrors);

            // 2. Excel内部重复校验（物料编码列）
            if (duplicateCodes.contains(item.getMaterialCode())) {
                rowCellErrors.computeIfAbsent(COLUMN_MATERIAL_CODE, k -> new ArrayList<>())
                        .add(String.format("Excel中存在重复的物料编码(%s)", item.getMaterialCode()));
            }

            // 3. 物料业务校验（单元格级别）
            validateMaterialBusinessWithCellErrors(item, materialMap, rowCellErrors);

            if (!rowCellErrors.isEmpty()) {
                errors.put(rowIndex, rowCellErrors);
            }
        }

        return errors;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    protected ImportResultVO processSuccessImport(List<PurchaseMaterialConfigImportExcelVO> dataList) {
        List<String> successMaterialCodes = new ArrayList<>();

        for (PurchaseMaterialConfigImportExcelVO item : dataList) {
            try {
                // 通过物料编码获取物料ID
                MaterialMasterDataDO material = getMaterialByCode(item.getMaterialCode());

                // 保存配置
                SavePurchaseMaterialConfigDTO saveDTO = new SavePurchaseMaterialConfigDTO();
                saveDTO.setAverageCycleValue(item.getAverageCycleValue());

                purchaseMaterialConfigService.save(material.getId(), saveDTO);
                successMaterialCodes.add(item.getMaterialCode());

            } catch (Exception e) {
                log.error("保存采购物料配置失败，物料编码：{}", item.getMaterialCode(), e);
                // 这里理论上不应该发生，因为前面已经校验过了
                throw new RuntimeException("保存采购物料配置失败: " + e.getMessage(), e);
            }
        }

        ImportResultVO result = new ImportResultVO();
        result.setTotalCount(dataList.size());
        result.setSuccessCount(successMaterialCodes.size());
        result.setFailureCount(0);
        result.setAllSuccess(true);
        result.setSuccessMaterialCodes(successMaterialCodes);
        return result;
    }

    @Override
    protected ImportResultVO processFailureImport(MultipartFile file,
                                                  List<PurchaseMaterialConfigImportExcelVO> dataList,
                                                  Map<Integer, List<String>> errors) {
        try {
            // 重新获取单元格级别的错误信息
            Map<Integer, Map<Integer, List<String>>> cellErrors = validateDataWithCellErrors(dataList);

            // 使用新的单元格级别错误标注方法
            String errorFileUrl = excelErrorAnnotationUtils.generateErrorExcelFileWithCellErrors(file, cellErrors);

            ImportResultVO result = new ImportResultVO();
            result.setTotalCount(dataList.size());
            result.setSuccessCount(0);
            result.setFailureCount(errors.size());
            result.setAllSuccess(false);
            result.setErrorFileUrl(errorFileUrl);
            result.setFailureDetails(errors);
            return result;

        } catch (Exception e) {
            log.error("生成错误Excel文件失败", e);
            throw new RuntimeException("生成错误文件失败", e);
        }
    }

    /**
     * 基础字段校验
     */
    private void validateBasicFields(PurchaseMaterialConfigImportExcelVO item, List<String> errors) {
        if (StrUtil.isBlank(item.getMaterialCode())) {
            errors.add("物料编码不能为空");
        }
        if (item.getAverageCycleValue() == null) {
            errors.add("平均采购周期不能为空");
        } else if (item.getAverageCycleValue() < 1) {
            errors.add("平均采购周期不能小于1天");
        } else if (item.getAverageCycleValue() > 999) {
            errors.add("平均采购周期不能大于999天");
        }
    }

    /**
     * 基础字段校验（单元格级别）
     */
    private void validateBasicFieldsWithCellErrors(PurchaseMaterialConfigImportExcelVO item,
                                                   Map<Integer, List<String>> cellErrors) {
        // 物料编码校验
        if (StrUtil.isBlank(item.getMaterialCode())) {
            cellErrors.computeIfAbsent(COLUMN_MATERIAL_CODE, k -> new ArrayList<>())
                    .add("物料编码不能为空");
        }


        // 平均采购周期校验
        if (item.getAverageCycleValue() == null) {
            cellErrors.computeIfAbsent(COLUMN_AVERAGE_CYCLE, k -> new ArrayList<>())
                    .add("平均采购周期不能为空");
        } else if (item.getAverageCycleValue() < 1) {
            cellErrors.computeIfAbsent(COLUMN_AVERAGE_CYCLE, k -> new ArrayList<>())
                    .add("平均采购周期不能小于1天");
        } else if (item.getAverageCycleValue() > 999) {
            cellErrors.computeIfAbsent(COLUMN_AVERAGE_CYCLE, k -> new ArrayList<>())
                    .add("平均采购周期不能大于999天");
        }
    }

    /**
     * 物料业务校验
     */
    private void validateMaterialBusiness(PurchaseMaterialConfigImportExcelVO item,
                                          Map<String, MaterialMasterDataDO> materialMap,
                                          List<String> errors) {
        if (StrUtil.isBlank(item.getMaterialCode())) {
            return; // 基础校验已处理
        }

        MaterialMasterDataDO material = materialMap.get(item.getMaterialCode());
        if (material == null) {
            errors.add(String.format("物料编码(%s)不存在", item.getMaterialCode()));
            return;
        }


        // 校验物料是否启用
        if (material.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
            errors.add(String.format("物料(%s)未启用", item.getMaterialCode()));
        }
    }

    /**
     * 物料业务校验（单元格级别）
     */
    private void validateMaterialBusinessWithCellErrors(PurchaseMaterialConfigImportExcelVO item,
                                                        Map<String, MaterialMasterDataDO> materialMap,
                                                        Map<Integer, List<String>> cellErrors) {
        if (StrUtil.isBlank(item.getMaterialCode())) {
            return; // 基础校验已处理
        }

        MaterialMasterDataDO material = materialMap.get(item.getMaterialCode());
        if (material == null) {
            cellErrors.computeIfAbsent(COLUMN_MATERIAL_CODE, k -> new ArrayList<>())
                    .add(String.format("物料编码(%s)不存在", item.getMaterialCode()));
            return;
        }

        // 校验物料是否启用
        if (material.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
            cellErrors.computeIfAbsent(COLUMN_MATERIAL_CODE, k -> new ArrayList<>())
                    .add(String.format("物料(%s)未启用", item.getMaterialCode()));
        }
    }

    /**
     * 根据物料编码批量查询物料信息
     */
    private Map<String, MaterialMasterDataDO> getMaterialMapByCode(List<String> materialCodes) {
        if (CollUtil.isEmpty(materialCodes)) {
            return new HashMap<>();
        }

        List<MaterialMasterDataDO> materials = materialMasterDataMapper.selectList(
                new LambdaQueryWrapperX<MaterialMasterDataDO>()
                        .in(MaterialMasterDataDO::getCode, materialCodes));

        return materials.stream()
                .collect(Collectors.toMap(MaterialMasterDataDO::getCode, material -> material));
    }

    /**
     * 根据物料编码获取物料信息
     */
    private MaterialMasterDataDO getMaterialByCode(String materialCode) {
        return materialMasterDataMapper.selectByCode(materialCode);
    }

}
