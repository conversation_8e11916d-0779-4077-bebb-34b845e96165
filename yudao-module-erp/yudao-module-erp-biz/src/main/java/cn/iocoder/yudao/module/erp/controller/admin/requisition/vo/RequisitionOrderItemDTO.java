package cn.iocoder.yudao.module.erp.controller.admin.requisition.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 领料单明细 DTO")
@Data
public class RequisitionOrderItemDTO {

    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED, type = "number", example = "5")
    @NotNull(message = "物料ID不能为空")
    private Long materialId;

    @Schema(description = "物料编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "CP")
    @NotNull(message = "物料编码不能为空")
    private String materialCode;

    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "电脑")
    @NotNull(message = "物料名称不能为空")
    private String materialName;

    @Schema(description = "规格型号", example = "2核16G1T")
    private String materialSpec;

    @Schema(description = "主计量单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "台")
    @NotNull(message = "主计量单位不能为空")
    private String materialUom;

    @Schema(description = "可用库存", requiredMode = Schema.RequiredMode.REQUIRED, type = "number", example = "100")
    @NotNull(message = "可用库存不能为空")
    private BigDecimal availableQuantity;

    @Schema(description = "领用数量", requiredMode = Schema.RequiredMode.REQUIRED, type = "number", example = "5")
    @NotNull(message = "领用数量不能为空")
    private BigDecimal requestQuantity;

    @Schema(description = "备注", example = "备注信息")
    private String remark;
}
