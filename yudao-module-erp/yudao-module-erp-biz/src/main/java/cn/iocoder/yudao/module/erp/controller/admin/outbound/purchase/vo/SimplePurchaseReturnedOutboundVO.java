package cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo;

import cn.iocoder.yudao.module.erp.controller.admin.inventory.common.CommonInventoryDocumentVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 采购退货出库单 Simple Response VO")
@Data
@ExcelIgnoreUnannotated
public class SimplePurchaseReturnedOutboundVO extends CommonInventoryDocumentVO {

    @Schema(description = "出库日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出库日期")
    private LocalDateTime outboundDate;

    @Schema(description = "供应商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24917")
    @ExcelProperty("供应商ID")
    private Long supplierId;

    @Schema(description = "供应商名称", example = "芋艿")
    @ExcelProperty("供应商名称")
    private String supplierName;

    @Schema(description = "物料类型:采购单的物料类型（业务字典）", example = "2")
    @ExcelProperty("物料类型:采购单的物料类型（业务字典）")
    private String materialType;

    @Schema(description = "退货通知单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27355")
    @ExcelProperty("退货通知单ID")
    private Long returnedId;

    @Schema(description = "退货通知单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("退货通知单号")
    private String returnedCode;


}
