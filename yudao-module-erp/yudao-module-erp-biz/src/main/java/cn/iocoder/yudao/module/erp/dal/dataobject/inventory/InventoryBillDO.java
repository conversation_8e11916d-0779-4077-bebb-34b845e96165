package cn.iocoder.yudao.module.erp.dal.dataobject.inventory;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.DocumentDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 出入库清单 DO
 *
 * <AUTHOR>
 */
@TableName("erp_inventory_bill")
@KeySequence("erp_inventory_bill_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBillDO extends DocumentDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 单据号
     */
    private String code;
    /**
     * in入库 out出库
     */
    private String direction;
    /**
     * 单据类型
     */
    private String documentType;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 0编制中 1审核通过 2回退
     */
    private Integer documentStatus;
    /**
     * 业务状态：0关闭 1正常 2挂起
     */
    private Integer businessStatus;
    /**
     * 单据日期
     */
    private LocalDate documentDate;
    /**
     * 仓库ID
     */
    private Long warehouseId;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 供应商ID
     */
    private Long supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 客户ID
     */
    private Long customerId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 业务部门ID
     */
    private Long businessDepartmentId;
    /**
     * 业务部门名称
     */
    private String businessDepartmentName;
    /**
     * 业务员ID
     */
    private Long businessUserId;
    /**
     * 业务员姓名
     */
    private String businessUserName;
    /**
     * 来源单据类型
     */
    private String sourceType;
    /**
     * 来源单据ID
     */
    private Long sourceId;
    /**
     * 来源单据号
     */
    private String sourceCode;
    /**
     * 物料类型:采购单的物料类型（业务字典）
     */
    private String materialType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

}