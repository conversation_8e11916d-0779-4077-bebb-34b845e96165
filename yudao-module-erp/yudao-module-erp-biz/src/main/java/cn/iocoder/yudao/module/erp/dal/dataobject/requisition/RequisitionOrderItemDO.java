package cn.iocoder.yudao.module.erp.dal.dataobject.requisition;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 领料单明细 DO
 *
 * <AUTHOR>
 */
@TableName("erp_requisition_order_item")
@KeySequence("erp_requisition_order_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequisitionOrderItemDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 领料单ID
     */
    private Long requisitionOrderId;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String materialSpec;

    /**
     * 主计量单位
     */
    private String materialUom;

    /**
     * 可用库存
     */
    private BigDecimal availableQuantity;

    /**
     * 领用数量
     */
    private BigDecimal requestQuantity;

    /**
     * 备注
     */
    private String remark;

}
