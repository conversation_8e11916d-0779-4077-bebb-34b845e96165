package cn.iocoder.yudao.module.erp.dal.mysql.inbound.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.iocoder.yudao.module.erp.constant.DocumentStatusEnum;
import cn.iocoder.yudao.module.erp.controller.admin.inbound.purchase.vo.SearchItemPageDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.PurchaseInboundDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.PurchaseInboundItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.view.PurchaseInboundItemWithInboundDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 采购入库单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseInboundItemMapper extends BaseMapperX<PurchaseInboundItemDO> {

    default List<PurchaseInboundItemDO> selectListByInboundId(Long inboundId) {
        return selectList(PurchaseInboundItemDO::getInboundId, inboundId);
    }

    default void deleteByInboundId(Long inboundId) {
        delete(PurchaseInboundItemDO::getInboundId, inboundId);
    }

    default PageResult<PurchaseInboundItemWithInboundDO> selectItemPage(SearchItemPageDTO searchDTO) {
        MPJLambdaWrapper<PurchaseInboundItemDO> wrapper = new MPJLambdaWrapperX<PurchaseInboundItemDO>()
                .selectAll(PurchaseInboundItemDO.class)
                .likeIfPresent(PurchaseInboundItemDO::getMaterialCode, searchDTO.getMaterialCode())
                .likeIfPresent(PurchaseInboundItemDO::getMaterialName, searchDTO.getMaterialName())
                .likeIfPresent(PurchaseInboundItemDO::getBatchNo, searchDTO.getBatchNo())
                .likeIfPresent(PurchaseInboundItemDO::getUniqueCode, searchDTO.getUniqueCode())

                .selectAssociation(PurchaseInboundDO.class, PurchaseInboundItemWithInboundDO::getInbound)
                .leftJoin(PurchaseInboundDO.class, PurchaseInboundDO::getId, PurchaseInboundItemDO::getInboundId)
                .likeIfExists(PurchaseInboundDO::getCode, searchDTO.getCode())
                .eqIfExists(PurchaseInboundDO::getWarehouseId, searchDTO.getWarehouseId())
                .eqIfExists(PurchaseInboundDO::getDocumentStatus, searchDTO.getDocumentStatus())

                .orderByDesc(PurchaseInboundDO::getInboundDate)
                .orderByDesc(PurchaseInboundDO::getId);

        if (searchDTO.getInboundDate() != null && searchDTO.getInboundDate().length > 0) {
            LocalDate[] inboundDate = searchDTO.getInboundDate();
            LocalDate start = inboundDate[0];
            LocalDate end = inboundDate.length > 1 ? inboundDate[1] : null;

            if (start != null && end != null) {
                wrapper.between(PurchaseInboundDO::getInboundDate, start, end);
            } else if (start != null) {
                wrapper.ge(PurchaseInboundDO::getInboundDate, start);
            } else if (end != null) {
                wrapper.le(PurchaseInboundDO::getInboundDate, end);
            }
        }
        //根据searchDTO的所有条件，使用联合查询
        return selectJoinPage(searchDTO, PurchaseInboundItemWithInboundDO.class, wrapper);

    }

    default Long countByMaterialId(Long materialId) {
        return selectCount(PurchaseInboundItemDO::getMaterialId, materialId);
    }

    default Long countByPurchaseArrivalId(Long purchaseArrivalId) {
        return selectCount(PurchaseInboundItemDO::getPurchaseArrivalId, purchaseArrivalId);
    }

    default List<PurchaseInboundItemDO> selectItemListByPurchaseArrivalId(Long purchaseArrivalId) {
        return selectList(PurchaseInboundItemDO::getPurchaseArrivalId, purchaseArrivalId);
    }

    default boolean existsApprovedByUniqueCode(String uniqueCode) {
        return selectCount(new MPJLambdaWrapper<PurchaseInboundItemDO>()
                .eq(PurchaseInboundItemDO::getUniqueCode, uniqueCode)
                .eq(PurchaseInboundDO::getDocumentStatus, DocumentStatusEnum.APPROVE.getStatus())
                .leftJoin(PurchaseInboundDO.class, PurchaseInboundDO::getId, PurchaseInboundItemDO::getInboundId)) > 0;
    }

    default void updateReturnedQuantityWhenPurchaseReturnedApprove(Long purchaseInboundItemId, BigDecimal quantity) {
        update(new LambdaUpdateWrapper<PurchaseInboundItemDO>()
                .setSql("returned_quantity = returned_quantity + " + quantity)
                .setSql("unreturned_quantity = unreturned_quantity - " + quantity)
                .setSql("returnable_quantity = returnable_quantity - " + quantity)
                .eq(PurchaseInboundItemDO::getId, purchaseInboundItemId));
    }

    default void initReturnedQuantity(Long id) {
        update(new LambdaUpdateWrapper<PurchaseInboundItemDO>()
                .setSql("returned_quantity = 0")
                .setSql("returnable_quantity = inbound_quantity")
                .setSql("unreturned_quantity = inbound_quantity")
                .eq(PurchaseInboundItemDO::getInboundId, id));
    }

    default void updateReturnedQuantityWhenPurchaseReturnedCancelApprove(Long purchaseInboundItemId, BigDecimal quantity) {
        update(new LambdaUpdateWrapper<PurchaseInboundItemDO>()
                .setSql("returned_quantity = returned_quantity - " + quantity)
                .setSql("unreturned_quantity = unreturned_quantity + " + quantity)
                .setSql("returnable_quantity = returnable_quantity + " + quantity)
                .eq(PurchaseInboundItemDO::getId, purchaseInboundItemId));
    }
}
