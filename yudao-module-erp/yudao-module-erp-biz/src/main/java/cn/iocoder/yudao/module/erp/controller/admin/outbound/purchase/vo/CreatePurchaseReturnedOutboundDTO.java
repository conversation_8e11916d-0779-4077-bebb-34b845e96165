package cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo;

import cn.iocoder.yudao.module.erp.controller.admin.inventory.common.CommonInventoryDocumentDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 采购退货出库单新增 DTO")
@Data
public class CreatePurchaseReturnedOutboundDTO extends CommonInventoryDocumentDTO {


    @Schema(description = "出库日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "出库日期不能为空")
    private LocalDateTime outboundDate;


    @Schema(description = "供应商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24917")
    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;

    @Schema(description = "供应商名称", example = "芋艿")
    private String supplierName;


    @Schema(description = "物料类型:采购单的物料类型（业务字典）", example = "2")
    private String materialType;

    @Schema(description = "退货通知单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27355")
    @NotNull(message = "退货通知单ID不能为空")
    private Long returnedId;

    @Schema(description = "退货通知单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "退货通知单号不能为空")
    private String returnedCode;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;


    @Schema(description = "采购退货出库单明细列表")
    private List<PurchaseReturnedOutboundItemDTO> items;
}