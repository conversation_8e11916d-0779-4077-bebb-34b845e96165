package cn.iocoder.yudao.module.erp.dal.mysql.outbound.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.SearchItemPageDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.outbound.purchase.PurchaseReturnedOutboundDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.outbound.purchase.PurchaseReturnedOutboundItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.outbound.purchase.view.ItemWithPurchaseReturnedOutbound;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.List;

/**
 * 采购退货出库单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseReturnedOutboundItemMapper extends BaseMapperX<PurchaseReturnedOutboundItemDO> {

    default PageResult<PurchaseReturnedOutboundItemDO> selectPage(PageParam reqVO, Long returnedOutboundId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PurchaseReturnedOutboundItemDO>()
                .eq(PurchaseReturnedOutboundItemDO::getBillId, returnedOutboundId)
                .orderByDesc(PurchaseReturnedOutboundItemDO::getId));
    }

    default int deleteByReturnedOutboundId(Long returnedOutboundId) {
        return delete(PurchaseReturnedOutboundItemDO::getBillId, returnedOutboundId);
    }

    /**
     * 根据主表ID查询明细列表
     *
     * @param returnedOutboundId 主表ID
     * @return 明细列表
     */
    default List<PurchaseReturnedOutboundItemDO> selectListByReturnedOutboundId(Long returnedOutboundId) {
        return selectList(PurchaseReturnedOutboundItemDO::getBillId, returnedOutboundId);
    }

    /**
     * 分页查询明细与主表组合数据
     *
     * @param searchDTO 查询条件
     * @return 分页结果
     */
    default PageResult<ItemWithPurchaseReturnedOutbound> selectItemPage(SearchItemPageDTO searchDTO) {
        MPJLambdaWrapper<PurchaseReturnedOutboundItemDO> wrapper = new MPJLambdaWrapperX<PurchaseReturnedOutboundItemDO>()
                .selectAll(PurchaseReturnedOutboundItemDO.class)
                .likeIfPresent(PurchaseReturnedOutboundItemDO::getMaterialCode, searchDTO.getMaterialCode())
                .likeIfPresent(PurchaseReturnedOutboundItemDO::getMaterialName, searchDTO.getMaterialName())
                .likeIfPresent(PurchaseReturnedOutboundItemDO::getBatchNumber, searchDTO.getBatchNo())
                .likeIfPresent(PurchaseReturnedOutboundItemDO::getUniqueCode, searchDTO.getUniqueCode())

                .selectAssociation(PurchaseReturnedOutboundDO.class, ItemWithPurchaseReturnedOutbound::getPurchaseReturnedOutbound)
                .leftJoin(PurchaseReturnedOutboundDO.class, PurchaseReturnedOutboundDO::getId, PurchaseReturnedOutboundItemDO::getBillId)
                .likeIfExists(PurchaseReturnedOutboundDO::getCode, searchDTO.getCode())
                .eqIfExists(PurchaseReturnedOutboundDO::getWarehouseId, searchDTO.getWarehouseId())
                .eqIfExists(PurchaseReturnedOutboundDO::getDocumentStatus, searchDTO.getDocumentStatus())

                .orderByDesc(PurchaseReturnedOutboundDO::getCreateTime)
                .orderByDesc(PurchaseReturnedOutboundDO::getId);

        if (searchDTO.getCreateTime() != null && searchDTO.getCreateTime().length > 0) {
            LocalDate start = searchDTO.getCreateTime()[0];
            LocalDate end = searchDTO.getCreateTime()[1];
            if (start != null && end != null) {
                wrapper.between(PurchaseReturnedOutboundDO::getCreateTime, start.atStartOfDay(), end.plusDays(1).atStartOfDay());
            } else if (start != null) {
                wrapper.ge(PurchaseReturnedOutboundDO::getCreateTime, start.atStartOfDay());
            } else if (end != null) {
                wrapper.le(PurchaseReturnedOutboundDO::getCreateTime, end.plusDays(1).atStartOfDay());
            }
        }

        return selectJoinPage(searchDTO, ItemWithPurchaseReturnedOutbound.class, wrapper);
    }

}
