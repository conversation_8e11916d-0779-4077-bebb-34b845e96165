package cn.iocoder.yudao.module.erp.service.inventory.common;

import cn.iocoder.yudao.module.erp.controller.admin.inventory.common.CommonInventoryDocumentDTO;
import cn.iocoder.yudao.module.erp.controller.admin.inventory.common.CommonInventoryDocumentVO;

public interface InventoryCommonService<C extends CommonInventoryDocumentDTO, VO extends CommonInventoryDocumentVO> {
    /**
     * 创建单据
     *
     * @param createDTO
     * @return
     */
    Long create(C createDTO);

    /**
     * 修改单据
     *
     * @param documentId
     * @param updateDTO
     */
    void update(Long documentId, C updateDTO);

    /**
     * 删除单据，同时擅长单据明细
     *
     * @param id Id
     */
    void delete(Long id);

    /**
     * 获得出库单详情
     *
     * @param id Id
     * @return 采购退货出库单
     */
    VO get(Long id);

    /**
     * 审核采购退货出库单
     *
     * @param id Id
     */
    void approve(Long id);

    /**
     * 取消审核采购退货出库单
     *
     * @param id Id
     */
    void cancelApprove(Long id);
    
}
