package cn.iocoder.yudao.module.erp.dal.mysql.inventory;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.erp.convert.inventory.SearchInventoryBillDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryBillDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

/**
 * 出入库清单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryBillMapper extends BaseMapperX<InventoryBillDO> {

    default PageResult<InventoryBillDO> selectPage(SearchInventoryBillDTO searchDTO) {
        return selectPage(searchDTO, new LambdaQueryWrapperX<InventoryBillDO>()
                .eqIfPresent(InventoryBillDO::getCode, searchDTO.getCode())
                .eqIfPresent(InventoryBillDO::getDirection, searchDTO.getDirection())
                .eqIfPresent(InventoryBillDO::getDocumentType, searchDTO.getDocumentType())
                .eqIfPresent(InventoryBillDO::getBusinessType, searchDTO.getBusinessType())
                .eqIfPresent(InventoryBillDO::getDocumentStatus, searchDTO.getDocumentStatus())
                .eqIfPresent(InventoryBillDO::getBusinessStatus, searchDTO.getBusinessStatus())
                .betweenIfPresent(InventoryBillDO::getDocumentDate, searchDTO.getDocumentDate())
                .eqIfPresent(InventoryBillDO::getWarehouseId, searchDTO.getWarehouseId())
                .likeIfPresent(InventoryBillDO::getWarehouseName, searchDTO.getWarehouseName())
                .eqIfPresent(InventoryBillDO::getSupplierId, searchDTO.getSupplierId())
                .likeIfPresent(InventoryBillDO::getSupplierName, searchDTO.getSupplierName())
                .eqIfPresent(InventoryBillDO::getCustomerId, searchDTO.getCustomerId())
                .likeIfPresent(InventoryBillDO::getCustomerName, searchDTO.getCustomerName())
                .eqIfPresent(InventoryBillDO::getBusinessDepartmentId, searchDTO.getBusinessDepartmentId())
                .likeIfPresent(InventoryBillDO::getBusinessDepartmentName, searchDTO.getBusinessDepartmentName())
                .eqIfPresent(InventoryBillDO::getBusinessUserId, searchDTO.getBusinessUserId())
                .likeIfPresent(InventoryBillDO::getBusinessUserName, searchDTO.getBusinessUserName())
                .eqIfPresent(InventoryBillDO::getSourceType, searchDTO.getSourceType())
                .eqIfPresent(InventoryBillDO::getSourceId, searchDTO.getSourceId())
                .eqIfPresent(InventoryBillDO::getSourceCode, searchDTO.getSourceCode())
                .eqIfPresent(InventoryBillDO::getMaterialType, searchDTO.getMaterialType())
                .eqIfPresent(InventoryBillDO::getRemark, searchDTO.getRemark())
                .betweenIfPresent(InventoryBillDO::getAuditTime, searchDTO.getAuditTime())
                .betweenIfPresent(InventoryBillDO::getCreateTime, searchDTO.getCreateTime())
                .orderByDesc(InventoryBillDO::getId));
    }


    /**
     * 更新业务状态
     *
     * @param businessStatus 业务状态
     * @param id             编号
     */
    default void updateBusinessStatusById(Integer businessStatus, Long id) {
        InventoryBillDO updateObj = new InventoryBillDO();
        updateObj.setBusinessStatus(businessStatus);
        updateObj.setId(id);
        updateById(updateObj);
    }

    /**
     * 更新单据状态
     *
     * @param documentStatus 单据状态
     * @param id             编号
     */
    default void updateDocumentStatusById(Integer documentStatus, Long id) {
        InventoryBillDO updateObj = new InventoryBillDO();
        updateObj.setDocumentStatus(documentStatus);
        updateObj.setId(id);
        updateObj.setAuditTime(LocalDateTime.now());
        updateById(updateObj);
    }

}