package cn.iocoder.yudao.module.erp.convert.requisition;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.requisition.vo.CreateRequisitionOrderDTO;
import cn.iocoder.yudao.module.erp.controller.admin.requisition.vo.RequisitionOrderVO;
import cn.iocoder.yudao.module.erp.controller.admin.requisition.vo.SimpleRequisitionOrderVO;
import cn.iocoder.yudao.module.erp.controller.admin.requisition.vo.UpdateRequisitionOrderDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.requisition.RequisitionOrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 领料单 Convert
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface RequisitionOrderConvert {

    RequisitionOrderConvert INSTANCE = Mappers.getMapper(RequisitionOrderConvert.class);

    RequisitionOrderDO toDO(CreateRequisitionOrderDTO createDTO);

    RequisitionOrderDO toDO(UpdateRequisitionOrderDTO updateDTO);

    RequisitionOrderVO toVO(RequisitionOrderDO requisitionOrder);

    SimpleRequisitionOrderVO toSimpleVO(RequisitionOrderDO requisitionOrder);

    List<SimpleRequisitionOrderVO> toSimpleVOList(List<RequisitionOrderDO> list);

    PageResult<SimpleRequisitionOrderVO> toSimpleVOPage(PageResult<RequisitionOrderDO> page);

}
