package cn.iocoder.yudao.module.erp.controller.admin.inventory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 库存明细记录新增 DTO")
@Data
public class CreateInventoryDetailDTO {

    @Schema(description = "仓库id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29096")
    @NotNull(message = "仓库id不能为空")
    private Long warehouseId;

    @Schema(description = "仓库库位id", example = "7231")
    private Long warehousePositionId;

    @Schema(description = "操作类型（in，入库;out，出库）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "操作类型（in，入库;out，出库）不能为空")
    private String operationType;

    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "单据类型不能为空")
    private Integer documentType;

    @Schema(description = "单据id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21570")
    @NotNull(message = "单据id不能为空")
    private Long documentId;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @Schema(description = "业务项id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12107")
    @NotNull(message = "业务ItemId不能为空")
    private Long businessItemId;

    @Schema(description = "物料id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19059")
    @NotNull(message = "物料id不能为空")
    private Long materialId;

    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "物料名称不能为空")
    private String materialName;

    @Schema(description = "物料编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物料编码不能为空")
    private String materialCode;

    @Schema(description = "物料规格型号")
    private String materialSpec;

    @Schema(description = "物料主计量单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物料主计量单位不能为空")
    private String materialUom;

    @Schema(description = "入库数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal inQuantity;

    @Schema(description = "出库数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal outQuantity;

    @Schema(description = "批次号")
    private String batchNo;

    @Schema(description = "包装类型id", example = "7835")
    private Long packagingTypeId;

    @Schema(description = "包装类型名称", example = "张三")
    private String packagingTypeName;

    @Schema(description = "包装规格id", example = "11971")
    private Long packagingSpecId;

    @Schema(description = "包装规格名称", example = "张三")
    private String packagingSpecName;

    @Schema(description = "包装归属", requiredMode = Schema.RequiredMode.REQUIRED)
    private String packagingOwner;

    @Schema(description = "包装物所属公司id，从customer中获取", example = "20408")
    private Long packagingCompanyId;

    @Schema(description = "包装码")
    private String packagingCode;

    @Schema(description = "挂牌车号")
    private String plateNumber;

    @Schema(description = "毛重")
    private BigDecimal grossWeight;

    @Schema(description = "空重")
    private BigDecimal tareWeight;

    @Schema(description = "充装日期")
    private LocalDateTime fillingDate;

    @Schema(description = "充装人id", example = "12107")
    private Long fillingUserId;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "来源库存id", example = "12107")
    private Long fromInventoryId;

    @Schema(description = "物料的唯一码")
    private String materialUniqueCode;

}