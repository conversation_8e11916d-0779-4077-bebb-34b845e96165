package cn.iocoder.yudao.module.erp.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 单据类型枚举
 */
@Getter
@AllArgsConstructor
public enum DocumentTypeEnum {
    SALES_ORDER(0, "salesOrder", "销售订单"),
    PICKING_LIST(1, "pickingList", "备货通知单"),
    SHIPPING(2, "shipping", "发货通知单"),

    RETURN_ORDER(3, "returnOrder", "退货通知单"),
    INBOUND(4, "inbound", "入库单"),
    SALES_OUTBOUND(5, "salesOutbound", "销售出库单"),
    PURCHASE_REQUEST(6, "purchaseRequest", "采购申请单"),
    OUTBOUND(7, "outbound", "出库单"),

    PURCHASE_ORDER(8, "purchaseOrder", "采购订单"),
    PURCHASE_ARRIVAL(9, "purchaseArrival", "采购到货单"),
    PURCHASE_RETURNED(10, "purchaseReturned", "采购退货单"),
    REQUISITION_ORDER(11, "requisitionOrder", "领料单"),
    ;


    private final Integer type;
    private final String value;
    private final String name;
//    private final String prefix;


}
