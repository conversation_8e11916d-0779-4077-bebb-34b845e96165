package cn.iocoder.yudao.module.erp.dal.mysql.inbound.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.erp.controller.admin.inbound.purchase.vo.SearchPurchaseInboundDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inbound.purchase.PurchaseInboundDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

/**
 * 采购入库单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseInboundMapper extends BaseMapperX<PurchaseInboundDO> {

    default PageResult<PurchaseInboundDO> selectPage(SearchPurchaseInboundDTO searchDTO) {
        return selectPage(searchDTO, new LambdaQueryWrapperX<PurchaseInboundDO>()
                .likeIfPresent(PurchaseInboundDO::getCode, searchDTO.getCode())
                .eqIfPresent(PurchaseInboundDO::getBusinessType, searchDTO.getBusinessType())
                .eqIfPresent(PurchaseInboundDO::getBusinessStatus, searchDTO.getBusinessStatus())
                .eqIfPresent(PurchaseInboundDO::getDocumentStatus, searchDTO.getDocumentStatus())
                .betweenIfPresent(PurchaseInboundDO::getInboundDate, searchDTO.getInboundDate())
                .eqIfPresent(PurchaseInboundDO::getWarehouseId, searchDTO.getWarehouseId())
//                .eqIfPresent(PurchaseInboundDO::getSupplierId, searchDTO.getSupplierId())
                .eqIfPresent(PurchaseInboundDO::getBusinessUserId, searchDTO.getBusinessUserId())
                .orderByDesc(PurchaseInboundDO::getId));
    }

    default void updateBusinessStatusById(Integer businessStatus, Long id) {
        PurchaseInboundDO updateObj = new PurchaseInboundDO();
        updateObj.setBusinessStatus(businessStatus);
        updateObj.setId(id);
        updateById(updateObj);
    }

    default void updateApproveStatusById(Integer documentStatus, Long id) {
        PurchaseInboundDO updateObj = new PurchaseInboundDO();
        updateObj.setDocumentStatus(documentStatus);
        updateObj.setId(id);
        updateObj.setAuditTime(LocalDateTime.now());
        updateById(updateObj);
    }

    default Long countBySupplierId(Long supplierId) {
//        return selectCount(PurchaseInboundDO::getSupplierId, supplierId);
        return null;
    }

    default Long countByWarehouseId(Long warehouseId) {
        return selectCount(PurchaseInboundDO::getWarehouseId, warehouseId);
    }

}
