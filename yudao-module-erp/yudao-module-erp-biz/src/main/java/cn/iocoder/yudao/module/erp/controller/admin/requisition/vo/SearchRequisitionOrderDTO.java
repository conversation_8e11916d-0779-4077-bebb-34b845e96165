package cn.iocoder.yudao.module.erp.controller.admin.requisition.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 领料单分页 Request DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchRequisitionOrderDTO extends PageParam {

    @Schema(description = "领料单号", example = "LL202507001")
    private String code;

    @Schema(description = "领料用途", example = "生产用料")
    private String purpose;

    @Schema(description = "业务部门ID", example = "1001")
    private Long businessDepartmentId;

    @Schema(description = "业务员ID", example = "1001")
    private Long businessUserId;

    @Schema(description = "仓库ID", example = "1001")
    private Long warehouseId;

    @Schema(description = "仓库名称", example = "主仓库")
    private String warehouseName;

    @Schema(description = "单据状态", example = "1")
    private Integer documentStatus;

    @Schema(description = "业务状态", example = "1")
    private Integer businessStatus;

    @Schema(description = "领料日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] requisitionDate;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
