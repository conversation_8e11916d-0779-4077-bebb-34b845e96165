package cn.iocoder.yudao.module.erp.controller.admin.requisition.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 领料单简单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SimpleRequisitionOrderVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "领料单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "LL202507001")
    @ExcelProperty("领料单号")
    private String code;

    @Schema(description = "领料用途", example = "生产用料")
    @ExcelProperty("领料用途")
    private String purpose;

    @Schema(description = "领料日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("领料日期")
    private LocalDateTime requisitionDate;

    @Schema(description = "仓库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long warehouseId;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "主仓库")
    @ExcelProperty("仓库")
    private String warehouseName;

    @Schema(description = "业务部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long businessDepartmentId;

    @Schema(description = "业务部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "生产部")
    @ExcelProperty("业务部门")
    private String businessDepartmentName;

    @Schema(description = "业务员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long businessUserId;

    @Schema(description = "业务员姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("业务员")
    private String businessUserName;

    @Schema(description = "单据状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("单据状态")
    private Integer documentStatus;

    @Schema(description = "业务状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("业务状态")
    private Integer businessStatus;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "requisitionOrder")
    private String businessType;
    
    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "备注", example = "备注信息")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}
