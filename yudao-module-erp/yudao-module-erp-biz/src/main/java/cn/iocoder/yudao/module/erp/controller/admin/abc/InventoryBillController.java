package cn.iocoder.yudao.module.erp.controller.admin.abc;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;import db.migration.PermissionName;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.erp.controller.admin.abc.vo.*;
import cn.iocoder.yudao.module.erp.service.abc.InventoryBillService;

@Tag(name = "管理后台 - 出入库清单")
@RestController
@RequestMapping("/erp/inventory-bill")
@Validated
public class InventoryBillController {

    @Resource
    private InventoryBillService inventoryBillService;

    @PostMapping
    @Operation(summary = "创建出入库清单")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:create')")
    @PermissionName(name = "创建出入库清单", sort = 1)
    public CommonResult<Long> create(@Valid @RequestBody CreateInventoryBillDTO createDTO) {
        return success(inventoryBillService.create(createDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新出入库清单")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:update')")
    @PermissionName(name = "更新出入库清单", sort = 2)
    public CommonResult<Boolean> update(@PathVariable("id") Long id, @Valid @RequestBody UpdateInventoryBillDTO updateDTO) {
        inventoryBillService.update(id, updateDTO);
        return success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除出入库清单")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:delete')")
    @PermissionName(name = "删除出入库清单", sort = 3)
    public CommonResult<Boolean> delete(@PathVariable("id") Long id) {
        inventoryBillService.delete(id);
        return success(true);
    }

    @GetMapping("/{id}")
    @Operation(summary = "查看出入库清单详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:view')")
    @PermissionName(name = "查看出入库清单详情", sort = 4)
    public CommonResult<InventoryBillVO> get(@PathVariable("id") Long id) {
        return success(inventoryBillService.get(id));
    }

    @GetMapping("/page")
    @Operation(summary = "查询出入库清单分页")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:query')")
    @PermissionName(name = "查询出入库清单-单据模式", sort = 8)
    public CommonResult<PageResult<SimpleInventoryBillVO>> page(@Valid SearchInventoryBillDTO searchDTO) {
        PageResult<SimpleInventoryBillVO> pageResult = inventoryBillService.page(searchDTO);
        return success(pageResult);
    }



    // ==================== 子表（通用库存单据明细） ====================

    @GetMapping("/inventory-bill-item/page")
    @Operation(summary = "获得通用库存单据明细分页")
    @Parameter(name = "billId", description = "出入库单ID")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:query')")
    public CommonResult<PageResult<InventoryBillItemDO>> getInventoryBillItemPage(PageParam pageReqVO,
                                                                                        @RequestParam("billId") Long billId) {
        return success(inventoryBillService.getInventoryBillItemPage(pageReqVO, billId));
    }

    @PostMapping("/inventory-bill-item/create")
    @Operation(summary = "创建通用库存单据明细")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:create')")
    public CommonResult<Long> createInventoryBillItem(@Valid @RequestBody InventoryBillItemDO inventoryBillItem) {
        return success(inventoryBillService.createInventoryBillItem(inventoryBillItem));
    }

    @PutMapping("/inventory-bill-item/update")
    @Operation(summary = "更新通用库存单据明细")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:update')")
    public CommonResult<Boolean> updateInventoryBillItem(@Valid @RequestBody InventoryBillItemDO inventoryBillItem) {
        inventoryBillService.updateInventoryBillItem(inventoryBillItem);
        return success(true);
    }

    @DeleteMapping("/inventory-bill-item/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除通用库存单据明细")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:delete')")
    public CommonResult<Boolean> deleteInventoryBillItem(@RequestParam("id") Long id) {
        inventoryBillService.deleteInventoryBillItem(id);
        return success(true);
    }

	@GetMapping("/inventory-bill-item/get")
	@Operation(summary = "获得通用库存单据明细")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:query')")
	public CommonResult<InventoryBillItemDO> getInventoryBillItem(@RequestParam("id") Long id) {
	    return success(inventoryBillService.getInventoryBillItem(id));
	}


    // ==================== 文档操作接口 ====================

    @PutMapping("/close/{id}")
    @Operation(summary = "关闭出入库清单")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:close')")
    @PermissionName(name = "关闭/取消关闭出入库清单", sort = 6)
    public CommonResult<Boolean> close(@PathVariable("id") Long id) {
        inventoryBillService.close(id);
        return success(true);
    }

    @PutMapping("/cancel-close/{id}")
    @Operation(summary = "取消关闭出入库清单")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:close')")
    public CommonResult<Boolean> cancelClose(@PathVariable("id") Long id) {
        inventoryBillService.cancelClose(id);
        return success(true);
    }

    @PutMapping("/suspend/{id}")
    @Operation(summary = "挂起出入库清单")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:suspend')")
    @PermissionName(name = "挂起/取消挂起出入库清单", sort = 7)
    public CommonResult<Boolean> suspend(@PathVariable("id") Long id) {
        inventoryBillService.suspend(id);
        return success(true);
    }

    @PutMapping("/cancel-suspend/{id}")
    @Operation(summary = "取消挂起出入库清单")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:suspend')")
    public CommonResult<Boolean> cancelSuspend(@PathVariable("id") Long id) {
        inventoryBillService.cancelSuspend(id);
        return success(true);
    }

    @PutMapping("/approve/{id}")
    @Operation(summary = "审核出入库清单")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:approve')")
    @PermissionName(name = "审核/取消审核出入库清单", sort = 5)
    public CommonResult<Boolean> approve(@PathVariable("id") Long id) {
        inventoryBillService.approve(id);
        return success(true);
    }

    @PutMapping("/cancel-approve/{id}")
    @Operation(summary = "取消审核出入库清单")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:approve')")
    public CommonResult<Boolean> cancelApprove(@PathVariable("id") Long id) {
        inventoryBillService.cancelApprove(id);
        return success(true);
    }

    @GetMapping("/item/page")
    @Operation(summary = "查询出入库清单明细分页")
    @PreAuthorize("@ss.hasPermission('erp:inventory-bill:item:query')")
    @PermissionName(name = "查询出入库清单-明细模式", sort = 8)
    public CommonResult<PageResult<ItemWithInventoryBillVO>> pageItem(@Valid SearchItemPageDTO searchDTO) {
        PageResult<ItemWithInventoryBillVO> pageResult = inventoryBillService.pageItem(searchDTO);
        return success(pageResult);
    }


}