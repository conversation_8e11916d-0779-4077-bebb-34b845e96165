package cn.iocoder.yudao.module.erp.dal.dataobject.inbound.returned;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
public class ReturnedInboundQuantity {
    /**
     * 退货入库-明细ID
     */
    private Long returnedInboundItemId;

    /**
     * 退货通知单-明细ID
     */
    private Long returnedOrderItemId;

    /**
     * 入库数量
     */
    private BigDecimal quantity;
}
