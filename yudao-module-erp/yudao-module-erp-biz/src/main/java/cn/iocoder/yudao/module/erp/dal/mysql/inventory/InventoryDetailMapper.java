package cn.iocoder.yudao.module.erp.dal.mysql.inventory;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.erp.constant.StockOperationTypeEnum;
import cn.iocoder.yudao.module.erp.controller.admin.inventory.vo.SearchInventoryDetailDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryChange;
import cn.iocoder.yudao.module.erp.dal.dataobject.inventory.InventoryDetailDO;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 库存明细记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryDetailMapper extends BaseMapperX<InventoryDetailDO> {

    default PageResult<InventoryDetailDO> selectPage(SearchInventoryDetailDTO searchDTO) {
        return selectPage(searchDTO, new LambdaQueryWrapperX<InventoryDetailDO>()
                .eqIfPresent(InventoryDetailDO::getWarehouseId, searchDTO.getWarehouseId())
                .eqIfPresent(InventoryDetailDO::getWarehousePositionId, searchDTO.getWarehousePositionId())
                .eqIfPresent(InventoryDetailDO::getOperationType, searchDTO.getOperationType())
                .eqIfPresent(InventoryDetailDO::getDocumentType, searchDTO.getDocumentType())
                .eqIfPresent(InventoryDetailDO::getDocumentId, searchDTO.getDocumentId())
                .eqIfPresent(InventoryDetailDO::getBusinessType, searchDTO.getBusinessType())
                .eqIfPresent(InventoryDetailDO::getBusinessItemId, searchDTO.getBusinessItemId())
                .eqIfPresent(InventoryDetailDO::getMaterialId, searchDTO.getMaterialId())
                .likeIfPresent(InventoryDetailDO::getMaterialName, searchDTO.getMaterialName())
                .eqIfPresent(InventoryDetailDO::getMaterialCode, searchDTO.getMaterialCode())
                .eqIfPresent(InventoryDetailDO::getMaterialSpec, searchDTO.getMaterialSpec())
                .eqIfPresent(InventoryDetailDO::getMaterialUom, searchDTO.getMaterialUom())
                .eqIfPresent(InventoryDetailDO::getInQuantity, searchDTO.getInQuantity())
                .eqIfPresent(InventoryDetailDO::getOutQuantity, searchDTO.getOutQuantity())
                .eqIfPresent(InventoryDetailDO::getBatchNo, searchDTO.getBatchNo())
                .eqIfPresent(InventoryDetailDO::getPackagingTypeId, searchDTO.getPackagingTypeId())
                .likeIfPresent(InventoryDetailDO::getPackagingTypeName, searchDTO.getPackagingTypeName())
                .eqIfPresent(InventoryDetailDO::getPackagingSpecId, searchDTO.getPackagingSpecId())
                .likeIfPresent(InventoryDetailDO::getPackagingSpecName, searchDTO.getPackagingSpecName())
                .eqIfPresent(InventoryDetailDO::getPackagingOwner, searchDTO.getPackagingOwner())
                .eqIfPresent(InventoryDetailDO::getPackagingCompanyId, searchDTO.getPackagingCompanyId())
                .eqIfPresent(InventoryDetailDO::getPackagingCode, searchDTO.getPackagingCode())
                .eqIfPresent(InventoryDetailDO::getPlateNumber, searchDTO.getPlateNumber())
                .eqIfPresent(InventoryDetailDO::getGrossWeight, searchDTO.getGrossWeight())
                .eqIfPresent(InventoryDetailDO::getTareWeight, searchDTO.getTareWeight())
                .betweenIfPresent(InventoryDetailDO::getFillingDate, searchDTO.getFillingDate())
                .eqIfPresent(InventoryDetailDO::getFillingUserId, searchDTO.getFillingUserId())
                .eqIfPresent(InventoryDetailDO::getRemark, searchDTO.getRemark())
                .betweenIfPresent(InventoryDetailDO::getCreateTime, searchDTO.getCreateTime())
                .openBetweenClose(InventoryDetailDO::getAvailableQuantity, searchDTO.getAvailableQuantity())
                .orderByDesc(InventoryDetailDO::getId));
    }

    default void batchDecreaseAvailableQuantity(List<InventoryChange> inventoryChangeList) {
        if (CollUtil.isEmpty(inventoryChangeList)) {
            return;
        }
        inventoryChangeList.forEach(item -> {
            update(new UpdateWrapper<InventoryDetailDO>()
                    .setSql("available_quantity = available_quantity - " + item.getChangeQuantity())
                    .eq("id", item.getFromInventoryId()));
        });
    }

    default void batchIncreaseAvailableQuantity(List<InventoryChange> inventoryChangeList) {
        if (CollUtil.isEmpty(inventoryChangeList)) {
            return;
        }
        inventoryChangeList.forEach(item -> {
            update(new UpdateWrapper<InventoryDetailDO>()
                    .setSql("available_quantity = available_quantity + " + item.getChangeQuantity())
                    .eq("id", item.getFromInventoryId()));
        });
    }

    default List<InventoryDetailDO> selectByDocumentTypeAndId(Integer type, Long id) {
        return selectList(new LambdaQueryWrapperX<InventoryDetailDO>()
                .eq(InventoryDetailDO::getDocumentType, type)
                .eq(InventoryDetailDO::getDocumentId, id));
    }

    default void deleteByDocumentAndBusinessTypeAndId(Integer documentType, Long documentId, String businessType, List<Long> businessItemIds) {
        delete(new LambdaQueryWrapperX<InventoryDetailDO>()
                .eq(InventoryDetailDO::getDocumentType, documentType)
                .eq(InventoryDetailDO::getDocumentId, documentId)
                .eq(InventoryDetailDO::getBusinessType, businessType)
                .in(InventoryDetailDO::getBusinessItemId, businessItemIds));
    }

    default Long countByMaterialId(Long materialId) {
        return selectCount(InventoryDetailDO::getMaterialId, materialId);
    }

    default List<InventoryDetailDO> selectByDocumentTypeAndBusinessTypeAndId(Integer documentType, Long documentId, String businessType, List<Long> businessItemIds) {
        return selectList(new LambdaQueryWrapperX<InventoryDetailDO>()
                .eq(InventoryDetailDO::getDocumentType, documentType)
                .eq(InventoryDetailDO::getDocumentId, documentId)
                .eq(InventoryDetailDO::getBusinessType, businessType)
                .in(InventoryDetailDO::getBusinessItemId, businessItemIds));
    }

    default Long countOutboundByInventoryIds(List<Long> inventoryIds) {
        return selectCount(new LambdaQueryWrapperX<InventoryDetailDO>()
                .eq(InventoryDetailDO::getOperationType, StockOperationTypeEnum.OUT.getType())
                .in(InventoryDetailDO::getFromInventoryId, inventoryIds));
    }

    default Long countByWarehouseId(Long warehouseId) {
        return selectCount(InventoryDetailDO::getWarehouseId, warehouseId);
    }

    default Long countByUniqueCode(String uniqueCode) {
        return selectCount(InventoryDetailDO::getMaterialUniqueCode, uniqueCode);
    }
}