package cn.iocoder.yudao.module.erp.controller.admin.abc;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;import db.migration.PermissionName;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.erp.controller.admin.abc.vo.*;
import cn.iocoder.yudao.module.erp.service.abc.PurchaseReturnedOutboundService;

@Tag(name = "管理后台 - 采购退货出库单")
@RestController
@RequestMapping("/erp/purchase-returned-outbound")
@Validated
public class PurchaseReturnedOutboundController {

    @Resource
    private PurchaseReturnedOutboundService purchaseReturnedOutboundService;

    @PostMapping
    @Operation(summary = "创建采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:create')")
    @PermissionName(name = "创建采购退货出库单", sort = 1)
    public CommonResult<Long> create(@Valid @RequestBody CreatePurchaseReturnedOutboundDTO createDTO) {
        return success(purchaseReturnedOutboundService.create(createDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:update')")
    @PermissionName(name = "更新采购退货出库单", sort = 2)
    public CommonResult<Boolean> update(@PathVariable("id") Long id, @Valid @RequestBody UpdatePurchaseReturnedOutboundDTO updateDTO) {
        purchaseReturnedOutboundService.update(id, updateDTO);
        return success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:delete')")
    @PermissionName(name = "删除采购退货出库单", sort = 3)
    public CommonResult<Boolean> delete(@PathVariable("id") Long id) {
        purchaseReturnedOutboundService.delete(id);
        return success(true);
    }

    @GetMapping("/{id}")
    @Operation(summary = "查看采购退货出库单详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:view')")
    @PermissionName(name = "查看采购退货出库单详情", sort = 4)
    public CommonResult<PurchaseReturnedOutboundVO> get(@PathVariable("id") Long id) {
        return success(purchaseReturnedOutboundService.get(id));
    }

    @GetMapping("/page")
    @Operation(summary = "查询采购退货出库单分页")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:query')")
    @PermissionName(name = "查询采购退货出库单-单据模式", sort = 8)
    public CommonResult<PageResult<SimplePurchaseReturnedOutboundVO>> page(@Valid SearchPurchaseReturnedOutboundDTO searchDTO) {
        PageResult<SimplePurchaseReturnedOutboundVO> pageResult = purchaseReturnedOutboundService.page(searchDTO);
        return success(pageResult);
    }



    // ==================== 子表（采购退货出库单明细） ====================

    @GetMapping("/purchase-returned-outbound-item/page")
    @Operation(summary = "获得采购退货出库单明细分页")
    @Parameter(name = "returnedOutboundId", description = "出入库单ID")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:query')")
    public CommonResult<PageResult<PurchaseReturnedOutboundItemDO>> getPurchaseReturnedOutboundItemPage(PageParam pageReqVO,
                                                                                        @RequestParam("returnedOutboundId") Long returnedOutboundId) {
        return success(purchaseReturnedOutboundService.getPurchaseReturnedOutboundItemPage(pageReqVO, returnedOutboundId));
    }

    @PostMapping("/purchase-returned-outbound-item/create")
    @Operation(summary = "创建采购退货出库单明细")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:create')")
    public CommonResult<Long> createPurchaseReturnedOutboundItem(@Valid @RequestBody PurchaseReturnedOutboundItemDO purchaseReturnedOutboundItem) {
        return success(purchaseReturnedOutboundService.createPurchaseReturnedOutboundItem(purchaseReturnedOutboundItem));
    }

    @PutMapping("/purchase-returned-outbound-item/update")
    @Operation(summary = "更新采购退货出库单明细")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:update')")
    public CommonResult<Boolean> updatePurchaseReturnedOutboundItem(@Valid @RequestBody PurchaseReturnedOutboundItemDO purchaseReturnedOutboundItem) {
        purchaseReturnedOutboundService.updatePurchaseReturnedOutboundItem(purchaseReturnedOutboundItem);
        return success(true);
    }

    @DeleteMapping("/purchase-returned-outbound-item/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除采购退货出库单明细")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:delete')")
    public CommonResult<Boolean> deletePurchaseReturnedOutboundItem(@RequestParam("id") Long id) {
        purchaseReturnedOutboundService.deletePurchaseReturnedOutboundItem(id);
        return success(true);
    }

	@GetMapping("/purchase-returned-outbound-item/get")
	@Operation(summary = "获得采购退货出库单明细")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:query')")
	public CommonResult<PurchaseReturnedOutboundItemDO> getPurchaseReturnedOutboundItem(@RequestParam("id") Long id) {
	    return success(purchaseReturnedOutboundService.getPurchaseReturnedOutboundItem(id));
	}


    // ==================== 文档操作接口 ====================

    @PutMapping("/close/{id}")
    @Operation(summary = "关闭采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:close')")
    @PermissionName(name = "关闭/取消关闭采购退货出库单", sort = 6)
    public CommonResult<Boolean> close(@PathVariable("id") Long id) {
        purchaseReturnedOutboundService.close(id);
        return success(true);
    }

    @PutMapping("/cancel-close/{id}")
    @Operation(summary = "取消关闭采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:close')")
    public CommonResult<Boolean> cancelClose(@PathVariable("id") Long id) {
        purchaseReturnedOutboundService.cancelClose(id);
        return success(true);
    }

    @PutMapping("/suspend/{id}")
    @Operation(summary = "挂起采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:suspend')")
    @PermissionName(name = "挂起/取消挂起采购退货出库单", sort = 7)
    public CommonResult<Boolean> suspend(@PathVariable("id") Long id) {
        purchaseReturnedOutboundService.suspend(id);
        return success(true);
    }

    @PutMapping("/cancel-suspend/{id}")
    @Operation(summary = "取消挂起采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:suspend')")
    public CommonResult<Boolean> cancelSuspend(@PathVariable("id") Long id) {
        purchaseReturnedOutboundService.cancelSuspend(id);
        return success(true);
    }

    @PutMapping("/approve/{id}")
    @Operation(summary = "审核采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:approve')")
    @PermissionName(name = "审核/取消审核采购退货出库单", sort = 5)
    public CommonResult<Boolean> approve(@PathVariable("id") Long id) {
        purchaseReturnedOutboundService.approve(id);
        return success(true);
    }

    @PutMapping("/cancel-approve/{id}")
    @Operation(summary = "取消审核采购退货出库单")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:approve')")
    public CommonResult<Boolean> cancelApprove(@PathVariable("id") Long id) {
        purchaseReturnedOutboundService.cancelApprove(id);
        return success(true);
    }

    @GetMapping("/item/page")
    @Operation(summary = "查询采购退货出库单明细分页")
    @PreAuthorize("@ss.hasPermission('erp:purchase-returned-outbound:item:query')")
    @PermissionName(name = "查询采购退货出库单-明细模式", sort = 8)
    public CommonResult<PageResult<ItemWithPurchaseReturnedOutboundVO>> pageItem(@Valid SearchItemPageDTO searchDTO) {
        PageResult<ItemWithPurchaseReturnedOutboundVO> pageResult = purchaseReturnedOutboundService.pageItem(searchDTO);
        return success(pageResult);
    }


}