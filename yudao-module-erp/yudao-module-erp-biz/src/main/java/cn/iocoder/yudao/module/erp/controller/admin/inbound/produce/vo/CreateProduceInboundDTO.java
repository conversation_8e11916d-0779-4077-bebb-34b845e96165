package cn.iocoder.yudao.module.erp.controller.admin.inbound.produce.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 生产入库新增 DTO")
@Data
public class CreateProduceInboundDTO {

    @Schema(description = "仓库id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20092")
    @NotNull(message = "仓库id不能为空")
    private Long warehouseId;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @Schema(description = "业务状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "业务状态不能为空")
    private Integer businessStatus;

    @Schema(description = "单据状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "单据状态不能为空")
    private Integer documentStatus;

    @Schema(description = "入库日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "入库日期不能为空")
    private LocalDateTime inboundDate;

    @Schema(description = "业务部门", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务部门不能为空")
    private Long businessDepartment;

    @Schema(description = "业务员", requiredMode = Schema.RequiredMode.REQUIRED, example = "9803")
    @NotNull(message = "业务员不能为空")
    private Long businessUserId;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "生产入库行-明细列表")
    @NotNull(message = "生产入库行-明细列表不能为空")
    @Size(min = 1, message = "生产入库行-明细列表不能为空")
    private List<ProduceInboundLineDTO> produceInboundLines;

}