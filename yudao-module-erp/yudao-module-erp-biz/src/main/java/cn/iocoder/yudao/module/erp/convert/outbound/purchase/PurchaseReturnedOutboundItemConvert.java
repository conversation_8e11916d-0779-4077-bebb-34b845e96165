package cn.iocoder.yudao.module.erp.convert.outbound.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.ItemWithPurchaseReturnedOutboundVO;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.PurchaseReturnedOutboundItemDTO;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.PurchaseReturnedOutboundItemVO;
import cn.iocoder.yudao.module.erp.dal.dataobject.outbound.purchase.PurchaseReturnedOutboundItemDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.outbound.purchase.view.ItemWithPurchaseReturnedOutbound;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PurchaseReturnedOutboundItemConvert {

    @AfterMapping
    default void setDefaultValues(@MappingTarget PurchaseReturnedOutboundItemDTO itemDO) {

    }

    PurchaseReturnedOutboundItemDO toDO(PurchaseReturnedOutboundItemDTO purchasereturnedoutbounditemDTO);

    PurchaseReturnedOutboundItemVO toVO(PurchaseReturnedOutboundItemDO purchasereturnedoutbounditemDO);

    List<PurchaseReturnedOutboundItemVO> toVO(List<PurchaseReturnedOutboundItemDO> purchasereturnedoutbounditemDOList);

    List<PurchaseReturnedOutboundItemDO> toDO(List<PurchaseReturnedOutboundItemDTO> purchasereturnedoutbounditemDTOList);

    /**
     * 转换分页结果
     */
    default PageResult<ItemWithPurchaseReturnedOutboundVO> convertPage(PageResult<ItemWithPurchaseReturnedOutbound> pageResult) {
        return BeanUtils.toBean(pageResult, ItemWithPurchaseReturnedOutboundVO.class);
    }
}