package cn.iocoder.yudao.module.erp.controller.admin.carrier.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 承运商档案分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchCarrierPageDTO extends PageParam {

    @Schema(description = "编号")
    private String code;

    @Schema(description = "名称", example = "赵六")
    private String name;

    @Schema(description = "状态", example = "0")
    private Integer status;
}