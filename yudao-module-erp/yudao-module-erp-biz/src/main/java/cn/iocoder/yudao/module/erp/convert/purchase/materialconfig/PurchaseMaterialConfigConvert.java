package cn.iocoder.yudao.module.erp.convert.purchase.materialconfig;

import cn.iocoder.yudao.module.erp.controller.admin.purchase.materialconfig.vo.*;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.materialconfig.PurchaseMaterialConfigDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.purchase.materialconfig.view.PurchaseMaterialConfigWithMaterialVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PurchaseMaterialConfigConvert {

    PurchaseMaterialConfigDO toDO(SavePurchaseMaterialConfigDTO saveDTO);

    @Mapping(source = "code", target = "materialCode")
    @Mapping(source = "name", target = "materialName")
    @Mapping(source = "unitOfMeasure", target = "unitOfMeasure")
    @Mapping(source = "specification", target = "specification")
    @Mapping(source = "categoryName", target = "categoryName")
    SimplePurchaseMaterialConfigVO toSimpleVO(PurchaseMaterialConfigWithMaterialVO configWithMaterial);

    List<SimplePurchaseMaterialConfigVO> toSimpleVO(List<PurchaseMaterialConfigWithMaterialVO> configWithMaterialList);

    @Mapping(source = "code", target = "materialCode")
    @Mapping(source = "name", target = "materialName")
    @Mapping(source = "specification", target = "materialSpec")
    @Mapping(source = "unitOfMeasure", target = "materialUom")
    @Mapping(source = "status", target = "materialStatus")
    PurchaseMaterialConfigVO toVO(PurchaseMaterialConfigWithMaterialVO configWithMaterial);

    @Mapping(target = "materialCode", ignore = true)
    @Mapping(target = "materialName", ignore = true)
    @Mapping(target = "materialSpec", ignore = true)
    @Mapping(target = "materialUom", ignore = true)
    @Mapping(target = "categoryId", ignore = true)
    @Mapping(target = "materialStatus", ignore = true)
    PurchaseMaterialConfigVO toVO(PurchaseMaterialConfigDO purchaseMaterialConfigDO);

}
