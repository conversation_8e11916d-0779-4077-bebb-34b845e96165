package cn.iocoder.yudao.module.erp.dal.dataobject.requisition;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.DocumentDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

/**
 * 领料单 DO
 *
 * <AUTHOR>
 */
@TableName("erp_requisition_order")
@KeySequence("erp_requisition_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RequisitionOrderDO extends DocumentDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 领料用途
     */
    private String purpose;

    /**
     * 领料日期
     */
    private LocalDate requisitionDate;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 业务部门ID
     */
    private Long businessDepartmentId;

    /**
     * 业务部门名称
     */
    private String businessDepartmentName;

    /**
     * 业务员ID
     */
    private Long businessUserId;

    /**
     * 业务员姓名
     */
    private String businessUserName;

    /**
     * 备注
     */
    private String remark;

}
