package cn.iocoder.yudao.module.erp.controller.admin.abc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 出入库清单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryBillVO extends SimpleInventoryBillVO {

    @Schema(description = "出入库清单明细")
    private List<InventoryBillItemVO> items;

}
