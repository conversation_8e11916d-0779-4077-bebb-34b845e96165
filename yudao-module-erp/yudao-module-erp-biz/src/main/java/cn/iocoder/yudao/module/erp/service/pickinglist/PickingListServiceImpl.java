package cn.iocoder.yudao.module.erp.service.pickinglist;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.BusinessStatusEnum;
import cn.iocoder.yudao.module.erp.constant.BusinessTypeEnum;
import cn.iocoder.yudao.module.erp.constant.DocumentStatusEnum;
import cn.iocoder.yudao.module.erp.controller.admin.packaging.vo.SpecWithPackagingTypeVO;
import cn.iocoder.yudao.module.erp.controller.admin.pickinglist.vo.*;
import cn.iocoder.yudao.module.erp.convert.pickinglist.PickingListConvert;
import cn.iocoder.yudao.module.erp.convert.pickinglist.PickingListLineConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.pickinglist.PickingListDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.pickinglist.PickingListLineDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.pickinglist.view.LineWithPickingList;
import cn.iocoder.yudao.module.erp.dal.mysql.pickinglist.PickingListLineMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.pickinglist.PickingListMapper;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataRefer;
import cn.iocoder.yudao.module.erp.service.base.material.MaterialMasterDataService;
import cn.iocoder.yudao.module.erp.service.common.AdminUserUtil;
import cn.iocoder.yudao.module.erp.service.common.DocumentServiceUtil;
import cn.iocoder.yudao.module.erp.service.common.SerialNumberService;
import cn.iocoder.yudao.module.erp.service.customer.CustomerRefer;
import cn.iocoder.yudao.module.erp.service.customer.CustomerService;
import cn.iocoder.yudao.module.erp.service.packaging.PackagingTypeService;
import cn.iocoder.yudao.module.erp.service.shipping.ShippingService;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.*;

/**
 * 备货通知单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PickingListServiceImpl implements PickingListService, MaterialMasterDataRefer, CustomerRefer {

    @Resource
    private PickingListMapper pickingListMapper;

    @Resource
    private PickingListLineMapper pickingListLineMapper;

    @Resource
    private PickingListConvert pickinglistConvert;

    @Resource
    private PickingListLineConvert pickingListLineConvert;

    @Resource
    private SerialNumberService serialNumberService;

    @Resource
    @Lazy
    private CustomerService customerService;

    @Resource
    @Lazy
    private MaterialMasterDataService materialMasterDataService;

    @Resource
    @Lazy
    private PackagingTypeService packagingTypeService;

    @Resource
    private AdminUserUtil adminUserUtil;

    @Resource
    @Lazy
    private ShippingService shippingService;

    @Resource
    @Lazy
    private DocumentServiceUtil documentServiceUtil;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(CreatePickingListDTO createDTO) {
        validateCustomerAndPackaging(createDTO.getCustomerId(), createDTO.getLines());

        PickingListDO pickingList = pickinglistConvert.toDO(createDTO);
        pickingList.setCode(serialNumberService.generateOrderNumber(BusinessTypeEnum.PICKING_LIST.getPrefix()));
        pickingList.setDocumentStatus(DocumentStatusEnum.DRAFT.getStatus());
        pickingList.setBusinessStatus(BusinessStatusEnum.NORMAL.getStatus());
        pickingList.setBusinessType(BusinessTypeEnum.PICKING_LIST.getType());

        pickingListMapper.insert(pickingList);
        Long id = pickingList.getId();

        List<PickingListLineDO> pickingListLines = pickingListLineConvert.toDO(createDTO.getLines());
        if (CollUtil.isNotEmpty(pickingListLines)) {
            pickingListLines.forEach(item -> {
                item.setPickingListId(id);
            });
            pickingListLineMapper.insertBatch(pickingListLines);
        }
        return id;
    }

    private void validateCustomerAndPackaging(Long customerId, List<? extends PickingListLineDTO> lines) {
        documentServiceUtil.validateCustomer(customerId);

        List<Long> materialIds = lines.stream()
                .map(PickingListLineDTO::getMaterialId)
                .distinct()
                .toList();

        materialMasterDataService.validateEnableAndIsPackagedProduct(materialIds);

        List<Long> packagingSpecIds = lines.stream()
                .map(PickingListLineDTO::getPackagingSpecId)
                .distinct()
                .toList();

        List<SpecWithPackagingTypeVO> specWithPackagingTypes = packagingTypeService.listSpecWithPackagingType(packagingSpecIds);

        Map<Long, SpecWithPackagingTypeVO> specMap = specWithPackagingTypes.stream()
                .collect(Collectors.toMap(SpecWithPackagingTypeVO::getId, vo -> vo, (existing, replacement) -> existing));

        for (PickingListLineDTO line : lines) {
            Long specId = line.getPackagingSpecId();
            SpecWithPackagingTypeVO specVO = specMap.get(specId);
            if (specVO == null) {
                throw exception(PACKAGING_SPEC_NOT_EXISTS, specId);
            }
            packagingTypeService.validateSpecAndTypeEnable(specVO);
            packagingTypeService.validateSpecCapacity(specVO, line.getSingleBottleFillingCapacity());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Long id, UpdatePickingListDTO updateDTO) {
        // 校验存在
        validatePickingListExists(id);
        //检验是否可用编辑
        validatePickingListCanEdit(id);

        validateCustomerAndPackaging(updateDTO.getCustomerId(), updateDTO.getLines());

        List<PickingListLineDTO> lines = updateDTO.getLines();
        List<PickingListLineDO> pickingListLines = pickingListLineConvert.toDO(lines);
        for (PickingListLineDO pickingListLine : pickingListLines) {
            pickingListLine.setPickingListId(id);
        }

        //从pickingListLines中获取新增的list
        List<PickingListLineDO> newPickingListLines = pickingListLines.stream().filter(item -> item.getId() == null).toList();
        //从pickingListLines中获取更新的list
        List<PickingListLineDO> updatePickingListLines = pickingListLines.stream().filter(item -> item.getId() != null).toList();
        //根据id查询所有现存的订单行记录。
        List<PickingListLineDO> existPickingListLines = pickingListLineMapper.selectListByPickingListId(id);
        //比对existPickingListLines和pickingListLines,获取删除的lineId集合。比较规则，id相等
        List<Long> deletePickingListLineIds = existPickingListLines.stream()
                .map(PickingListLineDO::getId)
                .filter(itemId -> updatePickingListLines.stream()
                        .noneMatch(item2 -> item2.getId().equals(itemId))).toList();

        if (CollUtil.isNotEmpty(deletePickingListLineIds)) {
            pickingListLineMapper.deleteByIds(deletePickingListLineIds);
        }

        if (CollUtil.isNotEmpty(newPickingListLines)) {
            pickingListLineMapper.insertBatch(newPickingListLines);
        }

        if (CollUtil.isNotEmpty(updatePickingListLines)) {
            pickingListLineMapper.updateBatch(updatePickingListLines);
        }

        // 更新
        PickingListDO updateObj = pickinglistConvert.toDO(updateDTO);
        updateObj.setId(id);
        pickingListMapper.updateById(updateObj);
    }

    private void validatePickingListCanEdit(Long id) {
        PickingListDO pickingListDO = pickingListMapper.selectById(id);
        if (DocumentStatusEnum.DRAFT.getStatus().equals(pickingListDO.getDocumentStatus())) {
            return;
        }
        if (DocumentStatusEnum.ROLLBACK.getStatus().equals(pickingListDO.getDocumentStatus())) {
            return;
        }
        throw exception(PICKING_LIST_NOT_EDITABLE);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        // 校验存在
        validatePickingListExists(id);
        validatePickingListCanEdit(id);
        // 删除
        pickingListMapper.deleteById(id);
        pickingListLineMapper.deleteByPickingListId(id);
    }

    private void validatePickingListExists(Long id) {
        if (pickingListMapper.selectById(id) == null) {
            throw exception(PICKING_LIST_NOT_EXISTS, id);
        }
    }

    @Override
    public PickingListVO get(Long id) {
        validatePickingListExists(id);
        PickingListDO pickingListDO = pickingListMapper.selectById(id);
        PickingListVO vo = pickinglistConvert.toVO(pickingListDO);
        Long pickingListId = vo.getId();
        List<PickingListLineDO> pickingListLineDOS = pickingListLineMapper.selectListByPickingListId(pickingListId);
        List<PickingListLineVO> pickingListLineVOS = pickingListLineConvert.toVO(pickingListLineDOS);
        vo.setLines(pickingListLineVOS);
        return vo;
    }

    @Override
    public PageResult<SimplePickingListVO> page(SearchPickingListPageDTO pageReqVO) {
        PageResult<PickingListDO> pickingListDOPageResult = pickingListMapper.selectPage(pageReqVO);
        List<PickingListDO> list = pickingListDOPageResult.getList();
        List<SimplePickingListVO> vo = pickinglistConvert.toSimpleVO(list);
        Map<Long, AdminUserRespDTO> adminUserMap = new HashMap<>();
        //找到销售订单的businessUserId
        List<Long> businessUserIds = list.stream().map(PickingListDO::getBusinessUserId).toList();
        if (CollUtil.isNotEmpty(businessUserIds)) {
            adminUserMap = adminUserUtil.listUserMap(businessUserIds);
        }
        for (SimplePickingListVO simplePickingListVO : vo) {
            simplePickingListVO.setBusinessUserName(adminUserMap.get(simplePickingListVO.getBusinessUserId()) == null ? null : adminUserMap.get(simplePickingListVO.getBusinessUserId()).getNickname());
        }

        return new PageResult<>(vo, pickingListDOPageResult.getTotal());
    }


    @Override
    public void approve(Long id) {
        validatePickingListExists(id);
        validatePickingListCanEdit(id);

        pickingListMapper.updateApproveStatusById(DocumentStatusEnum.APPROVE.getStatus(), id);
    }

    @Override
    public void cancelApprove(Long id) {
        validatePickingListExists(id);
        validateDocumentStatus(id, DocumentStatusEnum.APPROVE.getStatus());
        pickingListMapper.updateApproveStatusById(DocumentStatusEnum.ROLLBACK.getStatus(), id);

    }

    private void validateDocumentStatus(Long id, Integer status) {
        PickingListDO pickingListDO = pickingListMapper.selectById(id);
        if (!Objects.equals(pickingListDO.getDocumentStatus(), status)) {
            throw exception(PICKING_LIST_NOT_DOCUMENT_STATUS_CHANGE);
        }
    }

    @Override
    public void suspend(Long id) {
        // 校验存在
        validatePickingListExists(id);
        pickingListMapper.updateBusinessStatusById(BusinessStatusEnum.SUSPENDED.getStatus(), id);
    }

    @Override
    public void cancelSuspend(Long id) {
        validatePickingListExists(id);
        validateBusinessStatus(id, BusinessStatusEnum.SUSPENDED.getStatus());
        pickingListMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    @Override
    public void close(Long id) {
        validatePickingListExists(id);
        pickingListMapper.updateBusinessStatusById(BusinessStatusEnum.CLOSED.getStatus(), id);
    }

    @Override
    public void cancelClose(Long id) {
        validatePickingListExists(id);
        validateBusinessStatus(id, BusinessStatusEnum.CLOSED.getStatus());
        pickingListMapper.updateBusinessStatusById(BusinessStatusEnum.NORMAL.getStatus(), id);
    }

    private void validateBusinessStatus(Long id, Integer status) {
        PickingListDO pickingListDO = pickingListMapper.selectById(id);
        if (!Objects.equals(pickingListDO.getBusinessStatus(), status)) {
            throw exception(PICKING_LIST_NOT_BUSINESS_STATUS_CHANGE);
        }

    }

    @Override
    public PageResult<LineWithPickingListVO> pageLine(SearchPickingListLinePageDTO searchPickingListLinePageDTO) {
        PageResult<LineWithPickingList> lineWithPickingListPageResult = pickingListLineMapper.selectPageLineWithPickingList(searchPickingListLinePageDTO);
        List<LineWithPickingList> list = lineWithPickingListPageResult.getList();
        List<LineWithPickingListVO> lineWithPickingListVOS = pickingListLineConvert.viewToVO(list);
        //找到所有的businessUserId
        List<Long> businessUserIds = lineWithPickingListVOS.stream()
                .map(vo -> {
                    SimplePickingListVO pickingList = vo.getPickingList();
                    return pickingList != null ? pickingList.getBusinessUserId() : null;
                })
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        Map<Long, AdminUserRespDTO> userMap = adminUserUtil.listUserMap(businessUserIds);

        for (LineWithPickingListVO lineWithPickingListVO : lineWithPickingListVOS) {
            SimplePickingListVO pickingList = lineWithPickingListVO.getPickingList();
            if (pickingList != null) {
                Long businessUserId = pickingList.getBusinessUserId();
                if (businessUserId != null && userMap.containsKey(businessUserId)) {
                    pickingList.setBusinessUserName(userMap.get(businessUserId).getNickname());
                }
            }
        }
        return new PageResult<>(lineWithPickingListVOS, lineWithPickingListPageResult.getTotal());
    }

    @Override
    public List<SimplePickingListVO> simpleList(SearchSimpleListDTO searchSimpleList) {
        List<Long> ids = new ArrayList<>();
        if (Objects.nonNull(searchSimpleList.getShippingId())) {
            List<Long> pickingListIds = shippingService.selectPickingListByShippingId(searchSimpleList.getShippingId());
            if (CollUtil.isNotEmpty(pickingListIds)) {
                ids.addAll(pickingListIds);
            }
        }
        if (CollUtil.isNotEmpty(searchSimpleList.getIds())) {
            ids.addAll(searchSimpleList.getIds());
        }

        searchSimpleList.setIds(ids);
        List<PickingListDO> pickingListDOList = pickingListMapper.selectListBySearchDTO(searchSimpleList);
        return pickinglistConvert.toSimpleVO(pickingListDOList);
    }

    @Override
    public void validateWhenMaterialDelete(Long materialId) {
        if (pickingListLineMapper.countByMaterialId(materialId) > 0) {
            throw exception(PICKING_LIST_LINE_REFER_MATERIAL_EXITS);
        }
    }

    @Override
    public void validateWhenCustomerDelete(Long customerId) {
        if (pickingListMapper.countByCustomerId(customerId) > 0) {
            throw exception(PICKING_LIST_REFER_CUSTOMER_EXITS);
        }
    }
}