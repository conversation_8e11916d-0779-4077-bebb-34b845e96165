package cn.iocoder.yudao.module.erp.controller.admin.requisition.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 领料单明细 Response VO")
@Data
public class RequisitionOrderItemVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "领料单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long requisitionOrderId;

    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long materialId;

    @Schema(description = "物料编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "M001")
    private String materialCode;

    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "钢材")
    private String materialName;

    @Schema(description = "规格型号", example = "Q235")
    private String materialSpec;

    @Schema(description = "主计量单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "吨")
    private String materialUom;

    @Schema(description = "可用库存", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    private BigDecimal availableQuantity;

    @Schema(description = "领用数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "10.00")
    private BigDecimal requestQuantity;

    @Schema(description = "备注", example = "备注信息")
    private String remark;

}
