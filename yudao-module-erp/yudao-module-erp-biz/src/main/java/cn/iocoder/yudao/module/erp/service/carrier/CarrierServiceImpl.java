package cn.iocoder.yudao.module.erp.service.carrier;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.erp.constant.OrganizationIdentitySpecEnum;
import cn.iocoder.yudao.module.erp.controller.admin.base.organization.vo.OrganizationVO;
import cn.iocoder.yudao.module.erp.controller.admin.carrier.vo.*;
import cn.iocoder.yudao.module.erp.convert.carrier.CarrierConvert;
import cn.iocoder.yudao.module.erp.convert.carrier.CarrierDriverConvert;
import cn.iocoder.yudao.module.erp.convert.carrier.CarrierVehicleConvert;
import cn.iocoder.yudao.module.erp.dal.dataobject.carrier.CarrierDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.carrier.CarrierDriverDO;
import cn.iocoder.yudao.module.erp.dal.dataobject.carrier.CarrierVehicleDO;
import cn.iocoder.yudao.module.erp.dal.mysql.carrier.CarrierDriverMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.carrier.CarrierMapper;
import cn.iocoder.yudao.module.erp.dal.mysql.carrier.CarrierVehicleMapper;
import cn.iocoder.yudao.module.erp.service.base.organization.AbstractOrganizationEntityService;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import jakarta.annotation.Resource;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.erp.enums.ErrorCodeConstants.*;

/**
 * 承运商档案 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CarrierServiceImpl extends AbstractOrganizationEntityService<CreateCarrierDTO, UpdateCarrierDTO> implements CarrierService {


    @Resource
    private CarrierMapper carrierMapper;

    @Resource
    private CarrierDriverMapper driverMapper;

    @Resource
    private CarrierVehicleMapper vehicleMapper;

    @Resource
    private CarrierConvert carrierConvert;
    @Resource
    private CarrierVehicleConvert vehicleConvert;

    @Resource
    private CarrierDriverConvert driverConvert;

    private void validateShortNameUnique(Long id, String shortName) {
        CarrierDO carrierDO = carrierMapper.selectByShortName(shortName);
        if (Objects.nonNull(carrierDO) && !Objects.equals(carrierDO.getId(), id)) {
            throw exception(CARRIER_SHORT_NAME_DUPLICATE, shortName);
        }
    }

    private void validateNameUnique(Long id, String name) {
        CarrierDO carrierDO = carrierMapper.selectByName(name);
        if (Objects.nonNull(carrierDO) && !Objects.equals(carrierDO.getId(), id)) {
            throw exception(CARRIER_NAME_DUPLICATE, name);
        }
    }


    private void validateCodeUnique(Long id, String code) {
        CarrierDO carrierDO = carrierMapper.selectByCode(code);
        if (Objects.nonNull(carrierDO) && !Objects.equals(carrierDO.getId(), id)) {
            throw exception(CARRIER_CODE_DUPLICATE, code);
        }
    }


    private void validateCarrierExists(Long id) {
        if (carrierMapper.selectById(id) == null) {
            throw exception(CARRIER_NOT_EXISTS, id);
        }
    }

    @Override
    public CarrierVO getCarrier(Long id) {
        validateCarrierExists(id);

        List<CarrierDriverDO> carrierDriverDOS = driverMapper.selectListByCarrierId(id);
        List<CarrierVehicleDO> carrierVehicleDOS = vehicleMapper.selectListByCarrierId(id);
        CarrierDO carrierDO = carrierMapper.selectById(id);

        CarrierVO vo = carrierConvert.toVO(carrierDO);
        vo.setDrivers(driverConvert.toVO(carrierDriverDOS));
        vo.setVehicles(vehicleConvert.toVO(carrierVehicleDOS));
        return vo;
    }

    @Override
    public PageResult<SimpleCarrierVO> page(SearchCarrierPageDTO searchCarrierPageDTO) {
        PageResult<CarrierDO> carrierDOPageResult = carrierMapper.selectPage(searchCarrierPageDTO);
        List<CarrierDO> list = carrierDOPageResult.getList();
        List<SimpleCarrierVO> simpleCarrierVOS = carrierConvert.toSimpleVO(list);
        return new PageResult<>(simpleCarrierVOS, carrierDOPageResult.getTotal());
    }

    @Override
    public void disable(Long id) {
        validateCarrierExists(id);
        carrierMapper.updateById(new CarrierDO().setId(id).setStatus(CommonStatusEnum.DISABLE.getStatus()));
    }

    @Override
    public void enable(Long id) {
        validateCarrierExists(id);
        carrierMapper.updateById(new CarrierDO().setId(id).setStatus(CommonStatusEnum.ENABLE.getStatus()));
    }

    @Override
    public void validateCarrierAndDriver(Long carrierId, Long driverId) {
        CarrierDO carrierDO = carrierMapper.selectById(carrierId);
        if (Objects.isNull(carrierDO)) {
            throw exception(CARRIER_NOT_EXISTS, carrierId);
        }
        if (Objects.equals(carrierDO.getStatus(), CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(CARRIER_STATUS_DISABLE, carrierDO.getName());
        }
        if (Objects.nonNull(driverId)) {
            CarrierDriverDO carrierDriverDO = driverMapper.selectById(driverId);
            if (Objects.isNull(carrierDriverDO)) {
                throw exception(CARRIER_DRIVER_NOT_EXISTS, driverId);
            }
            if (Objects.equals(carrierDriverDO.getStatus(), CommonStatusEnum.DISABLE.getStatus())) {
                throw exception(CARRIER_DRIVER_STATUS_DISABLE, carrierDriverDO.getName());
            }
            if (!Objects.equals(carrierId, carrierDriverDO.getCarrierId())) {
                throw exception(CARRIER_DRIVER_NOT_BELONG_TO_CARRIER, carrierDriverDO.getName(), carrierDO.getName());
            }
        }

    }

    @Override
    public List<SimpleCarrierWhitVehicleAndDriverVO> simpleList(SearchCarrierListDTO searchCarrierLis) {
        List<CarrierDO> carrierDOS = carrierMapper.selectList(searchCarrierLis);
        List<SimpleCarrierWhitVehicleAndDriverVO> resultVO = carrierConvert.SimpleCarrierWhitVehicleAndDriverVO(carrierDOS);
        if (searchCarrierLis.getNeedDriver()) {
            for (SimpleCarrierWhitVehicleAndDriverVO simpleCarrier : resultVO) {
                List<CarrierDriverDO> carrierDriverDOS = driverMapper.selectListByCarrierId(simpleCarrier.getId());
                simpleCarrier.setDrivers(driverConvert.toVO(carrierDriverDOS));
            }
        }
        if (searchCarrierLis.getNeedVehicle()) {
            for (SimpleCarrierWhitVehicleAndDriverVO simpleCarrier : resultVO) {
                List<CarrierVehicleDO> carrierVehicleDOS = vehicleMapper.selectListByCarrierId(simpleCarrier.getId());
                simpleCarrier.setVehicles(vehicleConvert.toVO(carrierVehicleDOS));
            }
        }
        return resultVO;
    }

    @Override
    protected void updateOrganizationCommonField(Long id, OrganizationVO organizationVO) {
        CarrierDO updated = carrierConvert.toDO(organizationVO);
        updated.setId(id);
        carrierMapper.updateById(updated);
    }

    @Override
    protected Long doCreate(CreateCarrierDTO createDTO) {
        // 插入
        CarrierDO carrier = carrierConvert.toDO(createDTO);
        carrier.setStatus(CommonStatusEnum.ENABLE.getStatus());
        carrierMapper.insert(carrier);
        // 返回
        return carrier.getId();
    }

    @Override
    protected void doUpdate(Long id, UpdateCarrierDTO updateDTO) {
        synchronizeEntities(forVehicles(id, updateDTO.getVehicles()));
        synchronizeEntities(forDrivers(id, updateDTO.getDrivers()));

        updateCoreInfo(id, updateDTO);
    }


    @Override
    protected OrganizationIdentitySpecEnum getOrganizationIdentitySpecEnum() {
        return OrganizationIdentitySpecEnum.CARRIER;
    }

    @Override
    protected void doDelete(Long id) {
        // 删除
        carrierMapper.deleteById(id);
        //删除车辆信息
        vehicleMapper.deleteByCarrierId(id);
        //删除司机信息
        driverMapper.deleteByCarrierId(id);
    }


    @Override
    protected void validateSelfBeforeCreate(CreateCarrierDTO createDTO) {
        //验证承运商编号全局唯一
        validateCodeUnique(null, createDTO.getCode());
        //验证名称是否全局唯一
        validateNameUnique(null, createDTO.getName());
        //验证简称是否全局唯一
        validateShortNameUnique(null, createDTO.getShortName());
    }

    @Override
    protected void validateSelfBeforeUpdate(Long id, UpdateCarrierDTO updateDTO) {
        validateCarrierExists(id);
        validateCarrierStatus(id, CommonStatusEnum.DISABLE.getStatus());
        //验证承运商编号全局唯一
        validateCodeUnique(id, updateDTO.getCode());
        //验证名称是否全局唯一
        validateNameUnique(id, updateDTO.getName());
        //验证简称是否全局唯一
        validateShortNameUnique(id, updateDTO.getShortName());
    }

    @Override
    protected void validateBeforeDelete(Long id) {
        validateCarrierExists(id);
    }

    private void validateCarrierStatus(Long carrierId, Integer status) {
        if (Objects.equals(status, carrierMapper.selectById(carrierId).getStatus())) {
            throw exception(CARRIER_STATUS_DISABLE, carrierId);
        }
    }

    private SyncConfig<CarrierVehicleDTO, CarrierVehicleDO> forVehicles(
            Long carrierId, List<CarrierVehicleDTO> dtos) {
        return new SyncConfig<>(
                carrierId,
                dtos,
                vehicleMapper::selectListByCarrierId,
                vehicleConvert::toDO,
                CarrierVehicleDO::getId,
                CarrierVehicleDO::setCarrierId,
                vehicleMapper
        );
    }

    private SyncConfig<CarrierDriverDTO, CarrierDriverDO> forDrivers(
            Long carrierId, List<CarrierDriverDTO> dtos) {
        return new SyncConfig<>(
                carrierId,
                dtos,
                driverMapper::selectListByCarrierId,
                driverConvert::toDO,
                CarrierDriverDO::getId,
                CarrierDriverDO::setCarrierId,
                driverMapper
        );
    }


    // 核心同步方法（完全封装函数式接口）
    private <D, E> void synchronizeEntities(SyncConfig<D, E> config) {
        if (config.entities == null) return;

        // 获取现有数据
        List<E> existingList = config.existLoader.apply(config.carrierId);
        List<Long> existingIds = existingList.stream()
                .map(config.idExtractor)
                .collect(Collectors.toList());

        // 分区处理
        EntityChangeSet<D> changes = partitionChanges(config.entities, existingIds);

        // 执行操作
        performDeletions(config.mapper, changes.deleteIds());
        processAdditions(config, changes.newEntities());
        processUpdates(config, changes.updateEntities());
    }
    
    // 变更集合记录
    private record EntityChangeSet<D>(
            List<D> newEntities,
            List<D> updateEntities,
            List<Long> deleteIds
    ) {
    }

    //--------------------- 以下为内部辅助方法 ---------------------
    private <D> EntityChangeSet<D> partitionChanges(List<D> entities, List<Long> existingIds) {
        Map<Boolean, List<D>> partitioned = entities.stream()
                .collect(Collectors.partitioningBy(d -> getId(d) == null));

        List<D> newEntities = partitioned.get(true);
        List<D> updateEntities = partitioned.get(false);

        List<Long> updateIds = updateEntities.stream()
                .map(this::getId)
                .toList();

        List<Long> deleteIds = existingIds.stream()
                .filter(id -> !updateIds.contains(id))
                .collect(Collectors.toList());

        return new EntityChangeSet<>(newEntities, updateEntities, deleteIds);
    }

    private <D> Long getId(D dto) {
        // 实际项目中需要类型安全处理，这里假设DTO都有getId()
        try {
            return (Long) dto.getClass().getMethod("getId").invoke(dto);
        } catch (Exception e) {
            throw new RuntimeException("DTO必须实现getId方法", e);
        }
    }

    private <E> void performDeletions(BaseMapper<E> mapper, List<Long> deleteIds) {
        if (!deleteIds.isEmpty()) {
            mapper.deleteByIds(deleteIds);
        }
    }

    private <D, E> void processAdditions(SyncConfig<D, E> config, List<D> newEntities) {
        if (newEntities.isEmpty()) return;

        List<E> entities = newEntities.stream()
                .map(config.converter)
                .peek(e -> config.idSetter.accept(e, config.carrierId))
                .collect(Collectors.toList());

        config.mapper.insertBatch(entities);
    }

    private <D, E> void processUpdates(SyncConfig<D, E> config, List<D> updateEntities) {
        if (updateEntities.isEmpty()) return;

        List<E> entities = updateEntities.stream()
                .map(config.converter)
                .peek(e -> config.idSetter.accept(e, config.carrierId))
                .collect(Collectors.toList());

        config.mapper.updateBatch(entities);
    }

    // 核心信息更新保持不变
    private void updateCoreInfo(Long carrierId, UpdateCarrierDTO updateDTO) {
        CarrierDO carrier = carrierConvert.toDO(updateDTO);
        carrier.setId(carrierId);
        carrierMapper.updateById(carrier);
    }

    // 参数封装类（使用建造者模式增强可读性）
    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    private static class SyncConfig<D, E> {
        private final Long carrierId;
        private final List<D> entities;
        private final Function<Long, List<E>> existLoader;
        private final Function<D, E> converter;
        private final Function<E, Long> idExtractor;
        private final BiConsumer<E, Long> idSetter;
        private final BaseMapperX<E> mapper;
    }

}