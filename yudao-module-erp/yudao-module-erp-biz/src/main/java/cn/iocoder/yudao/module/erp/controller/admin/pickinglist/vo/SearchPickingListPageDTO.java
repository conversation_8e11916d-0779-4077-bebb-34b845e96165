package cn.iocoder.yudao.module.erp.controller.admin.pickinglist.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 备货通知单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchPickingListPageDTO extends PageParam {

    @Schema(description = "备货单号")
    private String code;

    /***
     * 客户名称
     */
    private String customerName;

    @Schema(description = "交货日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] deliveryDate;

    @Schema(description = "`业务员`   ", example = "15877")
    private Long businessUserId;

    @Schema(description = "业务状态", example = "2")
    private Integer businessStatus;

    @Schema(description = "单据状态", example = "2")
    private Integer documentStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}