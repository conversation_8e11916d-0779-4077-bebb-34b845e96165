package cn.iocoder.yudao.module.erp.convert.outbound.purchase;


import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.CreatePurchaseReturnedOutboundDTO;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.PurchaseReturnedOutboundVO;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.SimplePurchaseReturnedOutboundVO;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.UpdatePurchaseReturnedOutboundDTO;
import cn.iocoder.yudao.module.erp.dal.dataobject.outbound.purchase.PurchaseReturnedOutboundDO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PurchaseReturnedOutboundConvert {

    PurchaseReturnedOutboundDO toDO(CreatePurchaseReturnedOutboundDTO creatDTO);

    PurchaseReturnedOutboundDO toDO(UpdatePurchaseReturnedOutboundDTO updateDTO);

    PurchaseReturnedOutboundVO toVO(PurchaseReturnedOutboundDO purchaseReturnedOutboundDO);

    List<PurchaseReturnedOutboundVO> toVO(List<PurchaseReturnedOutboundDO> purchaseReturnedOutboundDOList);

    SimplePurchaseReturnedOutboundVO toSimpleVO(PurchaseReturnedOutboundDO purchaseReturnedOutboundDO);

    List<SimplePurchaseReturnedOutboundVO> toSimpleVO(List<PurchaseReturnedOutboundDO> purchaseReturnedOutboundDOList);
}