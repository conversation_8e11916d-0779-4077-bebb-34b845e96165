package cn.iocoder.yudao.module.erp.dal.dataobject.abc;

import lombok.*;
import java.util.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.DocumentDO;
import lombok.experimental.SuperBuilder;

/**
 * 采购退货出库单 DO
 *
 * <AUTHOR>
 */
@TableName("erp_purchase_returned_outbound")
@KeySequence("erp_purchase_returned_outbound_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseReturnedOutboundDO extends DocumentDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 单据号
     */
    private String code;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 0编制中 1审核通过 2回退
     */
    private Integer documentStatus;
    /**
     * 业务状态：0关闭 1正常 2挂起
     */
    private Integer businessStatus;
    /**
     * 出库日期
     */
    private LocalDate outboundDate;
    /**
     * 仓库ID
     */
    private Long warehouseId;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 供应商ID
     */
    private Long supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 业务部门ID
     */
    private Long businessDepartmentId;
    /**
     * 业务部门名称
     */
    private String businessDepartmentName;
    /**
     * 业务员ID
     */
    private Long businessUserId;
    /**
     * 业务员姓名
     */
    private String businessUserName;
    /**
     * 物料类型:采购单的物料类型（业务字典）
     */
    private String materialType;
    /**
     * 退货通知单ID
     */
    private Long returnedId;
    /**
     * 退货通知单号
     */
    private String returnedCode;
    /**
     * 备注
     */
    private String remark;
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

}