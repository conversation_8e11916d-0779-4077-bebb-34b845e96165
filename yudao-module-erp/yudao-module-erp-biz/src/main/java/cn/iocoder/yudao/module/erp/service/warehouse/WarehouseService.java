package cn.iocoder.yudao.module.erp.service.warehouse;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.constant.StockOperationInTypeEnum;
import cn.iocoder.yudao.module.erp.constant.StockOperationOutTypeEnum;
import cn.iocoder.yudao.module.erp.controller.admin.warehouse.vo.*;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Set;

/**
 * 仓库基本信息 Service 接口
 *
 * <AUTHOR>
 */
public interface WarehouseService {

    /**
     * 创建仓库基本信息
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long createWarehouse(@Valid CreateWarehouseDTO createDTO);

    /**
     * 更新仓库基本信息
     *
     * @param id        仓库Id
     * @param updateDTO 更新信息
     */
    void updateWarehouse(Long id, @Valid UpdateWarehouseDTO updateDTO);

    /**
     * 删除仓库基本信息
     *
     * @param id 编号
     */
    void deleteWarehouse(Long id);

    /**
     * 获得仓库基本信息
     *
     * @param id 编号
     * @return 仓库基本信息
     */
    WarehouseVO getWarehouse(Long id);

    /**
     * 获得仓库基本信息分页
     *
     * @param searchWarehouseDTO 分页查询
     * @return 仓库基本信息分页
     */
    PageResult<WarehouseVO> pageWarehouse(SearchWarehouseDTO searchWarehouseDTO);

    /**
     * 根据Id停用仓库
     *
     * @param id 仓库Id
     */
    void disableWarehouse(Long id);

    /**
     * 根据Id启用仓库
     *
     * @param id 仓库Id
     */
    void enableWarehouse(Long id);

    /**
     * 创建库位
     *
     * @param id        仓库Id
     * @param createDTO 库位信息
     */
    void createWarehouseStoragePosition(Long id, CreateStoragePositionDTO createDTO);

    /**
     * 更新库位
     *
     * @param id                仓库Id
     * @param storagePositionId 库位Id
     * @param updateDTO         更新信息
     */
    void updateWarehouseStoragePosition(Long id, Long storagePositionId, UpdateStoragePositionDTO updateDTO);

    /**
     * 删除库位信息
     *
     * @param id                仓库Id
     * @param storagePositionId 库位Id
     */
    void deleteWarehouseStoragePosition(Long id, Long storagePositionId);

    /**
     * 查询仓库的库位信息
     *
     * @param id        仓库Id
     * @param listReqVO 查询条件
     * @return 库位列表
     */
    List<StoragePositionVO> listWarehouseStoragePosition(Long id, SearchStoragePositionDTO listReqVO);

    /**
     * 获取仓库的下的库位信息
     *
     * @param id                仓库Id
     * @param storagePositionId 库位信息
     * @return 库位信息
     */
    StoragePositionVO getWarehouseStoragePosition(Long id, Long storagePositionId);

    /**
     * 停用库位
     *
     * @param id                仓库Id
     * @param storagePositionId 库位Id
     */
    void disableWarehouseStoragePosition(Long id, Long storagePositionId);

    /**
     * 启用库位
     *
     * @param id                仓库Id
     * @param storagePositionId 库位Id
     */
    void enableWarehouseStoragePosition(Long id, Long storagePositionId);

    /**
     * 验证仓库是否启用，并且支持指定入库类型
     *
     * @param warehouseId
     * @param stockOperationInTypeEnum
     */
    void validateWarehouseEnableAndInStockOperationType(Long warehouseId, StockOperationInTypeEnum stockOperationInTypeEnum);


    /**
     * 验证仓库是否启用，并且支持指定出库类型
     */
    void validateWarehouseEnableAndOutStockOperationType(Long warehouseId, StockOperationOutTypeEnum stockOperationOutTypeEnum);

    /**
     * 验证库位存在并且是启用的并且属于指定仓库
     *
     * @param warehouseId          仓库Id
     * @param warehousePositionIds 库位Id集合
     */
    void validatePositionEnableAndBelongWarehouse(Long warehouseId, List<Long> warehousePositionIds);


    void validatePositionEnableAndBelongWarehouse(Long warehouseId, Long warehousePositionId);

    /**
     * 获取仓库信息列表-简化版
     *
     * @param searchListDTO
     * @return
     */
    List<SimpleWarehouseVO> simpleList(SearchWarehouseListDTO searchListDTO);

    /**
     * 获取仓库信息列表-详细版
     *
     * @param warehouseIds 仓库Id集合
     * @return
     */
    List<SimpleWarehouseVO> listByIds(Set<Long> warehouseIds);


    List<SimpleStoragePositionVO> listStoragePositionByIds(Set<Long> storagePositionIds);
}