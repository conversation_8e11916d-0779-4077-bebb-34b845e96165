package cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 采购退货出库单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchPurchaseReturnedOutboundDTO extends PageParam {

    @Schema(description = "单据号")
    private String code;

    @Schema(description = "业务类型", example = "1")
    private String businessType;

    @Schema(description = "0编制中 1审核通过 2回退", example = "1")
    private Integer documentStatus;

    @Schema(description = "业务状态：0关闭 1正常 2挂起", example = "1")
    private Integer businessStatus;

    @Schema(description = "出库日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] outboundDate;


    @Schema(description = "仓库ID", example = "31837")
    private Long warehouseId;

    @Schema(description = "仓库名称", example = "芋艿")
    private String warehouseName;

    @Schema(description = "供应商ID", example = "24917")
    private Long supplierId;

    @Schema(description = "供应商名称", example = "芋艿")
    private String supplierName;

    @Schema(description = "业务部门ID", example = "13559")
    private Long businessDepartmentId;

    @Schema(description = "业务部门名称", example = "张三")
    private String businessDepartmentName;

    @Schema(description = "业务员ID", example = "28466")
    private Long businessUserId;

    @Schema(description = "业务员姓名", example = "芋艿")
    private String businessUserName;

    @Schema(description = "物料类型:采购单的物料类型（业务字典）", example = "2")
    private String materialType;

    @Schema(description = "退货通知单ID", example = "27355")
    private Long returnedId;

    @Schema(description = "退货通知单号")
    private String returnedCode;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;


    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;


}