package cn.iocoder.yudao.module.erp.service.base.organization;

public interface OrganizationCommonFieldUpdate {
    /**
     * 同步组织公共字段
     * 先根据 sourceSpec，如果sourceSpec来自当前类，跳过当前类的更新
     * 如果不是，根据organizationId 查询当前类是否有关联记录，如果有，则更新
     *
     * @param event
     */
//    default void syncOrganizationCommonField(OrganizationUpdatedEvent event, OrganizationHelper organizationHelper) {
//        if (Objects.equals(getOrganizationIdentitySpec(), (event.getSourceSpec()))) {
//            return;
//        }
//        Long specIdByOrganizationId = organizationHelper.getSpecIdByOrganizationIdAndSpecType(event.getOrganization().getId(), getOrganizationIdentitySpec());
//        if (Objects.nonNull(specIdByOrganizationId)) {
//            updateOrganizationCommonField(specIdByOrganizationId, event.getOrganization());
//        }
//    }
//
//    /**
//     * 获取当前服务关联组织时的IdentitySpec
//     *
//     * @return
//     */
//    String getOrganizationIdentitySpec();
//
//    /**
//     * 更新组织公共字段
//     *
//     * @param specIdByOrganizationId
//     * @param organization
//     */
//    void updateOrganizationCommonField(Long specIdByOrganizationId, OrganizationVO organization);

}
