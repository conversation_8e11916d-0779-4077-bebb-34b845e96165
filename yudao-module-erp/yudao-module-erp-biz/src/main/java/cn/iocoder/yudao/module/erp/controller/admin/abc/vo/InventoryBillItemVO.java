package cn.iocoder.yudao.module.erp.controller.admin.abc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 出入库清单 VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryBillItemVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26739")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "出入库单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24483")
    @ExcelProperty("出入库单ID")
    private Long billId;

    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10300")
    @ExcelProperty("物料ID")
    private Long materialId;

    @Schema(description = "物料编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物料编码")
    private String materialCode;

    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("物料名称")
    private String materialName;

    @Schema(description = "规格型号")
    @ExcelProperty("规格型号")
    private String materialSpec;

    @Schema(description = "计量单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("计量单位")
    private String materialUom;

    @Schema(description = "入库数量")
    @ExcelProperty("入库数量")
    private BigDecimal inQuantity;

    @Schema(description = "出库数量")
    @ExcelProperty("出库数量")
    private BigDecimal outQuantity;

    @Schema(description = "批次号")
    @ExcelProperty("批次号")
    private String batchNumber;

    @Schema(description = "唯一码")
    @ExcelProperty("唯一码")
    private String uniqueCode;

    @Schema(description = "库位ID", example = "23198")
    @ExcelProperty("库位ID")
    private Long warehousePositionId;

    @Schema(description = "库位名称", example = "赵六")
    @ExcelProperty("库位名称")
    private String warehousePositionName;

    @Schema(description = "来源单据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("来源单据类型")
    private String sourceType;

    @Schema(description = "来源单据ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26732")
    @ExcelProperty("来源单据ID")
    private Long sourceId;

    @Schema(description = "来源单据code", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("来源单据code")
    private String sourceCode;

    @Schema(description = "来源单据ItemID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15491")
    @ExcelProperty("来源单据ItemID")
    private Long sourceItemId;

    @Schema(description = "包装类型id", example = "11138")
    @ExcelProperty("包装类型id")
    private Long packagingTypeId;

    @Schema(description = "包装类型名称", example = "王五")
    @ExcelProperty("包装类型名称")
    private String packagingTypeName;

    @Schema(description = "包装规格id", example = "27626")
    @ExcelProperty("包装规格id")
    private Long packagingSpecId;

    @Schema(description = "包装规格名称", example = "赵六")
    @ExcelProperty("包装规格名称")
    private String packagingSpecName;

    @Schema(description = "包装物所属公司id，从customer中获取", example = "26304")
    @ExcelProperty("包装物所属公司id，从customer中获取")
    private Long packagingCompanyId;

    @Schema(description = "包装物所属公司名称", example = "赵六")
    @ExcelProperty("包装物所属公司名称")
    private String packagingCompanyName;

    @Schema(description = "包装物归属")
    @ExcelProperty("包装物归属")
    private String packagingOwner;

    @Schema(description = "包装码")
    @ExcelProperty("包装码")
    private String packagingCode;

    @Schema(description = "挂牌车号")
    @ExcelProperty("挂牌车号")
    private String plateNumber;

    @Schema(description = "毛重")
    @ExcelProperty("毛重")
    private BigDecimal grossWeight;

    @Schema(description = "空重")
    @ExcelProperty("空重")
    private BigDecimal tareWeight;

    @Schema(description = "充装日期")
    @ExcelProperty("充装日期")
    private LocalDateTime fillingDate;

    @Schema(description = "充装人id", example = "7619")
    @ExcelProperty("充装人id")
    private Long fillingUserId;

    @Schema(description = "充装人姓名", example = "李四")
    @ExcelProperty("充装人姓名")
    private String fillingUserName;

    @Schema(description = "已退货数量")
    @ExcelProperty("已退货数量")
    private BigDecimal retunedQuantity;

    @Schema(description = "可退货数量：入库数量-已退货数量-已使用数量")
    @ExcelProperty("可退货数量：入库数量-已退货数量-已使用数量")
    private BigDecimal returnableQuantity;

    @Schema(description = "已使用数量:领料出库单审核通过时代表已使用")
    @ExcelProperty("已使用数量:领料出库单审核通过时代表已使用")
    private BigDecimal usedQuantity;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

}