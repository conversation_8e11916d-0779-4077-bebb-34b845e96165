package cn.iocoder.yudao.module.erp.service.outbound.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.erp.controller.admin.outbound.purchase.vo.*;
import cn.iocoder.yudao.module.erp.service.inventory.common.InventoryCommonService;

/**
 * 采购退货出库单 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseReturnedOutboundService extends InventoryCommonService<CreatePurchaseReturnedOutboundDTO, PurchaseReturnedOutboundVO> {

    /**
     * 获得采购退货出库单分页
     *
     * @param searchDTO 分页查询
     * @return 采购退货出库单分页
     */
    PageResult<SimplePurchaseReturnedOutboundVO> page(SearchPurchaseReturnedOutboundDTO searchDTO);

    /**
     * 获得采购退货出库单明细分页
     *
     * @param searchDTO 分页查询
     * @return 采购退货出库单明细分页
     */
    PageResult<ItemWithPurchaseReturnedOutboundVO> pageItem(SearchItemPageDTO searchDTO);

}