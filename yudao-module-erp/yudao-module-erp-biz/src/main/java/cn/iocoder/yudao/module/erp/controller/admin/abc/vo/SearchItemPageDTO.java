package cn.iocoder.yudao.module.erp.controller.admin.abc.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDate;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 出入库清单明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchItemPageDTO extends PageParam {

    @Schema(description = "出入库清单ID", example = "1024")
    private Long inventoryBillId;

    @Schema(description = "单据编号", example = "DH202401001")
    private String code;

    @Schema(description = "仓库ID", example = "1")
    private Long warehouseId;

    @Schema(description = "单据状态", example = "1")
    private Integer documentStatus;

    @Schema(description = "物料编码", example = "M001")
    private String materialCode;

    @Schema(description = "物料名称", example = "钢材")
    private String materialName;

    @Schema(description = "批次号", example = "B001")
    private String batchNo;

    @Schema(description = "唯一码", example = "U001")
    private String uniqueCode;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] createTime;

}
