package cn.iocoder.yudao.module.erp.controller.admin.inventory.common;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CommonInventoryItemDTO {
    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24255")
    @NotNull(message = "物料ID不能为空")
    private Long materialId;

    @Schema(description = "物料编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物料编码不能为空")
    private String materialCode;

    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "物料名称不能为空")
    private String materialName;

    @Schema(description = "规格型号")
    private String materialSpec;

    @Schema(description = "计量单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "计量单位不能为空")
    private String materialUom;

    @Schema(description = "库位ID", example = "1798")
    private Long warehousePositionId;

    @Schema(description = "库位名称", example = "赵六")
    private String warehousePositionName;


    @Schema(description = "备注", example = "随便")
    private String remark;
}
