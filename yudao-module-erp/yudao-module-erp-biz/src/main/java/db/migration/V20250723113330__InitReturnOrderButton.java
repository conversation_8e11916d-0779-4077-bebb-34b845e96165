package db.migration;

import cn.iocoder.yudao.module.erp.controller.admin.returned.ReturnedOrderController;

/**
 * init-return-order-button 迁移脚本描述
 */
public class V20250723113330__InitReturnOrderButton extends AbstractMenuInitMigration {
    @Override
    protected String getMenuName() {
        return "menus.returnOrder";
    }

    @Override
    protected String getCurrentPermission() {
        return "returned-order";
    }

    @Override
    protected Class<?> getControllerClass() {
        return ReturnedOrderController.class;
    }

    @Override
    protected boolean isDeleteOldButton() {
        return true;
    }
}
