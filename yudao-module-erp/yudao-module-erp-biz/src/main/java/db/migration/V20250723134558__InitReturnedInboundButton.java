package db.migration;

import cn.iocoder.yudao.module.erp.controller.admin.inbound.returned.ReturnedInboundController;

/**
 * init-returned-inbound-button 迁移脚本描述
 */
public class V20250723134558__InitReturnedInboundButton extends AbstractMenuInitMigration {
    @Override
    protected String getMenuName() {
        return "menus.salesReturn";
    }

    @Override
    protected String getCurrentPermission() {
        return "returned-inbound";
    }

    @Override
    protected Class<?> getControllerClass() {
        return ReturnedInboundController.class;
    }

    @Override
    protected boolean isDeleteOldButton() {
        return true;
    }
}
