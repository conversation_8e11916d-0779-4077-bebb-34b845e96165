package db.migration;

import cn.iocoder.yudao.module.erp.controller.admin.inbound.produce.ProduceInboundController;

/**
 * init-production-inbound-button 迁移脚本描述
 */
public class V20250723113956__InitProductionInboundButton extends AbstractMenuInitMigration {
    @Override
    protected String getMenuName() {
        return "menus.productionReceipt";
    }

    @Override
    protected String getCurrentPermission() {
        return "produce-inbound";
    }

    @Override
    protected Class<?> getControllerClass() {
        return ProduceInboundController.class;
    }

    @Override
    protected boolean isDeleteOldButton() {
        return true;
    }
}
