# AbstractMenuInitMigration 使用说明

## 概述

`AbstractMenuInitMigration` 是一个用于自动初始化系统菜单的 Flyway 迁移脚本抽象类。它能够：

1. 根据给定的父菜单名称查找父菜单
2. 创建或验证当前菜单是否存在
3. 解析指定的 Controller 类中的所有 public 方法
4. 提取方法上的 `@Operation` 和 `@PreAuthorize` 注解信息
5. 自动插入菜单和按钮权限到 `system_menu` 表中

## 功能特性

- **自动解析 Controller**：扫描 Controller 中所有带有 `@Operation` 和 `@PreAuthorize` 注解的 public 方法
- **智能菜单创建**：如果父菜单不存在会报错，如果当前菜单不存在会自动创建
- **按钮权限生成**：从 Controller 方法自动生成按钮类型的菜单项
- **权限码提取**：自动从 `@PreAuthorize("@ss.hasPermission('erp:purchase-order:approve')")`或
  `@PreAuthorize("@ss.hasAnyPermissions('erp:purchase-order:approve','erp:purchase-order:create'))")` 中提取权限码
- **重复检查**：避免重复插入已存在的菜单项

## 使用方法

### 1. 创建具体的迁移脚本

继承 `AbstractMenuInitMigration` 并实现三个抽象方法：

```java
package db.migration;

import cn.iocoder.yudao.module.erp.controller.admin.purchase.PurchaseOrderController;

public class V20250705120000__init_purchase_order_menu extends AbstractMenuInitMigration {

    @Override
    protected String getParentMenuName() {
        return "ERP 系统";  // 父菜单名称，必须已存在
    }

    @Override
    protected String getMenuName() {
        return "采购订单";  // 当前菜单名称，不存在会自动创建
    }

    @Override
    protected Class<?> getControllerClass() {
        return PurchaseOrderController.class;  // 要解析的 Controller 类
    }

    protected String getCurrentPermission() {
        return "purchase-order";  // 当前菜单的权限标识前缀
    }
}
```

### 2. Controller 方法要求

Controller 中的方法需要同时包含以下两个注解：

```java

@Operation(summary = "审核采购订单") // 用作按钮菜单的名称
@PreAuthorize("@ss.hasPermission('erp:purchase-order:approve')")
public CommonResult<Boolean> approve(@PathVariable Long id) {
    // 方法实现
}

```

### 3. 文件命名规范

迁移脚本文件名必须遵循 Flyway 命名规范：

- 格式：`V{时间戳}__{描述}.java`
- 示例：`V20250705120000__initPurchaseOrderMenu.java`

## 生成的菜单结构

### 菜单类型

- **父菜单**：由 `getParentMenuName()` 指定，必须已存在
- **当前菜单**：由 `getMenuName()` 指定，类型为 `MENU`，不存在会自动创建
- **按钮菜单**：从 Controller 方法解析，类型为 `BUTTON`

### 菜单字段映射

| 字段          | 菜单            | 按钮                 | 说明     |
|-------------|---------------|--------------------|--------|
| name        | getMenuName() | @Operation.summary | 菜单名称   |
| permission  | 空             | @PreAuthorize 提取   | 权限标识   |
| type        | 0 (MENU)      | 3 (BUTTON)         | 菜单类型   |
| parent_id   | 父菜单ID         | 当前菜单ID             | 父菜单ID  |
| sort        | 0             | 方法顺序+1             | 排序     |
| status      | 0 (启用)        | 0 (启用)             | 状态     |
| visible     | true          | true               | 是否可见   |
| keep_alive  | true          | true               | 是否缓存   |
| always_show | true          | true               | 是否总是显示 |
| creator     | "system"      | "system"           | 创建者    |

## 注意事项

1. **父菜单必须存在**：如果父菜单不存在，脚本会抛出异常
2. **权限码格式**：必须使用 `@ss.hasPermission('权限码')` 格式
3. **方法可见性**：只解析 public 方法
4. **注解完整性**：方法必须同时有 `@Operation` 和 `@PreAuthorize` 注解
5. **重复执行安全**：脚本可以重复执行，不会重复插入已存在的菜单

## 示例输出

假设有以下 Controller 方法：

```java

@Operation(summary = "创建采购订单")
@PreAuthorize("@ss.hasPermission('erp:purchase-order:create')")
public CommonResult<Long> create(@RequestBody CreatePurchaseOrderDTO dto) { ...}

@Operation(summary = "审核采购订单")
@PreAuthorize("@ss.hasPermission('erp:purchase-order:approve')")
public CommonResult<Boolean> approve(@PathVariable Long id) { ...}
```

将生成以下菜单结构：

```
ERP 系统 (已存在)
└── 采购订单 (自动创建，类型：菜单)
    ├── 创建采购订单 (类型：按钮，权限：erp:purchase-order:create)
    └── 审核采购订单 (类型：按钮，权限：erp:purchase-order:approve)
```

## 生成迁移脚本

- 执行：python bin/generate_migration.py 
