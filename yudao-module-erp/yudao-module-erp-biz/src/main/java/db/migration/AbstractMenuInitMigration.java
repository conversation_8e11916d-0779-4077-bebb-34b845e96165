package db.migration;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.module.system.enums.permission.MenuTypeEnum;
import lombok.Data;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;
import org.springframework.security.access.prepost.PreAuthorize;

import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.sql.Connection;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public abstract class AbstractMenuInitMigration extends BaseJavaMigration {

    private final String tableName = "system_menu";

    // 权限提取的正则表达式
//    private static final Pattern PERMISSION_PATTERN = Pattern.compile("@ss\\.hasPermission\\('([^']+)'\\)");
    private static final Pattern PERMISSION_PATTERN = Pattern.compile("'([^']+)'");
    //在解析这样的权限码  @PreAuthorize("@ss.hasAnyPermissions('erp:shipping:line:query','erp:sales-outbound:create','erp:sales-outbound:update')")。截取到的内容用','分割，取第一个
//    private static final Pattern PERMISSION_HASANY = Pattern.compile("@ss\\.hasAnyPermissions\\('([^']+)'\\)");
    private static final Pattern PERMISSION_HASANY = Pattern.compile("'([^']+)'");

    //写一个main函数验证
    public static void main(String[] args) {
        String value = "@ss.hasAnyPermissions('erp:sales-order:item:query','erp:shipping:create','erp:shipping:update')";
        Matcher matcher = PERMISSION_HASANY.matcher(value);
        List<String> permissions = new ArrayList<>();
        while (matcher.find()) {
            permissions.add(matcher.group(1));
        }
        System.out.println(permissions);
    }

    // 新增 jdbcTemplate 成员变量
    private JdbcTemplate jdbcTemplate;


    /**
     * 获取当前菜单名称 - 子类实现
     */
    protected abstract String getMenuName();


    protected abstract String getCurrentPermission();

    /**
     * 获取Controller类 - 子类实现
     */
    protected abstract Class<?> getControllerClass();

    @Override
    public void migrate(Context context) throws Exception {
        // 初始化 jdbcTemplate
        Connection connection = context.getConnection();
        this.jdbcTemplate = new JdbcTemplate(connection);

        // 获取子类提供的参数
        String menuName = getMenuName();
        Class<?> controllerClass = getControllerClass();

        Long menuId = getMenuId(menuName);
        if (menuId == null) {
            throw new RuntimeException("菜单 [" + menuName + "] 不存在");
        }
        if (isDeleteOldButton()) {
            deleteButtons(menuId);
        }

        // 3. 解析 controller 方法
        List<MenuInfo> menuInfos = parseControllerMethods(controllerClass);

        // 优化去重逻辑：使用Map保存唯一的permission对应的MenuInfo
        Map<String, MenuInfo> uniqueMenuInfos = new HashMap<>();
        for (MenuInfo info : menuInfos) {
            // 只保留每个permission的第一个MenuInfo
            uniqueMenuInfos.putIfAbsent(info.getPermission(), info);
        }

        // 将Map转换回List并按sort字段排序
        menuInfos = new ArrayList<>(uniqueMenuInfos.values());
        menuInfos.sort(Comparator.comparingInt(MenuInfo::getSort));

        // 4. 插入每个按钮菜单项
        for (MenuInfo info : menuInfos) {
            // 如果permission一样就更新，否则就新增
            Long buttonId = getButtonId(info.getPermission());
            if (buttonId != null) {
                updateButton(buttonId, info);
            } else {
                insertButton(menuId, info);
            }
        }
    }

    protected boolean isDeleteOldButton() {
        return false;
    }

    private void deleteButtons(Long menuId) {
        //软删除parentId=menuId的所有记录
        String sql = "UPDATE " + tableName + " SET deleted = 1, updater = 'system', update_time = CURRENT_TIMESTAMP WHERE parent_id = ?";
        jdbcTemplate.update(sql, menuId);
    }

    private void deleteButton(Long id) {
        String sql = "DELETE FROM " + tableName + " WHERE id = ?";
        jdbcTemplate.update(sql, id);
    }

    private List<MenuInfo> findButtonsByMenuId(Long menuId) {
        //根据menuId查询所有parentId为menuId的权限列表，返回List<MenuInfo>
        String sql = "SELECT id, name, permission, sort FROM " + tableName + " WHERE parent_id = ? AND deleted = 0";
        return jdbcTemplate.queryForList(sql, menuId);
    }

    /**
     * 更新按钮菜单项
     *
     * @param buttonId 按钮ID
     * @param info     菜单信息
     */
    private void updateButton(Long buttonId, MenuInfo info) {
        String sql = "UPDATE " + tableName +
                " SET name = ?, sort = ?, updater = ?, update_time = CURRENT_TIMESTAMP " +
                " WHERE id = ?";

        try {
            jdbcTemplate.update(sql,
                    info.getMenuName(),
                    info.getSort(),
                    "system",
                    buttonId);
        } catch (Exception e) {
            throw new RuntimeException("更新按钮菜单项失败: " + info.getMenuName(), e);
        }
    }

    /**
     * 根据权限标识获取按钮ID
     *
     * @param permission 权限标识
     * @return 按钮ID，如果不存在则返回null
     */
    private Long getButtonId(String permission) {
        if (permission == null || permission.trim().isEmpty()) {
            return null;
        }

        String sql = "SELECT id FROM " + tableName + " WHERE permission = ? AND deleted = 0";
        try {
            return jdbcTemplate.queryForObject(sql, Long.class, permission);
        } catch (Exception e) {
            // 按钮不存在，返回null
            return null;
        }
    }

    private void insertButton(Long parentId, MenuInfo info) {
        String sql = "INSERT INTO " + tableName +
                "(name, permission, type, sort, parent_id, status, visible, keep_alive, always_show, creator) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        jdbcTemplate.update(sql,
                info.getMenuName(),
                info.getPermission(),
                MenuTypeEnum.BUTTON.getType(),
                info.getSort(),
                parentId,
                CommonStatusEnum.ENABLE.getStatus(),
                true,
                true,
                true,
                "system");
    }

    /**
     * 解析 controller 中的方法，提取菜单信息
     * <p>
     * 从方法上的 @PermissionName 注解获取菜单名称和排序值
     * 从方法上的 @PreAuthorize 注解获取权限标识
     *
     * @param controllerClass 控制器类
     * @return 菜单信息列表
     */
    private List<MenuInfo> parseControllerMethods(Class<?> controllerClass) {
        List<MenuInfo> menuInfos = new ArrayList<>();
        Method[] methods = controllerClass.getDeclaredMethods();
        for (Method method : methods) {
            if (Modifier.isPublic(method.getModifiers())) {
                PermissionName permissionName = method.getAnnotation(PermissionName.class);
                PreAuthorize preAuthorize = method.getAnnotation(PreAuthorize.class);

                if (permissionName != null && preAuthorize != null) {
                    String name = permissionName.name(); // 从PermissionName注解获取菜单名称
                    int sort = permissionName.sort(); // 从PermissionName注解获取排序值
                    String permission = extractPermission(preAuthorize); // 从PreAuthorize注解提取权限标识

                    if (name != null && !name.trim().isEmpty() &&
                            permission != null && !permission.trim().isEmpty()) {
                        MenuInfo info = new MenuInfo();
                        info.setMenuName(name);
                        info.setPermission(permission);
                        info.setSort(sort);
                        menuInfos.add(info);
                    }
                }
            }
        }
        return menuInfos;
    }

    /**
     * 从PreAuthorize注解中提取权限标识
     * <p>
     * 支持两种格式：
     * 1. @PreAuthorize("@ss.hasPermission('permission:code')")
     * 2. @PreAuthorize("@ss.hasAnyPermissions('permission:code1','permission:code2',...)")
     * <p>
     * 对于第二种格式，会检查当前权限是否在列表中，如果在则返回第一个权限码
     *
     * @param preAuthorize PreAuthorize注解
     * @return 提取的权限标识，如果无法提取则返回null
     */
    private String extractPermission(PreAuthorize preAuthorize) {
        String value = preAuthorize.value();

        // 尝试匹配 @ss.hasPermission('permission:code') 格式
        Matcher matcher = PERMISSION_PATTERN.matcher(value);

        List<String> permissions = new LinkedList<>();
        while (matcher.find()) {
            if (matcher.group(1).contains(getCurrentPermission())) {
                permissions.add(matcher.group(1));
            }
        }
        return permissions.stream().findFirst().orElse(null);
    }

    // 获取父菜单 ID
    private Long getParentId(String parentMenuName) {
        String sql = "SELECT id FROM " + tableName + " WHERE name = ? AND deleted = 0";
        try {
            return jdbcTemplate.queryForObject(sql, Long.class, parentMenuName);
        } catch (Exception e) {
            return null;
        }
    }

    // 查询当前菜单是否存在
    private Long getMenuId(String menuName) {
        String sql = "SELECT id FROM " + tableName + " WHERE name = ? AND deleted = 0";
        try {
            return jdbcTemplate.queryForObject(sql, Long.class, menuName);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 插入菜单
     *
     * @param parentId  父菜单ID
     * @param menuName  菜单名称
     * @param sortIndex 排序索引
     */
    private void insertMenu(Long parentId, String menuName, int sortIndex) {
        String sql = "INSERT INTO " + tableName +
                "(name, type, sort, parent_id, path, icon, component, component_name, status, visible, keep_alive, always_show, creator) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        // 生成路径：将菜单名称转换为路径格式
        // 这里可以根据实际需求生成合适的路径
        String path = generatePathFromMenuName(menuName);

        jdbcTemplate.update(sql,
                menuName,
                MenuTypeEnum.MENU.getType(),
                sortIndex,
                parentId,
                path,
                "#",  // 默认图标
                "",   // 组件路径，对于目录菜单可为空
                "",   // 组件名称，对于目录菜单可为空
                CommonStatusEnum.ENABLE.getStatus(),
                true, // 可见
                true, // 保持活跃
                true, // 总是显示
                "system"); // 创建者
    }

    /**
     * 从菜单名称生成路径
     *
     * @param menuName 菜单名称
     * @return 生成的路径
     */
    private String generatePathFromMenuName(String menuName) {
        // 这里可以实现自定义的路径生成逻辑
        // 例如：将中文转为拼音、去除特殊字符等
        // 简单实现：返回空字符串，实际应用中可根据需求扩展
        return "";
    }

    /**
     * 内部类：菜单信息结构体
     * <p>
     * 用于存储从Controller方法中提取的菜单信息
     */
    @Data
    protected static class MenuInfo {
        private Long id;
        private String menuName;    // 菜单名称
        private String permission;  // 权限标识
        private int sort;           // 排序值
//
    }

    // 内部类：JdbcTemplate 简化数据库操作
    protected static class JdbcTemplate {
        private final Connection connection;

        public JdbcTemplate(Connection connection) {
            this.connection = Objects.requireNonNull(connection);
        }

        public Long queryForObject(String sql, Class<Long> requiredType, Object... args) {
            try (var stmt = connection.prepareStatement(sql)) {
                for (int i = 0; i < args.length; i++) {
                    stmt.setObject(i + 1, args[i]);
                }
                try (var rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getObject(1, requiredType);
                    }
                }
                return null;
            } catch (Exception e) {
                throw new RuntimeException("执行 SQL 出错: " + sql, e);
            }
        }

        public int update(String sql, Object... args) {
            try (var stmt = connection.prepareStatement(sql)) {
                for (int i = 0; i < args.length; i++) {
                    stmt.setObject(i + 1, args[i]);
                }
                return stmt.executeUpdate();
            } catch (Exception e) {
                throw new RuntimeException("执行 SQL 出错: " + sql, e);
            }
        }

        public List<MenuInfo> queryForList(String sql, Object... args) {
            try (var stmt = connection.prepareStatement(sql)) {
                for (int i = 0; i < args.length; i++) {
                    stmt.setObject(i + 1, args[i]);
                }
                try (var rs = stmt.executeQuery()) {
                    List<MenuInfo> menuInfos = new ArrayList<>();
                    while (rs.next()) {
                        MenuInfo menuInfo = new MenuInfo();
                        menuInfo.setId(rs.getLong("id"));
                        menuInfo.setMenuName(rs.getString("name"));
                        menuInfo.setPermission(rs.getString("permission"));
                        menuInfo.setSort(rs.getInt("sort"));
                        menuInfos.add(menuInfo);
                    }
                    return menuInfos;
                }
            } catch (Exception e) {
                throw new RuntimeException("执行 SQL 出错: " + sql, e);
            }
        }
    }
}
