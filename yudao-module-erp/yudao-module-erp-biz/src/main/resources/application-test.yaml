--- #################### 注册中心 + 配置中心相关配置 ####################
spring:
  cloud:
    nacos:
      server-addr: 192.168.2.48:8848 # Nacos 服务器地址
      username: nacos # Nacos 账号
      password: nacos # Nacos 密码
      discovery: # 【配置中心】配置项
        namespace: ${spring.profiles.active} # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        metadata:
          version: 1.0.0 # 服务实例的版本号，可用于灰度发布
          tag: ${spring.cloud.nacos.server.instance.tag:${spring.profiles.active}}
      config: # 【注册中心】配置项
        namespace: ${spring.cloud.nacos.namespace} # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP

--- #################### 数据库相关配置 ####################
