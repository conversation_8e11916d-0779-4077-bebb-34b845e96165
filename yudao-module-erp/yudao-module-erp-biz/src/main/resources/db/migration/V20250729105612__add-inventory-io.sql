DROP TABLE IF EXISTS `erp_inventory_bill`;
# 通用库存单据
CREATE TABLE erp_inventory_bill
(
    id                       bigint AUTO_INCREMENT COMMENT 'ID' PRIMARY KEY,
    code                     varchar(50)                           NOT NULL UNIQUE COMMENT '单据号',
    direction                varchar(3)                            NOT NULL CHECK (direction IN ('in', 'out')) COMMENT 'in入库 out出库',
    document_type            varchar(30)                           NOT NULL COMMENT '单据类型',
    business_type            varchar(30)                           NOT NULL COMMENT '业务类型',
    document_status          int                                   NOT NULL DEFAULT 0 COMMENT '0编制中 1审核通过 2回退',
    business_status          int                                   not null comment '业务状态：0关闭 1正常 2挂起',
    document_date            date                                  NOT NULL COMMENT '单据日期',
    warehouse_id             bigint                                NOT NULL COMMENT '仓库ID',
    warehouse_name           varchar(100)                          NOT NULL COMMENT '仓库名称',
    supplier_id              bigint                                NULL COMMENT '供应商ID',
    supplier_name            varchar(100)                          NULL COMMENT '供应商名称',
    customer_id              bigint                                NULL COMMENT '客户ID',
    customer_name            varchar(100)                          NULL COMMENT '客户名称',
    business_department_id   bigint                                NOT NULL COMMENT '业务部门ID',
    business_department_name varchar(100)                          NOT NULL COMMENT '业务部门名称',
    business_user_id         bigint                                NOT NULL COMMENT '业务员ID',
    business_user_name       varchar(50)                           NOT NULL COMMENT '业务员姓名',
    source_type              varchar(30)                           NULL COMMENT '来源单据类型',
    source_id                bigint                                NULL COMMENT '来源单据ID',
    source_code              varchar(50)                           NULL COMMENT '来源单据号',
    material_type            varchar(30)                           NULL COMMENT '物料类型:采购单的物料类型（业务字典）',
    remark                   varchar(400)                          NULL COMMENT '备注',
    audit_time               datetime                              NULL COMMENT '审核时间',
    creator                  varchar(64) DEFAULT ''                NULL COMMENT '创建者',
    create_time              datetime    DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updater                  varchar(64) DEFAULT ''                NULL COMMENT '更新者',
    update_time              datetime    DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted                  bit         DEFAULT b'0'              NOT NULL COMMENT '逻辑删除',
    tenant_id                bigint      DEFAULT 0                 NOT NULL COMMENT '租户编号'
) COMMENT = '出入库清单' COLLATE utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `erp_inventory_bill_item`;
-- 通用库存单据明细
CREATE TABLE erp_inventory_bill_item
(
    id                      bigint AUTO_INCREMENT COMMENT 'ID' PRIMARY KEY,
    bill_id                 bigint         NOT NULL COMMENT '出入库单ID',
    material_id             bigint         NOT NULL COMMENT '物料ID',
    material_code           varchar(100)   NOT NULL COMMENT '物料编码',
    material_name           varchar(100)   NOT NULL COMMENT '物料名称',
    material_spec           varchar(300)   NULL COMMENT '规格型号',
    material_uom            varchar(64)    NOT NULL COMMENT '计量单位',
    in_quantity             decimal(65, 6) NULL COMMENT '入库数量',
    out_quantity            decimal(65, 6) NULL COMMENT '出库数量',
    batch_number            varchar(100)   NULL COMMENT '批次号',
    unique_code             varchar(100)   NULL COMMENT '唯一码',
    warehouse_position_id   bigint         null comment '库位ID',
    warehouse_position_name varchar(100)   null comment '库位名称',
#     //来源信息，冗余一下，这些都是不会变的
    source_type             varchar(30)    NOT NULL COMMENT '来源单据类型',
    source_id               bigint         NOT NULL COMMENT '来源单据ID',
    source_code             varchar(50)    NOT NULL COMMENT '来源单据code',
    source_item_id          bigint         NOT NULL COMMENT '来源单据ItemID',
#     生产入库
    packaging_type_id       bigint         NULL COMMENT '包装类型id',
    packaging_type_name     varchar(50)    NULL COMMENT '包装类型名称',
    packaging_spec_id       bigint         NULL COMMENT '包装规格id',
    packaging_spec_name     varchar(50)    NULL COMMENT '包装规格名称',
    packaging_company_id    bigint         NULL COMMENT '包装物所属公司id，从customer中获取',
    packaging_company_name  varchar(50)    NULL COMMENT '包装物所属公司名称',
    packaging_owner         varchar(100)   NULL COMMENT '包装物归属',
    packaging_code          varchar(100)   NULL COMMENT '包装码',
    plate_number            varchar(100)   NULL COMMENT '挂牌车号',
    gross_weight            decimal(65, 6) NULL default 0 COMMENT '毛重',
    tare_weight             decimal(65, 6) NULL default 0 COMMENT '空重',
    filling_date            date           NULL COMMENT '充装日期',
    filling_user_id         bigint         NULL COMMENT '充装人id',
    filling_user_name       varchar(50)    NULL COMMENT '充装人姓名',
    retuned_quantity        decimal(65, 6) NULL COMMENT '已退货数量',
    returnable_quantity     decimal(65, 6) NULL COMMENT '可退货数量：入库数量-已退货数量-已使用数量',
#     //已使用数量
    used_quantity           decimal(65, 6) NULL COMMENT '已使用数量:领料出库单审核通过时代表已使用',

    remark                  varchar(500)   NULL COMMENT '备注',
    tenant_id               bigint              DEFAULT 0 NOT NULL COMMENT '租户编号'
) COMMENT = '通用库存单据明细' COLLATE utf8mb4_unicode_ci;