CREATE TABLE `erp_requisition_order`
(
    `id`                       BIGINT       NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `code`                     VARCHAR(50)  NOT NULL COMMENT '领料单号',
    `purpose`                  VARCHAR(50)  NULL COMMENT '领料用途',
    `requisition_date`         DATE         NOT NULL COMMENT '领料日期',
    `warehouse_id`             BIGINT       NOT NULL COMMENT '仓库ID',
    `warehouse_name`           VARCHAR(50)  NOT NULL COMMENT '仓库名称',
    `business_department_id`   BIGINT       NOT NULL COMMENT '业务部门ID',
    `business_department_name` VARCHAR(50)  NOT NULL COMMENT '业务部门名称',
    `business_user_id`         BIGINT       NOT NULL COMMENT '业务员ID',
    `business_user_name`       VARCHAR(50)  NOT NULL COMMENT '业务员姓名',
    `document_status`          INTEGER      NOT NULL COMMENT '单据状态（0=编制中，1=审核通过，2=回退）',
    `business_status`          INTEGER      NOT NULL COMMENT '业务状态（0=关闭，1=正常，2=挂起）',
    `business_type`            VARCHAR(50)  NOT NULL COMMENT '业务类型',
    `remark`                   VARCHAR(200) NULL COMMENT '备注',
    `audit_time`               DATETIME     NULL COMMENT '审核时间',
    `creator`                  VARCHAR(64)           DEFAULT '' COMMENT '创建者',
    `create_time`              DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`                  VARCHAR(64)           DEFAULT '' COMMENT '更新者',
    `update_time`              DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`                  BIT(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`                BIGINT       NOT NULL DEFAULT 0 COMMENT '租户编号',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '领料单主表';


CREATE TABLE `erp_requisition_order_item`
(
    `id`                   BIGINT         NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `requisition_order_id` BIGINT         NOT NULL COMMENT '领料单ID',
    `material_id`          BIGINT         NOT NULL COMMENT '物料ID',
    `material_code`        VARCHAR(50)    NOT NULL COMMENT '物料编码',
    `material_name`        VARCHAR(50)    NOT NULL COMMENT '物料名称',
    `material_spec`        VARCHAR(50)    NULL COMMENT '规格型号',
    `material_uom`         VARCHAR(50)    NOT NULL COMMENT '主计量单位',
    `available_quantity`   DECIMAL(65, 6) NOT NULL DEFAULT 0 COMMENT '可用库存',
    `request_quantity`     DECIMAL(65, 6) NOT NULL COMMENT '领用数量',
    `remark`               VARCHAR(200)   NULL COMMENT '备注',
    `deleted`              BIT(1)         NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`            BIGINT         NOT NULL DEFAULT 0 COMMENT '租户编号',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '领料单明细表';