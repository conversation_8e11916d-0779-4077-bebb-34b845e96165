ALTER TABLE `erp_sales_outbound_shipment`
    ADD COLUMN `sales_order_code` VARCHAR(50) NOT NULL COMMENT '销售订单Code' AFTER `sales_order_id`,
    CHANGE COLUMN `customer_order_code` `sales_customer_order_code` VARCHAR(50) NOT NULL COMMENT '销售订单中的客户订单号';

ALTER TABLE `erp_sales_outbound_item`
    CHANGE COLUMN `customer_order_id` `sales_order_id` BIGINT NOT NULL COMMENT '销售订单Id',
    CHANGE COLUMN `customer_order_code` `sales_order_code` BIGINT NOT NULL COMMENT '销售订单Code',
    ADD COLUMN `sales_customer_order_code` VARCHAR(50) NOT NULL COMMENT '销售订单中的客户订单号' AFTER `sales_order_id`;



