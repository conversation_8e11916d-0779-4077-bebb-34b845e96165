ALTER TABLE `erp_shipping_line`
    MODIFY COLUMN shipped_quantity DECIMAL(65, 6) DEFAULT 0 NOT NULL COMMENT '已发货数量';
ALTER TABLE `erp_shipping_line`
    MODIFY COLUMN unshipped_quantity DECIMAL(65, 6) DEFAULT 0 NOT NULL COMMENT '未发货数量';
ALTER TABLE `erp_shipping_line`
    MODIFY COLUMN returned_quantity DECIMAL(65, 6) DEFAULT 0 NOT NULL COMMENT '已退货数量';
ALTER TABLE `erp_shipping_line`
    MODIFY COLUMN unreturned_quantity DECIMAL(65, 6) DEFAULT 0 NOT NULL COMMENT '未退货数量';


ALTER TABLE `erp_sales_outbound_item`
    MODIFY COLUMN returned_quantity DECIMAL(65, 6) DEFAULT 0 NOT NULL COMMENT '已退货数量';
ALTER TABLE `erp_sales_outbound_item`
    MODIFY COLUMN unreturned_quantity DECIMAL(65, 6) DEFAULT 0 NOT NULL COMMENT '未退货数量';