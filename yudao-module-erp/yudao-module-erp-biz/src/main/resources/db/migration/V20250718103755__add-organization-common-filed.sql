ALTER TABLE `erp_organization`
    ADD COLUMN `country_type` VARCHAR(100) NOT NULL COMMENT '国别';


ALTER TABLE `erp_supplier`
    ADD COLUMN `country_type` VARCHAR(100) NOT NULL COMMENT '国别' AFTER `settlement_currency`;

ALTER TABLE `erp_carrier`
    ADD COLUMN `country_type`        VARCHAR(100) NOT NULL COMMENT '国别',
    ADD COLUMN `settlement_currency` VARCHAR(100) NOT NULL COMMENT '结算货币' AFTER `country_type`;