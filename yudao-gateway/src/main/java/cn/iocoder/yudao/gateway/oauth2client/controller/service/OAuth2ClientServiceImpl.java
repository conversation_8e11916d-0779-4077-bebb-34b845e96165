package cn.iocoder.yudao.gateway.oauth2client.controller.service;

import cn.iocoder.yudao.gateway.oauth2client.config.OAuth2Properties;
import cn.iocoder.yudao.gateway.oauth2client.dto.OAuth2AccessTokenRespDTO;
import cn.iocoder.yudao.gateway.oauth2client.dto.OAuth2LoginUserSimpleDTO;
import cn.iocoder.yudao.gateway.oauth2client.dto.WebClientMonoResponseWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

/**
 * OAuth 2.0 客户端
 * <p>
 * 对应调用 OAuth2OpenController 接口
 */
@Service
@Slf4j
public class OAuth2ClientServiceImpl implements OAuth2ClientService {

    @Resource
    private OAuth2Properties oAuth2Properties;
    private final String GET_USER_PATH = "/user/get-login-user";
    private final String TOKEN_PATH = "/token";

    private final String AUTHORIZATION_HEADER = "Authorization";
    private final String BEARER_AUTH_PREFIX = "Bearer ";
    private final String BASIC_AUTH_PREFIX = "Basic ";
    private final String authorizationCode = "authorization_code";
    private final String GRANT_TYPE = "grant_type";
    private final String CODE = "code";
    private final String REDIRECT_URI = "redirect_uri";
    private final String TENANT_ID = "tenant-id";
    private final String ACCESS_TOKEN = "access_token";
    ;

    private Mono<OAuth2AccessTokenRespDTO> postAccessToken(String clientId, String code, String redirectUri, String tag) {
        if (code == null || code.isEmpty()) {
            throw new IllegalArgumentException("授权码不能为空");
        }
        if (redirectUri == null || redirectUri.isEmpty()) {
            throw new IllegalArgumentException("重定向 URI 不能为空");
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        addClientHeader(clientId, headers);
        //FIXME: 临时解决租户的问题,按道理来说这个租户Id应该通过code查询数据库，取code的租户Id
        headers.add(TENANT_ID, "1");
        headers.add("Tag", tag);

        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add(GRANT_TYPE, authorizationCode);
        body.add(CODE, code);
        body.add(REDIRECT_URI, redirectUri);

        String url = oAuth2Properties.getBaseUrl() + TOKEN_PATH;
        return executeRequest(url, HttpMethod.POST, headers, body, new ParameterizedTypeReference<WebClientMonoResponseWrapper<OAuth2AccessTokenRespDTO>>() {
        });

    }

    private <T> Mono<T> executeRequest(String url, HttpMethod method, HttpHeaders headers, MultiValueMap<String, String> body, ParameterizedTypeReference<WebClientMonoResponseWrapper<T>> typeRef) {
        // 构建 URI，对于 GET 请求，将参数附加到 URL 后面
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(url);
        if (HttpMethod.GET.equals(method) && null != body && !body.isEmpty()) {
            uriBuilder.queryParams(body);
        }

        WebClient webClient = WebClient.builder().build();

        return webClient.method(method)
                .uri(uriBuilder.build(true).toUriString())
                .headers(httpHeaders -> httpHeaders.addAll(headers))
                .body(BodyInserters.fromFormData(Objects.requireNonNull(body)))
                .retrieve()
                .bodyToMono(typeRef)
                .flatMap(apiResponse -> {
                    if (apiResponse.getCode() != 0) {
                        log.error("请求失败: code={}, msg={}", apiResponse.getCode(), apiResponse.getMsg());
                        return Mono.error(new RuntimeException("请求失败: " + apiResponse.getMsg()));
                    }
                    return Mono.just(apiResponse.getData());
                });
    }

    private void addClientHeader(String clientId, HttpHeaders headers) {
        if (clientId == null || clientId.isEmpty()) {
            throw new IllegalArgumentException("客户端ID不能为空");
        }
        String clientSecret = oAuth2Properties.getClients().get(clientId).getClientSecret();

        String client = clientId + ":" + clientSecret;
        client = Base64.getEncoder().encodeToString(client.getBytes(StandardCharsets.UTF_8));
        headers.add(AUTHORIZATION_HEADER, BASIC_AUTH_PREFIX + client);
    }

    public Mono<OAuth2LoginUserSimpleDTO> getUser(String clientId, String code, String redirectUri, String tag) {
        return this.postAccessToken(clientId, code, redirectUri, tag).flatMap(accessTokenRespDTO -> {
            String accessToken = accessTokenRespDTO.getAccessToken();
            return getLongUser(accessToken, tag);
        });
    }

    public Mono<OAuth2LoginUserSimpleDTO> getLongUser(String accessToken, String tag) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add(AUTHORIZATION_HEADER, BEARER_AUTH_PREFIX + accessToken);
        headers.add(TENANT_ID, "1");
        headers.add("Tag", tag);

        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.set(ACCESS_TOKEN, accessToken);

        String url = oAuth2Properties.getBaseUrl() + GET_USER_PATH;
        Mono<OAuth2LoginUserSimpleDTO> oAuth2LoginUserRespDTOMono = executeRequest(url, HttpMethod.GET, headers, body, new ParameterizedTypeReference<WebClientMonoResponseWrapper<OAuth2LoginUserSimpleDTO>>() {
        });

        return oAuth2LoginUserRespDTOMono.flatMap(Mono::just);

    }
}