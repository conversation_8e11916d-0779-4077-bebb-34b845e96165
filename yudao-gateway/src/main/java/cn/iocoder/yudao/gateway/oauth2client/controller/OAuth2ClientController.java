package cn.iocoder.yudao.gateway.oauth2client.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.signers.JWTSigner;
import cn.hutool.jwt.signers.JWTSignerUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.gateway.oauth2client.controller.service.OAuth2ClientService;
import cn.iocoder.yudao.gateway.oauth2client.dto.OAuth2LoginUserSimpleDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Date;

@Tag(name = "oauth2 client")
@RestController
@RequestMapping("/admin-api")
public class OAuth2ClientController {
    private final String JWT = "jwt";

    @Value("${jwt-256-secret}")
    private String JWT_256_SECRET;

    private Long JWT_EXPIRE_TIME = 60 * 5 * 1000L;

    @Resource
    private OAuth2ClientService oAuth2ClientService;

    @Operation(summary = "根据code获取用户基本信息")
    @GetMapping("/oauth2/get-user")
    @Parameters({
            @Parameter(name = "clientId", required = true, description = "应用Id", example = "code"),
            @Parameter(name = "code", required = true, description = "授权code", example = "userinfo.read"),
            @Parameter(name = "redirectUri", required = true, description = "重定向 URI", example = "https://www.iocoder.cn"),
    })
    public Mono<CommonResult<String>> getUser(@RequestParam String clientId, @RequestParam String code, @RequestParam String redirectUri, @RequestHeader("Tag") String tag) {
        return oAuth2ClientService.getUser(clientId, code, redirectUri, tag).flatMap(oAuth2LoginUserRespDTO -> {
            String jwtString = generateJWT(oAuth2LoginUserRespDTO);
            return Mono.just(CommonResult.success(jwtString));
        });
    }


    @Operation(summary = "跳转到第三方应用，完成登录")
    @Parameters({
            @Parameter(name = "accessToken", required = true, description = "accessToken", example = "xxxxxxx"),
            @Parameter(name = "refreshToken", required = true, description = "refreshToken", example = "yyyyy'"),
            @Parameter(name = "redirectUri", required = true, description = "重定向 URI", example = "https://www.iocoder.cn"),
    })
    @GetMapping("/oauth2/goto-client")
    public Mono<CommonResult<String>> gotoClient(@RequestParam String accessToken, @RequestParam String refreshToken, @RequestParam String redirectUri, ServerWebExchange exchange, @RequestHeader("Tag") String tag) {
        return oAuth2ClientService.getLongUser(accessToken, tag).flatMap(oAuth2LoginUserRespDTO -> {

            String jwtString = generateJWT(oAuth2LoginUserRespDTO);

            URI url = UriComponentsBuilder.fromUriString(redirectUri)
                    .queryParam(JWT, jwtString)
                    .build()
                    .toUri();
            return Mono.just(CommonResult.success(url.toString()));
        });

    }

    /**
     * @return
     */
    private String generateJWT(OAuth2LoginUserSimpleDTO oAuth2LoginUserSimpleVO) {
        JSONObject jsonObject = new JSONObject(oAuth2LoginUserSimpleVO);

        JWTSigner signer = JWTSignerUtil.hs256(JWT_256_SECRET.getBytes(StandardCharsets.UTF_8));
        JWT jwt = cn.hutool.jwt.JWT.create().setSigner(signer)
                .addPayloads(jsonObject)
                .setIssuedAt(new Date())
                .setExpiresAt(new Date(System.currentTimeMillis() + JWT_EXPIRE_TIME));

        return jwt.sign();
    }
}
