-- 客户档案

DROP TABLE IF EXISTS `erp_customer`;
CREATE TABLE `erp_customer`
(
    `id`                     bigint      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `code`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '编号',
    `name`                   varchar(50) NOT NULL COMMENT '名称',
    `settlement_currency`    varchar(50) NOT NULL COMMENT '结算货币',
    `short_name`             varchar(50) NULL COMMENT '客户简称',
    `category`               varchar(50) NULL COMMENT '客户类别',
    `area`                   varchar(200) NULL COMMENT '地区',
    `address`                varchar(200) NULL COMMENT '详细地址',
    `contact`                varchar(50) NULL COMMENT '联系人',
    `contact_phone`          varchar(50) NULL COMMENT '联系电话',
    `business_contact`       varchar(50) NULL COMMENT '业务联系人',
    `business_contact_phone` varchar(11) NULL COMMENT '业务联系电话',
    `business_contact_email` varchar(50) NULL COMMENT '业务联系人邮箱',
    `finance_contact`        varchar(50) NULL COMMENT '财务联系人',
    `finance_contact_phone`  varchar(11) NULL COMMENT '财务联系人电话',
    `finance_contact_email`  varchar(50) NULL COMMENT '财务联系人邮箱',
    `status`                 int  NOT NULL DEFAULT 0 COMMENT '状态（停用/启用）',
    `creator`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time`            datetime    NOT NULL                                         DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time`            datetime    NOT NULL                                         DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`                bit(1)      NOT NULL                                         DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`              bigint      NOT NULL                                         DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户基本信息';


-- #### 开票信息
DROP TABLE IF EXISTS `erp_customer_invoice_info`;

CREATE TABLE `erp_customer_invoice_info`
(
    `id`           bigint   NOT NULL AUTO_INCREMENT COMMENT 'id',
    `customer_id`  bigint   NOT NULL COMMENT '客户id',
    `bank_account` varchar(50) NULL COMMENT '开票信息-账号',
    `bank_name`    varchar(50) NULL COMMENT '开票信息-开户行',
    `uscc`         varchar(50) NULL COMMENT '开票信息-统一社会信用代码',
    `address`      varchar(200) NULL COMMENT '开票信息-地址',
    `creator`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time`  datetime NOT NULL                                            DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time`  datetime NOT NULL                                            DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`                bit(1)      NOT NULL                                         DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`              bigint      NOT NULL                                         DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
)ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户信息-开票信息';


-- #### 收货地址
DROP TABLE IF EXISTS `erp_customer_receive_address`;
CREATE TABLE `erp_customer_receive_address`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    `customer_id`   bigint       NOT NULL COMMENT '客户id',
    `area`          varchar(200) NOT NULL COMMENT '收货地址-地区',
    `address`       varchar(200) NOT NULL COMMENT '收货地址-详细地址',
    `contact`       varchar(50) NULL COMMENT '收货地址-联系人',
    `contact_phone` varchar(11) NULL COMMENT '收货地址-联系电话',
    `creator`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time`   datetime     NOT NULL                                        DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time`   datetime     NOT NULL                                        DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`                bit(1)      NOT NULL                                         DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`              bigint      NOT NULL                                         DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
)ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户信息-收货地址';

