-- 创建临时表来存储查询结果
CREATE TEMPORARY TABLE temp_dept_tree AS
WITH RECURSIVE dept_tree AS (
    -- 选择根节点
    SELECT id, parent_id, name
    FROM system_dept
    WHERE name = '芋道源码'

    UNION ALL

    -- 递归选择子节点
    SELECT d.id, d.parent_id, d.name
    FROM system_dept d
             INNER JOIN dept_tree dt ON d.parent_id = dt.id
)
SELECT id FROM dept_tree;

-- 根据临时表中的数据删除 system_dept 表中的记录
DELETE FROM system_dept
WHERE id IN (SELECT id FROM temp_dept_tree);

-- 删除临时表（可选）
DROP TEMPORARY TABLE temp_dept_tree;
