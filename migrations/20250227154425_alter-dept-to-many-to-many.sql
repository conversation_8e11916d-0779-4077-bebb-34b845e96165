-- ----------------------------
-- Table structure for system_user_dept
-- ----------------------------
DROP TABLE IF EXISTS `system_user_dept`;
CREATE TABLE `system_user_dept`  (
                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户ID',
                                     `dept_id` bigint NOT NULL DEFAULT 0 COMMENT '部门ID',
                                     `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
                                     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
                                     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     `deleted` bit(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
                                     `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 126 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户部门表';

-- ----------------------------
-- system_user 增加一个新字段 `dept_ids`
-- 参考 `post_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '岗位编号数组', after dept_id
-- ----------------------------

ALTER TABLE `system_users`
    ADD COLUMN `dept_ids` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '部门编号数组' AFTER `dept_id`;

-- ----------------------------
-- 原有的`dept_id`字段中的数据转换成 [1] 的字符串，更新至`dept_ids`字段，判断一下是不是null

-- ----------------------------
UPDATE `system_users` SET `dept_ids` = CONCAT('[', `dept_id`, ']') WHERE `dept_id` IS NOT NULL;
-- ----------------------------
-- 原有的`dept_id`字段中的数据生成 `system_user_dept` 的数据
-- ----------------------------
INSERT INTO `system_user_dept`(`user_id`, `dept_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) SELECT `id`, `dept_id`, 'system', NOW(), 'system', NOW(), 0, 0 FROM `system_users` WHERE `dept_id` IS NOT NULL;

-- ----------------------------
-- 删除`dept_id`字段
-- ----------------------------
ALTER TABLE `system_users`
    DROP COLUMN `dept_id`;


-- ----------------------------
-- 在 `system_dept` 表中增加一个新字段 `external_id`
-- 参考 `external_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '外部编号', after `id`
-- ----------------------------
ALTER TABLE `system_dept`
    ADD COLUMN `external_id` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '外部系统ID：例如：钉钉中的组织机构ID' AFTER `tenant_id`;