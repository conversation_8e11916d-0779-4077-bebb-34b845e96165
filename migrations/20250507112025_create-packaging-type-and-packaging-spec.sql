DROP TABLE IF EXISTS `erp_packaging_type`;
CREATE TABLE `erp_packaging_type`
(
    `id`          BIGINT                                                        NOT NULL AUTO_INCREMENT COMMENT 'id',
    `code`        VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '包装类型编号',
    `name`        VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '包装类型名称',
    `category`    VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '包装类型类别',
    `sort`        INT UNSIGNED                                                  NOT NULL DEFAULT 0 COMMENT '排序',
    `status`      INT                                                           NOT NULL DEFAULT 0 COMMENT '状态（停用/启用）',
    `creator`     VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT '' COMMENT '创建者',
    `create_time` DATETIME                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`     VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT '' COMMENT '更新者',
    `update_time` DATETIME                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`     BIT(1)                                                        NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`   BIGINT                                                        NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='包装类型表';

DROP TABLE IF EXISTS `erp_packaging_spec`;
CREATE TABLE `erp_packaging_spec`
(
    `id`                BIGINT                                                        NOT NULL AUTO_INCREMENT COMMENT 'id',
    `packaging_type_id` BIGINT                                                        NOT NULL COMMENT '包装类型id',
    `code`              VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '包装规格编号',
    `name`              VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '包装规格名称',
    `uom`               VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '计量单位',
    `max_quantity`      DECIMAL(10, 2) UNSIGNED                                       NOT NULL DEFAULT 0.00 COMMENT '最大包装量',
    `sort`              INT UNSIGNED                                                  NOT NULL DEFAULT 0 COMMENT '排序',
    `status`            INT                                                           NOT NULL DEFAULT 0 COMMENT '状态（停用/启用）',
    `creator`           VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT '' COMMENT '创建者',
    `create_time`       DATETIME                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`           VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT '' COMMENT '更新者',
    `update_time`       DATETIME                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`           BIT(1)                                                        NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`         BIGINT                                                        NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='包装规格表';
